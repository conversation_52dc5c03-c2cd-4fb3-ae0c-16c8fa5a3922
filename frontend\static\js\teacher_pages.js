// Teacher Pages Module
(function(window){
    const SMS = window.SMS || (window.SMS = {});

// ==================== TIMETABLE PAGE ====================

SMS.loadTimetablePage = async function() {
    const contentArea = document.getElementById('contentArea');
    const today = new Date();
    const todayStr = today.toISOString().split('T')[0];
    
    contentArea.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-calendar-week"></i> Teacher Timetable (Read-only)</h2>
        </div>

        <!-- Date Selection -->
        <div class="card mb-4">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-calendar"></i> Select Date to View Timetable</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4">
                        <label class="form-label"><strong>Select Date:</strong></label>
                        <input type="date" class="form-control" id="timetableDate" value="${todayStr}" onchange="loadTimetableForDate(this.value)">
                    </div>
                    <div class="col-md-4">
                        <label class="form-label"><strong>Filter by Teacher:</strong></label>
                        <select class="form-select" id="teacherFilter" onchange="loadTimetableForDate(document.getElementById('timetableDate').value)">
                            <option value="">-- All Teachers --</option>
                        </select>
                    </div>
                    <div class="col-md-4 d-flex align-items-end">
                        <button class="btn btn-success w-100" onclick="compareFreePeriodsNextDays()">
                            <i class="fas fa-chart-line"></i> Compare Next 3 Days
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Timetable Display -->
        <div id="timetableContainer">
            <div class="text-center py-5">
                <div class="spinner-border"></div>
            </div>
        </div>
        
        <!-- Free Periods Comparison -->
        <div id="freePeriodsComparison" style="display: none;" class="card mt-4">
            <div class="card-header bg-success text-white">
                <h5 class="mb-0"><i class="fas fa-chart-bar"></i> Free Periods - Next 3 Days</h5>
            </div>
            <div class="card-body" id="freePeriodsContainer"></div>
        </div>
    `;
    
    await loadTeacherFilter();
    await loadTimetableForDate(todayStr);
}

async function loadTeacherFilter() {
    try {
        const response = await fetch(SMS.apiUrl('/staff'), {
            headers: { 'Authorization': `Bearer ${SMS.authToken}` }
        });
        
        if (response.ok) {
            const raw = await response.json();
            // Deduplicate teachers by user_id
            const seen = new Set();
            const teachers = raw.filter(t => {
                if (seen.has(t.user_id)) return false;
                seen.add(t.user_id);
                return true;
            });
            const select = document.getElementById('teacherFilter');
            if (select) {
                teachers.forEach(t => {
                    const option = document.createElement('option');
                    option.value = t.user_id;
                    option.textContent = t.full_name;
                    select.appendChild(option);
                });
            }
        }
    } catch (error) {
        console.error('Error loading teachers:', error);
    }
}

async function loadTimetableForDate(dateStr) {
    const container = document.getElementById('timetableContainer');
    const teacherFilter = document.getElementById('teacherFilter')?.value;
    
    const date = new Date(dateStr);
    const dayOfWeek = date.getDay() === 0 ? 7 : date.getDay();
    const displayDate = date.toLocaleDateString('en-US', {weekday: 'long', year: 'numeric', month: 'long', day: 'numeric'});
    
    try {
        const response = await fetch(SMS.apiUrl('/teachers/schedules'), {
            headers: { 'Authorization': `Bearer ${SMS.authToken}` }
        });
        
        if (response.ok) {
            const schedules = await response.json();
            console.log('Schedules loaded:', schedules);
            
            const filtered = teacherFilter ? schedules.filter(s => s.teacher_id == teacherFilter) : schedules;
            
            const timetableData = filtered.map(schedule => ({
                ...schedule,
                dayPeriods: (schedule.periods || []).filter(p => p.day_of_week === dayOfWeek).sort((a, b) => a.start_time.localeCompare(b.start_time))
            })).filter(s => s.dayPeriods.length > 0);
            
            displayTimetable(timetableData, displayDate);
        } else {
            container.innerHTML = `<div class="alert alert-warning">Unable to load timetable</div>`;
        }
    } catch (error) {
        console.error('Error loading timetable:', error);
        container.innerHTML = `<div class="alert alert-danger">Error loading timetable</div>`;
    }
}

function displayTimetable(timetableData, displayDate) {
    const container = document.getElementById('timetableContainer');
    
    if (timetableData.length === 0) {
        container.innerHTML = `<div class="alert alert-info">No periods scheduled for ${displayDate}</div>`;
        return;
    }
    
    container.innerHTML = `
        <div class="card">
            <div class="card-header bg-info text-white">
                <h5 class="mb-0">Timetable for ${displayDate} - ${timetableData.length} Teacher(s)</h5>
            </div>
            <div class="card-body">
                ${timetableData.map(schedule => `
                    <div class="card mb-3">
                        <div class="card-header">
                            <strong>${schedule.teacher_name}</strong> - ${schedule.dayPeriods.length} Period(s)
                        </div>
                        <div class="card-body">
                            <table class="table table-sm">
                                <thead>
                                    <tr>
                                        <th>Time</th>
                                        <th>Period</th>
                                        <th>Class</th>
                                        <th>Subject</th>
                                        <th></th>
                                    </tr>
                                </thead>
                                <tbody>
                                    ${schedule.dayPeriods.map(p => `
                                        <tr>
                                            <td>${p.start_time.substring(0,5)} - ${p.end_time.substring(0,5)}</td>
                                            <td>Period ${p.period_number}</td>
                                            <td>${p.class_name} - ${p.section}</td>
                                            <td>${p.subject_name || 'General'}</td>
                                            <td></td>
                                        </tr>
                                    `).join('')}
                                </tbody>
                            </table>
                        </div>
                    </div>
                `).join('')}
            </div>
        </div>
    `;
}

async function compareFreePeriodsNextDays() {
    const today = new Date();
    const dates = [];
    
    for (let i = 0; i < 3; i++) {
        const date = new Date(today);
        date.setDate(date.getDate() + i);
        dates.push({
            dayOfWeek: date.getDay() === 0 ? 7 : date.getDay(),
            displayName: i === 0 ? 'Today' : i === 1 ? 'Tomorrow' : 'Day After',
            date: date.toLocaleDateString('en-US', {month: 'short', day: 'numeric'})
        });
    }
    
    try {
        const response = await fetch(SMS.apiUrl('/teachers/schedules'), {
            headers: { 'Authorization': `Bearer ${SMS.authToken}` }
        });

        if (response.ok) {
            const schedules = await response.json();

            const analysis = schedules.map(schedule => ({
                teacher_name: schedule.teacher_name,
                freePeriods: dates.map(d => ({
                    day: d.displayName,
                    date: d.date,
                    busyPeriods: schedule.periods.filter(p => p.day_of_week === d.dayOfWeek).length,
                    freePeriods: 8 - schedule.periods.filter(p => p.day_of_week === d.dayOfWeek).length
                }))
            }));

            displayFreePeriodsComparison(analysis);
        }
    } catch (error) {
        console.error('Error:', error);
    }
}

function displayFreePeriodsComparison(analysis) {
    const container = document.getElementById('freePeriodsContainer');
    document.getElementById('freePeriodsComparison').style.display = 'block';
    
    container.innerHTML = `
        <table class="table table-bordered">
            <thead>
                <tr>
                    <th>Teacher</th>
                    ${analysis[0].freePeriods.map(fp => `<th class="text-center">${fp.day}<br><small>${fp.date}</small><br>Free / Busy</th>`).join('')}
                </tr>
            </thead>
            <tbody>
                ${analysis.map(t => `
                    <tr>
                        <td><strong>${t.teacher_name}</strong></td>
                        ${t.freePeriods.map(fp => `
                            <td class="text-center">
                                <span class="badge bg-success">${fp.freePeriods}</span> / 
                                <span class="badge bg-secondary">${fp.busyPeriods}</span>
                            </td>
                        `).join('')}
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}


// ==================== LEAVE QUOTA PAGE ====================

SMS.loadLeaveQuotaPage = async function() {
    const contentArea = document.getElementById('contentArea');
    const currentYear = new Date().getFullYear();
    
    contentArea.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-calendar-alt"></i> Teacher Leave Quota</h2>
        </div>

        <div class="card">
            <div class="card-header bg-primary text-white">
                <div class="d-flex justify-content-between align-items-center">
                    <h5 class="mb-0">Leave Quota Overview</h5>
                    <select class="form-select form-select-sm" id="leaveYear" onchange="loadLeaveQuotaData()" style="width: 150px;">
                        ${Array.from({length: 5}, (_, i) => {
                            const year = currentYear - i;
                            return `<option value="${year}" ${year === currentYear ? 'selected' : ''}>${year}</option>`;
                        }).join('')}
                    </select>
                </div>
            </div>
            <div class="card-body" id="leaveQuotaContainer">
                <div class="text-center py-5"><div class="spinner-border"></div></div>
            </div>
        </div>
    `;
    
    await loadLeaveQuotaData();
}

async function loadLeaveQuotaData() {
    const container = document.getElementById('leaveQuotaContainer');
    const year = document.getElementById('leaveYear').value;
    
    try {
        const response = await fetch(SMS.apiUrl(`/teachers/attendance/summary?year=${year}`), {
            headers: { 'Authorization': `Bearer ${SMS.authToken}` }
        });
        
        if (response.ok) {
            const summaries = await response.json();
            displayLeaveQuotaData(summaries, year);
        } else {
            container.innerHTML = `<div class="alert alert-warning">No data for ${year}</div>`;
        }
    } catch (error) {
        console.error('Error:', error);
        container.innerHTML = `<div class="alert alert-danger">Error loading data</div>`;
    }
}

function displayLeaveQuotaData(data, year) {
    const container = document.getElementById('leaveQuotaContainer');
    
    if (data.length === 0) {
        container.innerHTML = `<div class="alert alert-warning">No leave data for ${year}</div>`;
        return;
    }
    
    container.innerHTML = `
        <div class="alert alert-info">
            <strong>Note:</strong> 2 Half-Days = 1 Leave. Standard quota: 10 Casual + 15 Medical + 20 Earned = 45 Total Days
        </div>
        <table class="table table-hover">
            <thead class="table-light">
                <tr>
                    <th>Teacher</th>
                    <th class="text-center">Leave Taken</th>
                    <th class="text-center">Half Days</th>
                    <th class="text-center">Total Leave Count</th>
                    <th class="text-center">Remaining (of 45)</th>
                    <th class="text-center">Utilization %</th>
                </tr>
            </thead>
            <tbody>
                ${data.map(s => {
                    const remaining = 45 - s.leave_count;
                    const util = ((s.leave_count / 45) * 100).toFixed(1);
                    const badgeClass = util > 80 ? 'danger' : util > 50 ? 'warning' : 'success';
                    return `
                        <tr>
                            <td><strong>${s.teacher_name}</strong></td>
                            <td class="text-center"><span class="badge bg-warning">${s.total_leave}</span></td>
                            <td class="text-center"><span class="badge bg-secondary">${s.total_half_day}</span></td>
                            <td class="text-center"><strong>${s.leave_count.toFixed(1)}</strong></td>
                            <td class="text-center"><span class="badge bg-success fs-6">${remaining.toFixed(1)}</span></td>
                            <td class="text-center"><span class="badge bg-${badgeClass} fs-6">${util}%</span></td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        </table>
    `;
}

// ==================== BACKWARDS COMPATIBILITY ====================

window.loadTimetablePage = function(){ return SMS.loadTimetablePage(); };
window.loadLeaveQuotaPage = function(){ return SMS.loadLeaveQuotaPage(); };
window.loadTeacherFilter = loadTeacherFilter;
window.loadTimetableForDate = loadTimetableForDate;
window.compareFreePeriodsNextDays = compareFreePeriodsNextDays;
window.loadLeaveQuotaData = loadLeaveQuotaData;

})(window);
