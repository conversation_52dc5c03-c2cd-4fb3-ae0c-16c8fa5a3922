"""
School Management System - Main FastAPI Application
"""
from fastapi import <PERSON><PERSON><PERSON>, Request
from fastapi.middleware.cors import CORSMiddleware
from fastapi.middleware.trustedhost import TrustedHostMiddleware
from fastapi.responses import JSONResponse
from fastapi.staticfiles import StaticFiles
from fastapi.templating import Jinja2Templates
from config import get_settings
from database import test_connection
import logging
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('server_debug.log'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Get settings
settings = get_settings()

# Create FastAPI app
app = FastAPI(
    title=settings.APP_NAME,
    version=settings.APP_VERSION,
    description="Complete School Management System with bilingual support",
    docs_url="/api/docs",
    redoc_url="/api/redoc",
    openapi_url="/api/openapi.json",
    redirect_slashes=False  # Disable automatic redirects to preserve headers
)

# Request logging middleware
@app.middleware("http")
async def log_requests(request: Request, call_next):
    logger.info(f"[REQUEST] {request.method} {request.url.path}")
    logger.info(f"[HEADERS] Authorization: {request.headers.get('Authorization', 'None')[:50]}...")
    response = await call_next(request)
    logger.info(f"[RESPONSE] Status: {response.status_code}")
    return response

# CORS middleware
app.add_middleware(
    CORSMiddleware,
    allow_origins=settings.CORS_ORIGINS,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# Import routers - using new modular structure where available
from routes import (
    auth_routes, student_routes, attendance_routes, report_routes,
    class_routes, user_routes, subject_routes, staff_routes, holiday_routes,
    dashboard_routes, settings_routes
)

# Import new modular route files
try:
    from routes.fee_routes_new import router as fee_routes
except ImportError:
    from routes.fee_routes import router as fee_routes

try:
    from routes.exam_routes_new import router as exam_routes
except ImportError:
    from routes.exam_routes import router as exam_routes

try:
    from routes.teacher_routes_new import router as teacher_routes
except ImportError:
    from routes.teacher_routes import router as teacher_routes

try:
    from routes.timetable_routes_new import router as timetable_routes
except ImportError:
    from routes.timetable_routes import router as timetable_routes

# Include routers
app.include_router(auth_routes.router, prefix="/api/auth", tags=["Authentication"])
app.include_router(dashboard_routes.router, prefix="/api/dashboard", tags=["Dashboard"])
app.include_router(user_routes.router, prefix="/api/users", tags=["Users"])
app.include_router(class_routes.router, prefix="/api/classes", tags=["Classes"])
app.include_router(student_routes.router, prefix="/api/students", tags=["Students"])
app.include_router(attendance_routes.router, prefix="/api/attendance", tags=["Attendance"])
app.include_router(fee_routes, prefix="/api/fees", tags=["Fees"])
app.include_router(exam_routes, prefix="/api/exams", tags=["Exams"])
app.include_router(report_routes.router, prefix="/api/reports", tags=["Reports"])
app.include_router(subject_routes.router, prefix="/api/subjects", tags=["Subjects"])
app.include_router(staff_routes.router, prefix="/api/staff", tags=["Staff"])
app.include_router(holiday_routes.router, prefix="/api/holidays", tags=["Holidays"])
app.include_router(teacher_routes, prefix="/api/teachers", tags=["Teachers"])
app.include_router(timetable_routes, prefix="/api/timetable", tags=["Timetable"])
app.include_router(settings_routes.router, prefix="/api/settings", tags=["Settings"])

# Get base directory
BASE_DIR = os.path.dirname(os.path.abspath(__file__))
ROOT_DIR = os.path.dirname(BASE_DIR)

# Static files and templates - now in frontend folder
static_dir = os.path.join(ROOT_DIR, "frontend", "static")
templates_dir = os.path.join(ROOT_DIR, "frontend", "templates")
uploads_dir = os.path.join(ROOT_DIR, "uploads")

if not os.path.exists(static_dir):
    os.makedirs(static_dir)
if not os.path.exists(uploads_dir):
    os.makedirs(uploads_dir)

app.mount("/static", StaticFiles(directory=static_dir), name="static")
app.mount("/uploads", StaticFiles(directory=uploads_dir), name="uploads")

templates = Jinja2Templates(directory=templates_dir)


@app.on_event("startup")
async def startup_event():
    """Startup event handler"""
    logger.info(f"Starting {settings.APP_NAME} v{settings.APP_VERSION}")
    
    # Test database connection
    if test_connection():
        logger.info("Database connection successful")
    else:
        logger.error("Database connection failed!")


@app.on_event("shutdown")
async def shutdown_event():
    """Shutdown event handler"""
    logger.info("Shutting down application")


@app.get("/")
async def root(request: Request):
    """Root endpoint - renders dashboard"""
    return templates.TemplateResponse("index.html", {"request": request})


@app.get("/api/health")
async def health_check():
    """Health check endpoint"""
    db_status = test_connection()
    
    return {
        "status": "healthy" if db_status else "unhealthy",
        "app_name": settings.APP_NAME,
        "version": settings.APP_VERSION,
        "database": "connected" if db_status else "disconnected"
    }


@app.exception_handler(Exception)
async def global_exception_handler(request: Request, exc: Exception):
    """Global exception handler"""
    logger.error(f"Unhandled exception: {exc}", exc_info=True)
    
    return JSONResponse(
        status_code=500,
        content={
            "success": False,
            "error": "Internal server error",
            "details": str(exc) if settings.DEBUG else None
        }
    )


if __name__ == "__main__":
    import uvicorn
    uvicorn.run(
        "main:app",
        host="0.0.0.0",
        port=8000,
        reload=settings.DEBUG,
        log_level="info"
    )

