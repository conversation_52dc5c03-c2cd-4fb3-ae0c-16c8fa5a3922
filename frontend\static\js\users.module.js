// Users module (minimal stub)
(function () {
    const SMS = window.SMS = window.SMS || {};

    async function loadUsersPage() {
        try {
            const el = document.getElementById('page-content');
            if (el) el.innerHTML = '<h3>Users</h3><p>Loading...</p>';
            await loadUsers();
        } catch (e) {
            console.error('loadUsersPage error', e);
            if (typeof SMS.showError === 'function') SMS.showError('Failed to load users page');
        }
    }

    async function loadUsers() {
        try {
            const url = (typeof SMS.apiUrl === 'function') ? SMS.apiUrl('/users') : '/api/users';
            const resp = await fetch(url, { headers: { 'Authorization': `Bearer ${SMS.authToken}` } });
            if (!resp.ok) {
                if (typeof SMS.showError === 'function') SMS.showError('Could not fetch users');
                return [];
            }
            const data = await resp.json();
            return data;
        } catch (e) {
            console.error('loadUsers error', e);
            return [];
        }
    }

    // expose on SMS namespace
    SMS.loadUsersPage = loadUsersPage;
    SMS.loadUsers = loadUsers;

    // backward-compatible globals
    window.loadUsersPage = loadUsersPage;
    window.loadUsers = loadUsers;
})();
