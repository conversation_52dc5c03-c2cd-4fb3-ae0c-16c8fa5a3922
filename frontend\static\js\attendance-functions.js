/**
 * Attendance Management Functions
 */

async function loadAttendancePage() {
    const contentArea = document.getElementById('contentArea');
    const today = new Date();
    const currentMonth = today.getMonth() + 1;
    const currentYear = today.getFullYear();

    contentArea.innerHTML = `
        <div class="d-flex justify-content-between align-items-center mb-4">
            <h2><i class="fas fa-clipboard-check"></i> Attendance Management</h2>
            <button class="btn btn-success" onclick="showMarkAttendanceModal()">
                <i class="fas fa-check"></i> Mark Attendance
            </button>
        </div>

        <!-- Tabs for Daily and Monthly View -->
        <ul class="nav nav-tabs mb-3" id="attendanceTabs" role="tablist">
            <li class="nav-item" role="presentation">
                <button class="nav-link active" id="daily-tab" data-bs-toggle="tab" data-bs-target="#daily" type="button">
                    <i class="fas fa-calendar-day"></i> Daily View
                </button>
            </li>
            <li class="nav-item" role="presentation">
                <button class="nav-link" id="monthly-tab" data-bs-toggle="tab" data-bs-target="#monthly" type="button">
                    <i class="fas fa-calendar-alt"></i> Monthly Report
                </button>
            </li>
        </ul>

        <div class="tab-content" id="attendanceTabContent">
            <!-- Daily View Tab -->
            <div class="tab-pane fade show active" id="daily" role="tabpanel">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-4">
                                <label>Select Date</label>
                                <input type="date" class="form-control" id="attendanceDate" value="${new Date().toISOString().split('T')[0]}">
                            </div>
                            <div class="col-md-4">
                                <label>Select Class</label>
                                <select class="form-control" id="attendanceClassFilter">
                                    <option value="">All Classes</option>
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label>&nbsp;</label>
                                <button class="btn btn-primary w-100" onclick="loadAttendanceRecords()">
                                    <i class="fas fa-search"></i> View Attendance
                                </button>
                            </div>
                        </div>
                        <div id="attendanceRecordsContainer">
                            <p class="text-center text-muted">Select date and class to view attendance</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Monthly Report Tab -->
            <div class="tab-pane fade" id="monthly" role="tabpanel">
                <div class="card">
                    <div class="card-body">
                        <div class="row mb-3">
                            <div class="col-md-3">
                                <label>Select Class</label>
                                <select class="form-control" id="monthlyClassFilter" required>
                                    <option value="">Select Class</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label>Month</label>
                                <select class="form-control" id="monthlyMonth">
                                    <option value="1">January</option>
                                    <option value="2">February</option>
                                    <option value="3">March</option>
                                    <option value="4">April</option>
                                    <option value="5">May</option>
                                    <option value="6">June</option>
                                    <option value="7">July</option>
                                    <option value="8">August</option>
                                    <option value="9">September</option>
                                    <option value="10">October</option>
                                    <option value="11">November</option>
                                    <option value="12">December</option>
                                </select>
                            </div>
                            <div class="col-md-3">
                                <label>Year</label>
                                <input type="number" class="form-control" id="monthlyYear" value="${currentYear}" min="2020" max="2030">
                            </div>
                            <div class="col-md-3">
                                <label>&nbsp;</label>
                                <button class="btn btn-primary w-100" onclick="loadMonthlyReport()">
                                    <i class="fas fa-chart-bar"></i> Generate Report
                                </button>
                            </div>
                        </div>
                        <div id="monthlyReportContainer">
                            <p class="text-center text-muted">Select class, month and year to view monthly report</p>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Set current month
    document.getElementById('monthlyMonth').value = currentMonth;

    await loadClassesForAttendance();
}

async function loadClassesForAttendance() {
    try {
        const response = await fetch(SMS.apiUrl('/classes'), {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const classes = await response.json();

            // Populate daily view dropdown
            const dailySelect = document.getElementById('attendanceClassFilter');
            if (dailySelect) {
                classes.forEach(cls => {
                    const option = document.createElement('option');
                    option.value = cls.class_id;
                    option.textContent = `${cls.class_name} - ${cls.section}`;
                    dailySelect.appendChild(option);
                });
            }

            // Populate monthly view dropdown
            const monthlySelect = document.getElementById('monthlyClassFilter');
            if (monthlySelect) {
                classes.forEach(cls => {
                    const option = document.createElement('option');
                    option.value = cls.class_id;
                    option.textContent = `${cls.class_name} - ${cls.section}`;
                    monthlySelect.appendChild(option);
                });
            }
        }
    } catch (error) {
        console.error('Error loading classes:', error);
    }
}

async function showMarkAttendanceModal() {
    // Load classes first
    const response = await fetch(SMS.apiUrl('/classes'), {
        headers: {
            'Authorization': `Bearer ${authToken}`
        }
    });

    const classes = response.ok ? await response.json() : [];

    const classOptions = classes.map(cls =>
        `<option value="${cls.class_id}">${cls.class_name} - ${cls.section}</option>`
    ).join('');

    Swal.fire({
        title: 'Mark Attendance',
        html: `
            <form id="markAttendanceForm" class="text-start">
                <div class="mb-3">
                    <label class="form-label">Select Date *</label>
                    <input type="date" class="form-control" id="attendanceDateModal" value="${new Date().toISOString().split('T')[0]}" required>
                    <small class="text-muted">You can select current or past dates</small>
                </div>
                <div class="mb-3">
                    <label class="form-label">Marking Type *</label>
                    <select class="form-control" id="attendanceMarkingType" required onchange="toggleStudentSelection()">
                        <option value="class">Whole Class</option>
                        <option value="individual">Individual Student</option>
                    </select>
                </div>
                <div class="mb-3">
                    <label class="form-label">Select Class *</label>
                    <select class="form-control" id="attendanceClassModal" required onchange="loadStudentsForSelection()">
                        <option value="">Select Class</option>
                        ${classOptions}
                    </select>
                </div>
                <div class="mb-3" id="studentSelectionDiv" style="display: none;">
                    <label class="form-label">Select Student *</label>
                    <select class="form-control" id="attendanceStudentModal">
                        <option value="">Select Student</option>
                    </select>
                </div>
                <p class="text-muted" id="attendanceHelpText">Select whole class or individual student to mark attendance.</p>
            </form>
        `,
        showCancelButton: true,
        confirmButtonText: 'Continue',
        cancelButtonText: 'Cancel',
        didOpen: () => {
            // Add global function for toggling student selection
            window.toggleStudentSelection = () => {
                const markingType = document.getElementById('attendanceMarkingType').value;
                const studentDiv = document.getElementById('studentSelectionDiv');
                const helpText = document.getElementById('attendanceHelpText');

                if (markingType === 'individual') {
                    studentDiv.style.display = 'block';
                    helpText.textContent = 'Select a student to mark individual attendance.';
                } else {
                    studentDiv.style.display = 'none';
                    helpText.textContent = 'You will be able to mark attendance for all students in the class.';
                }
            };

            // Add global function for loading students
            window.loadStudentsForSelection = async () => {
                const classId = document.getElementById('attendanceClassModal').value;
                const studentSelect = document.getElementById('attendanceStudentModal');

                if (!classId) {
                    studentSelect.innerHTML = '<option value="">Select Student</option>';
                    return;
                }

                try {
                    const response = await fetch(SMS.apiUrl(`/classes/${classId}/students`), {
                        headers: {
                            'Authorization': `Bearer ${authToken}`
                        }
                    });

                    if (response.ok) {
                        const students = await response.json();
                        studentSelect.innerHTML = '<option value="">Select Student</option>' +
                            students.map(s => `<option value="${s.student_id}">${s.full_name} (${s.admission_number || s.roll_number})</option>`).join('');
                    }
                } catch (error) {
                    console.error('Error loading students:', error);
                }
            };
        },
        preConfirm: () => {
            const classId = document.getElementById('attendanceClassModal').value;
            const date = document.getElementById('attendanceDateModal').value;
            const markingType = document.getElementById('attendanceMarkingType').value;
            const studentId = document.getElementById('attendanceStudentModal').value;

            if (!classId || !date) {
                Swal.showValidationMessage('Please select both date and class');
                return false;
            }

            if (markingType === 'individual' && !studentId) {
                Swal.showValidationMessage('Please select a student');
                return false;
            }

            return {
                classId: parseInt(classId),
                date,
                markingType,
                studentId: studentId ? parseInt(studentId) : null
            };
        }
    }).then(async (result) => {
        if (result.isConfirmed) {
            if (result.value.markingType === 'individual') {
                await showIndividualAttendanceModal(result.value.studentId, result.value.classId, result.value.date);
            } else {
                await showStudentAttendanceList(result.value.classId, result.value.date);
            }
        }
    });
}

// Export functions to global scope
window.loadAttendancePage = loadAttendancePage;
window.loadClassesForAttendance = loadClassesForAttendance;
window.showMarkAttendanceModal = showMarkAttendanceModal;
