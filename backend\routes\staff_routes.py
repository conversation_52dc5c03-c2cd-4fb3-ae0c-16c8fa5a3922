"""
Staff Routes - Staff management
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from pydantic import BaseModel
from datetime import date
from auth import get_current_user, require_admin
from database import execute_query, execute_non_query
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


class StaffCreate(BaseModel):
    user_id: int
    employee_code: str
    designation: str
    designation_urdu: Optional[str] = None
    department: Optional[str] = None
    joining_date: date
    salary: Optional[float] = None
    is_active: bool = True


class StaffResponse(BaseModel):
    staff_id: int
    user_id: int
    employee_code: str
    designation: str
    designation_urdu: Optional[str]
    department: Optional[str]
    joining_date: date
    salary: Optional[float]
    is_active: bool
    full_name: Optional[str]
    email: Optional[str]
    phone: Optional[str]


@router.post("/", status_code=status.HTTP_201_CREATED)
async def create_staff(
    staff: <PERSON><PERSON><PERSON>,
    current_user: dict = Depends(require_admin)
):
    """Create a new staff member (Admin only)"""
    try:
        query = "EXEC sp_Staff_Create @user_id=?, @employee_code=?, @designation=?, @designation_urdu=?, @department=?, @joining_date=?, @salary=?, @is_active=?"
        execute_non_query(query, (
            staff.user_id,
            staff.employee_code,
            staff.designation,
            staff.designation_urdu,
            staff.department,
            staff.joining_date,
            staff.salary,
            staff.is_active
        ))

        return {"message": "Staff member created successfully"}
    except Exception as e:
        logger.error(f"Error creating staff: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create staff: {str(e)}"
        )


@router.get("/", response_model=List[StaffResponse])
async def get_all_staff(
    is_active: Optional[bool] = None,
    department: Optional[str] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get all staff members"""
    try:
        query = "EXEC sp_Staff_GetAll @is_active=?, @department=?"
        results = execute_query(query, (is_active, department))
        return results
    except Exception as e:
        logger.warning(f"Stored procedure failed, using fallback: {e}")
        # Fallback to direct SQL query - return teachers as staff
        try:
            fallback_query = """
                SELECT
                    u.user_id as staff_id,
                    u.user_id,
                    'EMP' + CAST(u.user_id AS VARCHAR(10)) as employee_code,
                    r.role_name as designation,
                    NULL as designation_urdu,
                    'Teaching' as department,
                    CAST(u.created_at AS DATE) as joining_date,
                    NULL as salary,
                    u.is_active,
                    u.full_name,
                    u.email,
                    u.phone
                FROM users u
                JOIN roles r ON u.role_id = r.role_id
                WHERE u.role_id = 2 AND u.is_active = 1
                ORDER BY u.full_name
            """
            results = execute_query(fallback_query)
            return results
        except Exception as e2:
            logger.error(f"Fallback also failed: {e2}")
            return []


@router.get("/{staff_id}", response_model=StaffResponse)
async def get_staff(
    staff_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get staff by ID"""
    query = "EXEC sp_Staff_GetById @staff_id=?"
    results = execute_query(query, (staff_id,))

    if not results:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Staff member not found"
        )

    return results[0]


@router.put("/{staff_id}")
async def update_staff(
    staff_id: int,
    staff: StaffCreate,
    current_user: dict = Depends(require_admin)
):
    """Update staff (Admin only)"""
    try:
        query = "EXEC sp_Staff_Update @staff_id=?, @user_id=?, @employee_code=?, @designation=?, @designation_urdu=?, @department=?, @joining_date=?, @salary=?, @is_active=?"
        execute_query(query, (
            staff_id,
            staff.user_id,
            staff.employee_code,
            staff.designation,
            staff.designation_urdu,
            staff.department,
            staff.joining_date,
            staff.salary,
            staff.is_active
        ))

        return {"message": "Staff updated successfully"}
    except Exception as e:
        logger.error(f"Error updating staff: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update staff: {str(e)}"
        )


@router.delete("/{staff_id}")
async def delete_staff(
    staff_id: int,
    current_user: dict = Depends(require_admin)
):
    """Delete staff (Admin only)"""
    try:
        query = "EXEC sp_Staff_Delete @staff_id=?"
        execute_query(query, (staff_id,))

        return {"message": "Staff deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting staff: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete staff: {str(e)}"
        )


@router.get("/departments/list")
async def get_departments(current_user: dict = Depends(get_current_user)):
    """Get list of all departments"""
    query = "EXEC sp_Staff_GetDepartments"
    results = execute_query(query)
    return [r['department'] for r in results]

