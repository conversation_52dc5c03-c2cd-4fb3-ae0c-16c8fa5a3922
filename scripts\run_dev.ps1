param(
    [int]$Port = 8000
)

$ErrorActionPreference = "Stop"
$here = Split-Path -Parent $MyInvocation.MyCommand.Definition
$repoRoot = Resolve-Path (Join-Path $here "..")
Set-Location $repoRoot

# Ensure virtualenv is activated in your shell if used
# The app loads .env automatically via pydantic-settings

Write-Host "Starting FastAPI on http://localhost:$Port" -ForegroundColor Green
uvicorn backend.app.main:app --host 0.0.0.0 --port $Port --reload

