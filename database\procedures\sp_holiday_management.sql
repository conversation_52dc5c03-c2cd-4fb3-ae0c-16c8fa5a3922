-- =============================================
-- Holiday Management Stored Procedures
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Procedure: sp_Holiday_Create
-- Description: Create a new holiday
-- =============================================
IF OBJECT_ID('sp_Holiday_Create', 'P') IS NOT NULL
    DROP PROCEDURE sp_Holiday_Create;
GO

CREATE PROCEDURE sp_Holiday_Create
    @holiday_name NVARCHAR(200),
    @holiday_name_urdu NVARCHAR(200) = NULL,
    @holiday_date DATE,
    @holiday_type NVARCHAR(50) = 'Public',
    @description NVARCHAR(500) = NULL,
    @is_active BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        INSERT INTO holidays (
            holiday_name, 
            holiday_name_urdu, 
            holiday_date,
            holiday_type, 
            description, 
            is_active
        )
        VALUES (
            @holiday_name,
            @holiday_name_urdu,
            @holiday_date,
            @holiday_type,
            @description,
            @is_active
        );
        
        SELECT SCOPE_IDENTITY() AS holiday_id;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- Procedure: sp_Holiday_GetAll
-- Description: Get all holidays with optional filters
-- =============================================
IF OBJECT_ID('sp_Holiday_GetAll', 'P') IS NOT NULL
    DROP PROCEDURE sp_Holiday_GetAll;
GO

CREATE PROCEDURE sp_Holiday_GetAll
    @year INT = NULL,
    @month INT = NULL,
    @is_active BIT = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        holiday_id,
        holiday_name,
        holiday_name_urdu,
        holiday_date,
        holiday_type,
        description,
        is_active,
        created_at
    FROM holidays
    WHERE
        (@year IS NULL OR YEAR(holiday_date) = @year)
        AND (@month IS NULL OR MONTH(holiday_date) = @month)
        AND (@is_active IS NULL OR is_active = @is_active)
    ORDER BY holiday_date;
END;
GO

-- =============================================
-- Procedure: sp_Holiday_GetById
-- Description: Get holiday by ID
-- =============================================
IF OBJECT_ID('sp_Holiday_GetById', 'P') IS NOT NULL
    DROP PROCEDURE sp_Holiday_GetById;
GO

CREATE PROCEDURE sp_Holiday_GetById
    @holiday_id INT
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        holiday_id,
        holiday_name,
        holiday_name_urdu,
        holiday_date,
        holiday_type,
        description,
        is_active,
        created_at
    FROM holidays
    WHERE holiday_id = @holiday_id;
END;
GO

-- =============================================
-- Procedure: sp_Holiday_Update
-- Description: Update holiday
-- =============================================
IF OBJECT_ID('sp_Holiday_Update', 'P') IS NOT NULL
    DROP PROCEDURE sp_Holiday_Update;
GO

CREATE PROCEDURE sp_Holiday_Update
    @holiday_id INT,
    @holiday_name NVARCHAR(200),
    @holiday_name_urdu NVARCHAR(200) = NULL,
    @holiday_date DATE,
    @holiday_type NVARCHAR(50),
    @description NVARCHAR(500) = NULL,
    @is_active BIT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        UPDATE holidays
        SET
            holiday_name = @holiday_name,
            holiday_name_urdu = @holiday_name_urdu,
            holiday_date = @holiday_date,
            holiday_type = @holiday_type,
            description = @description,
            is_active = @is_active
        WHERE holiday_id = @holiday_id;
        
        SELECT @@ROWCOUNT AS rows_affected;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- Procedure: sp_Holiday_Delete
-- Description: Delete holiday
-- =============================================
IF OBJECT_ID('sp_Holiday_Delete', 'P') IS NOT NULL
    DROP PROCEDURE sp_Holiday_Delete;
GO

CREATE PROCEDURE sp_Holiday_Delete
    @holiday_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DELETE FROM holidays
        WHERE holiday_id = @holiday_id;
        
        SELECT @@ROWCOUNT AS rows_affected;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- Procedure: sp_Holiday_CheckDate
-- Description: Check if a specific date is a holiday
-- =============================================
IF OBJECT_ID('sp_Holiday_CheckDate', 'P') IS NOT NULL
    DROP PROCEDURE sp_Holiday_CheckDate;
GO

CREATE PROCEDURE sp_Holiday_CheckDate
    @check_date DATE
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        holiday_id,
        holiday_name,
        holiday_name_urdu,
        holiday_date,
        holiday_type,
        description,
        is_active
    FROM holidays
    WHERE holiday_date = @check_date 
        AND is_active = 1;
END;
GO

-- =============================================
-- Procedure: sp_Holiday_GetUpcoming
-- Description: Get upcoming holidays
-- =============================================
IF OBJECT_ID('sp_Holiday_GetUpcoming', 'P') IS NOT NULL
    DROP PROCEDURE sp_Holiday_GetUpcoming;
GO

CREATE PROCEDURE sp_Holiday_GetUpcoming
    @limit INT = 10
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT TOP (@limit)
        holiday_id,
        holiday_name,
        holiday_name_urdu,
        holiday_date,
        holiday_type,
        description,
        is_active
    FROM holidays
    WHERE holiday_date >= CAST(GETDATE() AS DATE)
        AND is_active = 1
    ORDER BY holiday_date;
END;
GO

PRINT 'Holiday management stored procedures created successfully!';

