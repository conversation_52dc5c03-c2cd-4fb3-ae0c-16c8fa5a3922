// Settings Management Module
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    SMS.loadSettingsPage = async function() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="mb-4">
                <h2><i class="fas fa-cog"></i> System Settings</h2>
            </div>

            <div class="row">
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-user-shield"></i> Account Settings</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-primary w-100 mb-2" onclick="SMS.changePassword()">
                                <i class="fas fa-key"></i> Change Password
                            </button>
                            <button class="btn btn-outline-primary w-100" onclick="SMS.updateProfile()">
                                <i class="fas fa-user-edit"></i> Update Profile
                            </button>
                        </div>
                    </div>
                </div>

                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-database"></i> System Settings</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-success w-100 mb-2" onclick="SMS.viewSystemInfo()">
                                <i class="fas fa-info-circle"></i> System Information
                            </button>
                            <button class="btn btn-outline-success w-100" onclick="SMS.viewAuditLogs()">
                                <i class="fas fa-history"></i> Audit Logs
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    };

    SMS.changePassword = async function() {
        SMS.showError('Change password functionality coming soon!');
    };

    SMS.updateProfile = async function() {
        SMS.showError('Update profile functionality coming soon!');
    };

    SMS.viewSystemInfo = async function() {
        try {
            const response = await fetch(SMS.apiUrl('/health'));
            if (response.ok) {
                const data = await response.json();
                Swal.fire({
                    title: 'System Information',
                    html: `
                        <div class="text-start">
                            <p><strong>Application:</strong> ${data.app_name}</p>
                            <p><strong>Version:</strong> ${data.version}</p>
                            <p><strong>Status:</strong> <span class="badge bg-success">${data.status}</span></p>
                            <p><strong>Database:</strong> <span class="badge bg-${data.database === 'connected' ? 'success' : 'danger'}">${data.database}</span></p>
                        </div>
                    `,
                    icon: 'info'
                });
            }
        } catch (error) {
            SMS.showError('Failed to load system information');
        }
    };

    SMS.viewAuditLogs = async function() {
        SMS.showError('Audit logs functionality coming soon!');
    };

    // Backwards-compatible aliases
    window.loadSettingsPage = function(){ return SMS.loadSettingsPage(); };
    window.changePassword = function(){ return SMS.changePassword(); };
    window.updateProfile = function(){ return SMS.updateProfile(); };
    window.viewSystemInfo = function(){ return SMS.viewSystemInfo(); };
    window.viewAuditLogs = function(){ return SMS.viewAuditLogs(); };

})(window);

