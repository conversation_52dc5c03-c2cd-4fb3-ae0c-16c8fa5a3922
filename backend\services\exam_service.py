"""
Exam Service - Business logic for exam and result management
"""
from typing import List, Dict, Optional
from datetime import date
from decimal import Decimal
from database import execute_procedure, execute_procedure_single, execute_query, execute_scalar, execute_non_query
from models import (ExamCreate, ExamResultCreate, BulkMarksEntry, ExamSubjectConfig,
                    ExamSubjectCreate, ExamMarksEntry)
import logging

logger = logging.getLogger(__name__)


class ExamService:
    """Service class for exam operations"""

    @staticmethod
    def create_exam(exam: ExamCreate) -> int:
        """
        Create a new exam

        Args:
            exam: Exam data

        Returns:
            New exam ID
        """
        params = {
            'exam_name': exam.exam_name,
            'exam_name_urdu': exam.exam_name_urdu,
            'exam_type': exam.exam_type,
            'term_type': exam.term_type,
            'academic_year': exam.academic_year,
            'start_date': exam.start_date,
            'end_date': exam.end_date,
            'total_marks': exam.total_marks,
            'passing_marks': exam.passing_marks,
            'weightage': exam.weightage,
            'is_active': exam.is_active
        }

        result = execute_procedure_single('sp_CreateExam', params)
        # procedure returns exam_id
        return int(result.get('exam_id')) if result and 'exam_id' in result else 0

    @staticmethod
    def update_exam(exam_id: int, exam: ExamCreate) -> bool:
        """
        Update an existing exam

        Args:
            exam_id: Exam ID to update
            exam: Updated exam data

        Returns:
            True if successful
        """
        params = {
            'exam_id': exam_id,
            'exam_name': exam.exam_name,
            'exam_name_urdu': exam.exam_name_urdu,
            'exam_type': exam.exam_type,
            'term_type': exam.term_type,
            'academic_year': exam.academic_year,
            'start_date': exam.start_date,
            'end_date': exam.end_date,
            'total_marks': exam.total_marks,
            'passing_marks': exam.passing_marks,
            'weightage': exam.weightage,
            'is_active': exam.is_active
        }

        execute_procedure_single('sp_UpdateExam', params)
        return True

    @staticmethod
    def record_marks(result: ExamResultCreate, entered_by: int) -> Dict:
        """
        Record exam marks for a student

        Args:
            result: Exam result data
            entered_by: User ID who is entering marks

        Returns:
            Result dictionary with grade
        """
        params = {
            'exam_id': result.exam_id,
            'student_id': result.student_id,
            'subject_id': result.subject_id,
            'obtained_marks': float(result.obtained_marks),
            'total_marks': float(result.total_marks),
            'entered_by': entered_by
        }

        result_data = execute_procedure_single('sp_RecordExamMarks', params)
        return result_data

    @staticmethod
    def get_student_results(student_id: int, exam_id: Optional[int] = None) -> List[Dict]:
        """
        Get exam results for a student

        Args:
            student_id: Student ID
            exam_id: Optional exam ID filter

        Returns:
            List of exam results
        """
        params = {'student_id': student_id}
        if exam_id:
            params['exam_id'] = exam_id

        return execute_procedure('sp_GetStudentResults', params)

    @staticmethod
    def get_exam_results(exam_id: int, class_id: Optional[int] = None) -> List[Dict]:
        """
        Get all results for an exam

        Args:
            exam_id: Exam ID
            class_id: Optional class ID filter

        Returns:
            List of exam results
        """
        params = {'exam_id': exam_id}
        if class_id:
            params['class_id'] = class_id

        return execute_procedure('sp_GetExamResults', params)

    @staticmethod
    def get_report_card(student_id: int, exam_id: int) -> Dict:
        """
        Generate report card for a student

        Args:
            student_id: Student ID
            exam_id: Exam ID

        Returns:
            Report card dictionary
        """
        # Get student info via stored procedure
        student_info = execute_procedure_single('sp_GetReportCard', {'student_id': student_id, 'exam_id': exam_id})

        if not student_info:
            return {}

        # Get results
        results = ExamService.get_student_results(student_id, exam_id)

        # Calculate totals
        total_obtained = sum(float(r['obtained_marks']) for r in results)
        total_marks = sum(float(r['total_marks']) for r in results)
        percentage = (total_obtained / total_marks * 100) if total_marks > 0 else 0

        # Calculate overall grade
        if percentage >= 90:
            overall_grade = 'A+'
        elif percentage >= 80:
            overall_grade = 'A'
        elif percentage >= 70:
            overall_grade = 'B'
        elif percentage >= 60:
            overall_grade = 'C'
        elif percentage >= 50:
            overall_grade = 'D'
        elif percentage >= 40:
            overall_grade = 'E'
        else:
            overall_grade = 'F'

        return {
            'student_info': student_info[0],
            'results': results,
            'total_obtained': total_obtained,
            'total_marks': total_marks,
            'percentage': round(percentage, 2),
            'overall_grade': overall_grade,
            'status': 'Pass' if percentage >= 40 else 'Fail'
        }

    @staticmethod
    def get_class_performance(exam_id: int, class_id: int) -> Dict:
        """
        Get class performance statistics

        Args:
            exam_id: Exam ID
            class_id: Class ID

        Returns:
            Performance statistics dictionary
        """
        params = {'exam_id': exam_id, 'class_id': class_id}
        result = execute_procedure_single('sp_GetClassPerformance', params)
        return result if result else {}

    @staticmethod
    def promote_students(current_class_id: int, next_class_id: int,
                        academic_year: int, promoted_by: int) -> Dict:
        """
        Promote students to next class

        Args:
            current_class_id: Current class ID
            next_class_id: Next class ID
            academic_year: Academic year
            promoted_by: User ID who is promoting

        Returns:
            Result dictionary with promotion count
        """
        params = {
            'current_class_id': current_class_id,
            'next_class_id': next_class_id,
            'academic_year': academic_year,
            'promoted_by': promoted_by
        }

        result = execute_procedure_single('sp_PromoteStudents', params)
        return result

    @staticmethod
    def get_exams(academic_year: Optional[int] = None, exam_type: Optional[str] = None) -> List[Dict]:
        """
        Get list of exams

        Args:
            academic_year: Optional academic year filter
            exam_type: Optional exam type filter

        Returns:
            List of exams
        """
        params = {}
        if academic_year is not None:
            params['academic_year'] = academic_year
        if exam_type is not None:
            params['exam_type'] = exam_type

        return execute_procedure('sp_GetExams', params)

    @staticmethod
    def get_top_performers(exam_id: int, class_id: Optional[int] = None, limit: int = 10) -> List[Dict]:
        """
        Get top performing students

        Args:
            exam_id: Exam ID
            class_id: Optional class ID filter
            limit: Number of top students to return

        Returns:
            List of top performers
        """
        params = {'exam_id': exam_id, 'limit': limit}
        if class_id is not None:
            params['class_id'] = class_id

        return execute_procedure('sp_GetTopPerformers', params)


    @staticmethod
    def bulk_marks_entry(bulk_data: BulkMarksEntry, entered_by: int) -> Dict:
        """
        Bulk marks entry for a class

        Args:
            bulk_data: Bulk marks data
            entered_by: User ID who is entering marks

        Returns:
            Result dictionary with success count
        """
        success_count = 0
        failed_count = 0
        errors = []

        for marks in bulk_data.marks_data:
            try:
                # Calculate total obtained marks
                obtained = 0
                if marks.get('theory_marks'):
                    obtained += float(marks['theory_marks'])
                if marks.get('practical_marks'):
                    obtained += float(marks['practical_marks'])
                if marks.get('oral_marks'):
                    obtained += float(marks['oral_marks'])
                if marks.get('assignment_marks'):
                    obtained += float(marks['assignment_marks'])

                # Use existing stored procedure to enter/update marks
                params = {
                    'exam_id': bulk_data.exam_id,
                    'student_id': marks['student_id'],
                    'subject_id': bulk_data.subject_id,
                    'obtained_marks': obtained,
                    'total_marks': marks.get('total_marks', 100),
                    'theory_marks': marks.get('theory_marks'),
                    'practical_marks': marks.get('practical_marks'),
                    'oral_marks': marks.get('oral_marks'),
                    'assignment_marks': marks.get('assignment_marks'),
                    'remarks': marks.get('remarks'),
                    'entered_by': entered_by
                }

                execute_procedure_single('sp_EnterExamMarks', params)

                # If caller wants to keep records as draft, call stored proc to set draft flag
                if getattr(bulk_data, 'is_draft', False):
                    draft_params = {
                        'exam_id': bulk_data.exam_id,
                        'student_id': marks['student_id'],
                        'subject_id': bulk_data.subject_id,
                        'is_draft': 1
                    }

                    execute_procedure_single('sp_SetExamResultDraft', draft_params)

                success_count += 1
            except Exception as e:
                failed_count += 1
                errors.append({
                    'student_id': marks.get('student_id'),
                    'error': str(e)
                })
                logger.error(f"Error entering marks for student {marks.get('student_id')}: {e}")

        return {
            'success_count': success_count,
            'failed_count': failed_count,
            'total_count': len(bulk_data.marks_data),
            'errors': errors
        }

    @staticmethod
    def get_class_students_for_marks_entry(exam_id: int, class_id: int, subject_id: int) -> List[Dict]:
        """
        Get list of students in a class with their existing marks for an exam/subject

        Args:
            exam_id: Exam ID
            class_id: Class ID
            subject_id: Subject ID

        Returns:
            List of students with marks data
        """
        params = {'exam_id': exam_id, 'class_id': class_id, 'subject_id': subject_id}
        return execute_procedure('sp_GetClassStudentsForMarksEntry', params)

    @staticmethod
    def get_subject_performance_summary(exam_id: int, class_id: int, subject_id: int) -> Dict:
        """
        Get performance summary for a subject in a class

        Args:
            exam_id: Exam ID
            class_id: Class ID
            subject_id: Subject ID

        Returns:
            Performance summary dictionary
        """
        params = {'exam_id': exam_id, 'class_id': class_id, 'subject_id': subject_id}
        summary = execute_procedure_single('sp_GetSubjectPerformanceSummary', params)

        if summary:
            # attempt to fetch top scorer via stored proc using same params
            top = execute_procedure('sp_GetTopPerformers', {'exam_id': exam_id, 'class_id': class_id, 'limit': 1})
            if top and len(top) > 0:
                summary['top_scorer_name'] = top[0].get('full_name') or top[0].get('student_name')
                summary['top_scorer_marks'] = top[0].get('total_obtained') or top[0].get('obtained_marks')

            return summary

        return {}

    @staticmethod
    def add_exam_subjects(exam_subject: ExamSubjectCreate) -> Dict:
        """
        Add subjects to an exam

        Args:
            exam_subject: Exam subject mapping data

        Returns:
            Result dictionary
        """
        params = {
            'exam_id': exam_subject.exam_id,
            'subject_id': exam_subject.subject_id,
            'class_id': exam_subject.class_id,
            'max_marks': exam_subject.max_marks,
            'passing_marks': exam_subject.passing_marks,
            'theory_max': exam_subject.theory_max,
            'practical_max': exam_subject.practical_max,
            'oral_max': exam_subject.oral_max,
            'assignment_max': exam_subject.assignment_max
        }

        result = execute_procedure_single('sp_AddExamSubjects', params)
        return result

    @staticmethod
    def get_exam_subjects(exam_id: int, class_id: Optional[int] = None) -> List[Dict]:
        """
        Get subjects for an exam

        Args:
            exam_id: Exam ID
            class_id: Optional class ID filter

        Returns:
            List of subjects
        """
        params = {'exam_id': exam_id}
        if class_id is not None:
            params['class_id'] = class_id

        return execute_procedure('sp_GetExamSubjects', params)

    @staticmethod
    def enter_exam_marks(marks_entry: ExamMarksEntry, entered_by: int) -> Dict:
        """
        Enter or update marks for a student in a subject

        Args:
            marks_entry: Marks entry data
            entered_by: User ID who is entering marks

        Returns:
            Result dictionary with grade and percentage
        """
        params = {
            'exam_id': marks_entry.exam_id,
            'student_id': marks_entry.student_id,
            'subject_id': marks_entry.subject_id,
            'obtained_marks': marks_entry.obtained_marks,
            'total_marks': marks_entry.total_marks,
            'theory_marks': marks_entry.theory_marks,
            'practical_marks': marks_entry.practical_marks,
            'oral_marks': marks_entry.oral_marks,
            'assignment_marks': marks_entry.assignment_marks,
            'remarks': marks_entry.remarks,
            'entered_by': entered_by
        }

        result = execute_procedure_single('sp_EnterExamMarks', params)
        return result

    @staticmethod
    def get_students_for_marks_entry(exam_id: int, class_id: int, subject_id: int) -> List[Dict]:
        """
        Get all students in a class with their marks for a subject

        Args:
            exam_id: Exam ID
            class_id: Class ID
            subject_id: Subject ID

        Returns:
            List of students with their marks
        """
        params = {'exam_id': exam_id, 'class_id': class_id, 'subject_id': subject_id}
        return execute_procedure('sp_GetStudentsForMarksEntry', params)

    @staticmethod
    def calculate_student_summary(exam_id: int, student_id: int) -> Dict:
        """
        Calculate overall exam summary for a student

        Args:
            exam_id: Exam ID
            student_id: Student ID

        Returns:
            Summary data
        """
        params = {
            'exam_id': exam_id,
            'student_id': student_id
        }

        result = execute_procedure_single('sp_CalculateStudentExamSummary', params)
        return result

    @staticmethod
    def get_exam_results(exam_id: int, class_id: Optional[int] = None) -> List[Dict]:
        """
        Get exam results for all students

        Args:
            exam_id: Exam ID
            class_id: Optional class ID filter

        Returns:
            List of student exam summaries
        """
        params = {'exam_id': exam_id}
        if class_id is not None:
            params['class_id'] = class_id

        return execute_procedure('sp_GetExamStudentSummaries', params)
