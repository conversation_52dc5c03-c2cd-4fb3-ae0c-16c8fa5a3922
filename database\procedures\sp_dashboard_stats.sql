-- =============================================
-- Dashboard Statistics Stored Procedures
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Get Dashboard Statistics
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetDashboardStats]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetDashboardStats;
GO

CREATE PROCEDURE sp_GetDashboardStats
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @TotalStudents INT = 0;
    DECLARE @TotalTeachers INT = 0;
    DECLARE @TotalClasses INT = 0;
    DECLARE @TotalStaff INT = 0;
    DECLARE @TodayAttendancePercentage DECIMAL(5,2) = 0.0;
    DECLARE @FeeDefaulters INT = 0;
    DECLARE @TodayDate DATE = CAST(GETDATE() AS DATE);
    DECLARE @CurrentYear INT = YEAR(GETDATE());
    
    -- Get total active students
    SELECT @TotalStudents = COUNT(*) FROM students WHERE is_active = 1;
    
    -- Get total active teachers (role_id = 2)
    SELECT @TotalTeachers = COUNT(*) FROM users WHERE role_id = 2 AND is_active = 1;
    
    -- Get total active classes
    SELECT @TotalClasses = COUNT(*) FROM classes WHERE is_active = 1;
    
    -- Get total active staff (excluding teachers and admin)
    SELECT @TotalStaff = COUNT(*) FROM users WHERE role_id NOT IN (1, 2) AND is_active = 1;
    
    -- Calculate today's attendance percentage
    SELECT @TodayAttendancePercentage = 
        CASE 
            WHEN COUNT(*) > 0 THEN 
                CAST(SUM(CASE WHEN status IN ('Present', 'Late') THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2))
            ELSE 0.0
        END
    FROM attendance 
    WHERE attendance_date = @TodayDate;
    
    -- Get fee defaulters count (students with outstanding fees > 1000)
    SELECT @FeeDefaulters = COUNT(DISTINCT student_id)
    FROM fee_accounts
    WHERE academic_year = @CurrentYear
    AND total_arrears > 1000;
    
    -- Return all statistics
    SELECT 
        @TotalStudents AS total_students,
        @TotalTeachers AS total_teachers,
        @TotalClasses AS total_classes,
        @TotalStaff AS total_staff,
        @TodayAttendancePercentage AS today_attendance_percentage,
        @FeeDefaulters AS fee_defaulters,
        @TodayDate AS today_date,
        @CurrentYear AS current_year;
END
GO

-- =============================================
-- Get Student Count by Status
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetStudentStats]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetStudentStats;
GO

CREATE PROCEDURE sp_GetStudentStats
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        COUNT(*) AS total_students,
        SUM(CASE WHEN status = 'Active' THEN 1 ELSE 0 END) AS active_students,
        SUM(CASE WHEN status = 'Discharged' THEN 1 ELSE 0 END) AS discharged_students,
        SUM(CASE WHEN status = 'Suspended' THEN 1 ELSE 0 END) AS suspended_students,
        SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) AS enabled_students
    FROM students;
END
GO

-- =============================================
-- Get Teacher and Staff Statistics
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetStaffStats]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetStaffStats;
GO

CREATE PROCEDURE sp_GetStaffStats
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        r.role_name,
        COUNT(*) AS count,
        SUM(CASE WHEN u.is_active = 1 THEN 1 ELSE 0 END) AS active_count
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE r.role_name != 'Student'  -- Exclude student role if it exists
    GROUP BY r.role_id, r.role_name
    ORDER BY r.role_name;
END
GO

PRINT 'Dashboard statistics stored procedures created successfully!';
GO
