-- =============================================
-- Stored Procedures for Comprehensive Fee Management
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Procedure: sp_SetClassFeeStructure
-- Description: Set or update fee structure for a class
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_SetClassFeeStructure]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_SetClassFeeStructure;
GO

CREATE PROCEDURE sp_SetClassFeeStructure
    @class_id INT,
    @academic_year INT,
    @monthly_fee DECIMAL(10,2),
    @admission_fee DECIMAL(10,2) = 0,
    @annual_fee DECIMAL(10,2) = 0,
    @exam_fee DECIMAL(10,2) = 0,
    @transport_fee DECIMAL(10,2) = 0,
    @other_fee DECIMAL(10,2) = 0
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if fee structure already exists
        IF EXISTS (SELECT 1 FROM class_fee_structure 
                   WHERE class_id = @class_id AND academic_year = @academic_year)
        BEGIN
            -- Update existing structure
            UPDATE class_fee_structure
            SET monthly_fee = @monthly_fee,
                admission_fee = @admission_fee,
                annual_fee = @annual_fee,
                exam_fee = @exam_fee,
                transport_fee = @transport_fee,
                other_fee = @other_fee,
                updated_at = GETDATE()
            WHERE class_id = @class_id AND academic_year = @academic_year;
            
            SELECT 'Fee structure updated successfully' AS message;
        END
        ELSE
        BEGIN
            -- Insert new structure
            INSERT INTO class_fee_structure (class_id, academic_year, monthly_fee, admission_fee,
                                             annual_fee, exam_fee, transport_fee, other_fee)
            VALUES (@class_id, @academic_year, @monthly_fee, @admission_fee,
                    @annual_fee, @exam_fee, @transport_fee, @other_fee);
            
            SELECT 'Fee structure created successfully' AS message;
        END
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_SetStudentDiscount
-- Description: Set discount for a student
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_SetStudentDiscount]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_SetStudentDiscount;
GO

CREATE PROCEDURE sp_SetStudentDiscount
    @student_id INT,
    @academic_year INT,
    @discount_type NVARCHAR(50),
    @discount_percentage DECIMAL(5,2) = 0,
    @discount_amount DECIMAL(10,2) = 0,
    @apply_to NVARCHAR(50) = 'Monthly Fee',
    @remarks NVARCHAR(500) = NULL,
    @created_by INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Deactivate existing discounts for this student and year
        UPDATE student_fee_discounts
        SET is_active = 0
        WHERE student_id = @student_id AND academic_year = @academic_year;
        
        -- Insert new discount
        INSERT INTO student_fee_discounts (student_id, academic_year, discount_type, discount_percentage,
                                           discount_amount, apply_to, remarks, created_by)
        VALUES (@student_id, @academic_year, @discount_type, @discount_percentage,
                @discount_amount, @apply_to, @remarks, @created_by);
        
        SELECT 'Student discount set successfully' AS message;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_CollectFee
-- Description: Collect fee from student with ledger entry and overpayment handling
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_CollectFee]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_CollectFee;
GO

CREATE PROCEDURE sp_CollectFee
    @student_id INT,
    @academic_year INT,
    @amount DECIMAL(10,2),
    @payment_method NVARCHAR(50),
    @reference_number NVARCHAR(100) = NULL,
    @description NVARCHAR(500) = NULL,
    @collected_by INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        DECLARE @current_balance DECIMAL(10,2);
        DECLARE @new_balance DECIMAL(10,2);
        DECLARE @remaining_amount DECIMAL(10,2) = @amount;
        
        -- Get current balance from ledger
        SELECT TOP 1 @current_balance = ISNULL(balance, 0)
        FROM fee_ledger
        WHERE student_id = @student_id
        ORDER BY ledger_id DESC;
        
        IF @current_balance IS NULL
            SET @current_balance = 0;
        
        -- Calculate new balance (negative = outstanding, positive = advance)
        SET @new_balance = @current_balance + @amount;
        
        -- Insert credit entry in ledger
        INSERT INTO fee_ledger (student_id, academic_year, transaction_type, fee_type,
                                amount, balance, payment_method, reference_number, description, created_by)
        VALUES (@student_id, @academic_year, 'Credit', 'Payment Received',
                @amount, @new_balance, @payment_method, @reference_number, @description, @collected_by);
        
        -- Update monthly fees (pay oldest pending fees first)
        DECLARE @monthly_fee_id INT;
        DECLARE @outstanding DECIMAL(10,2);
        DECLARE @payment_to_apply DECIMAL(10,2);
        
        DECLARE fee_cursor CURSOR FOR
        SELECT monthly_fee_id, outstanding_amount
        FROM student_monthly_fees
        WHERE student_id = @student_id 
        AND academic_year = @academic_year
        AND outstanding_amount > 0
        ORDER BY fee_year, fee_month;
        
        OPEN fee_cursor;
        FETCH NEXT FROM fee_cursor INTO @monthly_fee_id, @outstanding;
        
        WHILE @@FETCH_STATUS = 0 AND @remaining_amount > 0
        BEGIN
            IF @remaining_amount >= @outstanding
            BEGIN
                -- Pay full outstanding
                SET @payment_to_apply = @outstanding;
                
                UPDATE student_monthly_fees
                SET paid_amount = paid_amount + @payment_to_apply,
                    outstanding_amount = 0,
                    status = 'Paid',
                    paid_date = GETDATE(),
                    updated_at = GETDATE()
                WHERE monthly_fee_id = @monthly_fee_id;
            END
            ELSE
            BEGIN
                -- Partial payment
                SET @payment_to_apply = @remaining_amount;
                
                UPDATE student_monthly_fees
                SET paid_amount = paid_amount + @payment_to_apply,
                    outstanding_amount = outstanding_amount - @payment_to_apply,
                    status = 'Partial',
                    updated_at = GETDATE()
                WHERE monthly_fee_id = @monthly_fee_id;
            END
            
            SET @remaining_amount = @remaining_amount - @payment_to_apply;
            
            FETCH NEXT FROM fee_cursor INTO @monthly_fee_id, @outstanding;
        END
        
        CLOSE fee_cursor;
        DEALLOCATE fee_cursor;
        
        COMMIT TRANSACTION;
        
        SELECT 'Fee collected successfully' AS message,
               @new_balance AS new_balance,
               @remaining_amount AS advance_amount;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_GetStudentFeeLedger
-- Description: Get complete fee ledger for a student
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetStudentFeeLedger]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetStudentFeeLedger;
GO

CREATE PROCEDURE sp_GetStudentFeeLedger
    @student_id INT,
    @academic_year INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        ledger_id,
        student_id,
        academic_year,
        transaction_date,
        transaction_type,
        fee_type,
        fee_month,
        fee_year,
        amount,
        balance,
        payment_method,
        reference_number,
        description
    FROM fee_ledger
    WHERE student_id = @student_id
    AND (@academic_year IS NULL OR academic_year = @academic_year)
    ORDER BY ledger_id DESC;
END
GO

-- =============================================
-- Procedure: sp_GetStudentOutstanding
-- Description: Get total outstanding amount for a student
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetStudentOutstanding]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetStudentOutstanding;
GO

CREATE PROCEDURE sp_GetStudentOutstanding
    @student_id INT,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        SUM(outstanding_amount) AS total_outstanding,
        COUNT(CASE WHEN status = 'Pending' THEN 1 END) AS pending_months,
        COUNT(CASE WHEN status = 'Partial' THEN 1 END) AS partial_months,
        COUNT(CASE WHEN status = 'Paid' THEN 1 END) AS paid_months
    FROM student_monthly_fees
    WHERE student_id = @student_id
    AND academic_year = @academic_year
    AND is_active = 1;
END
GO

PRINT 'Fee management stored procedures created successfully!';
GO

