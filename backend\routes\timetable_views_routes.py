"""
Timetable Views Routes
Handles timetable viewing for classes, teachers, and grid views
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from models import (ClassTimetableResponse, TimetableGridResponse)
from auth import get_current_user
from services.timetable_service import TimetableService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/classes/{class_id}", response_model=List[ClassTimetableResponse])
async def get_class_timetable(
    class_id: int,
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Get timetable for a specific class
    
    Args:
        class_id: Class ID
        academic_year: Optional academic year filter
        current_user: Current authenticated user
    
    Returns:
        List of class timetable entries
    """
    try:
        result = TimetableService.get_class_timetable(class_id, academic_year)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching class timetable: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching class timetable"
        )


@router.get("/teachers/{teacher_id}", response_model=List[ClassTimetableResponse])
async def get_teacher_timetable(
    teacher_id: int,
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Get timetable for a specific teacher
    
    Args:
        teacher_id: Teacher ID
        academic_year: Optional academic year filter
        current_user: Current authenticated user
    
    Returns:
        List of teacher's timetable entries
    """
    try:
        result = TimetableService.get_teacher_timetable(teacher_id, academic_year)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching teacher timetable: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching teacher timetable"
        )


@router.get("/grid", response_model=List[TimetableGridResponse])
async def get_all_timetables_grid(
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Get comprehensive timetable grid view for all classes and teachers
    
    Args:
        academic_year: Optional academic year filter
        current_user: Current authenticated user
    
    Returns:
        List of timetable grid entries showing complete school schedule
    """
    try:
        result = TimetableService.get_all_timetables_grid(academic_year)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching timetable grid: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching timetable grid"
        )
