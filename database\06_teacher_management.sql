-- =============================================
-- Teacher Management Tables
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Teacher Periods/Timetable Table
-- =============================================
CREATE TABLE teacher_periods (
    period_id INT IDENTITY(1,1) PRIMARY KEY,
    teacher_id INT NOT NULL,
    class_id INT NOT NULL,
    subject_id INT,
    day_of_week INT NOT NULL, -- 1=Monday, 2=Tuesday, ..., 7=Sunday
    period_number INT NOT NULL, -- 1st period, 2nd period, etc.
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    academic_year INT NOT NULL,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_TeacherPeriods_Teacher FOREIGN KEY (teacher_id) REFERENCES users(user_id),
    CONSTRAINT FK_TeacherPeriods_Class FOREIGN KEY (class_id) REFERENCES classes(class_id),
    CONSTRAINT FK_TeacherPeriods_Subject FOREIGN KEY (subject_id) REFERENCES subjects(subject_id)
);
GO

CREATE INDEX IX_TeacherPeriods_Teacher ON teacher_periods(teacher_id);
CREATE INDEX IX_TeacherPeriods_Class ON teacher_periods(class_id);
CREATE INDEX IX_TeacherPeriods_Day ON teacher_periods(day_of_week);
GO

-- =============================================
-- Teacher Attendance Table
-- =============================================
CREATE TABLE teacher_attendance (
    teacher_attendance_id INT IDENTITY(1,1) PRIMARY KEY,
    teacher_id INT NOT NULL,
    attendance_date DATE NOT NULL,
    status NVARCHAR(20) NOT NULL, -- Present, Absent, Leave, Half-Day, Late
    check_in_time TIME,
    check_out_time TIME,
    leave_type NVARCHAR(50), -- Sick Leave, Casual Leave, Emergency Leave
    remarks NVARCHAR(500),
    marked_by INT NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_TeacherAttendance_Teacher FOREIGN KEY (teacher_id) REFERENCES users(user_id),
    CONSTRAINT FK_TeacherAttendance_MarkedBy FOREIGN KEY (marked_by) REFERENCES users(user_id),
    CONSTRAINT UQ_Teacher_Date UNIQUE (teacher_id, attendance_date)
);
GO

CREATE INDEX IX_TeacherAttendance_Teacher ON teacher_attendance(teacher_id);
CREATE INDEX IX_TeacherAttendance_Date ON teacher_attendance(attendance_date);
CREATE INDEX IX_TeacherAttendance_Status ON teacher_attendance(status);
GO

-- =============================================
-- Period Substitution Table (for absent teachers)
-- =============================================
CREATE TABLE period_substitutions (
    substitution_id INT IDENTITY(1,1) PRIMARY KEY,
    original_period_id INT NOT NULL,
    substitute_teacher_id INT NOT NULL,
    substitution_date DATE NOT NULL,
    remarks NVARCHAR(500),
    assigned_by INT NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_PeriodSubstitutions_Period FOREIGN KEY (original_period_id) REFERENCES teacher_periods(period_id),
    CONSTRAINT FK_PeriodSubstitutions_SubstituteTeacher FOREIGN KEY (substitute_teacher_id) REFERENCES users(user_id),
    CONSTRAINT FK_PeriodSubstitutions_AssignedBy FOREIGN KEY (assigned_by) REFERENCES users(user_id)
);
GO

CREATE INDEX IX_PeriodSubstitutions_Date ON period_substitutions(substitution_date);
CREATE INDEX IX_PeriodSubstitutions_SubstituteTeacher ON period_substitutions(substitute_teacher_id);
GO

PRINT 'Teacher management tables created successfully';
GO
