-- =============================================
-- Authentication Management Stored Procedures
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- sp_Auth_Login - Authenticate user and get user details
-- =============================================
IF OBJECT_ID('sp_Auth_Login', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_Login;
GO

CREATE PROCEDURE sp_Auth_Login
    @username NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        u.user_id,
        u.username,
        u.password_hash,
        u.full_name,
        u.full_name_urdu,
        u.email,
        u.phone,
        u.role_id,
        r.role_name,
        u.is_active,
        u.last_login,
        u.created_at
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE u.username = @username;
END;
GO

-- =============================================
-- sp_Auth_UpdateLastLogin - Update user's last login time
-- =============================================
IF OBJECT_ID('sp_Auth_UpdateLastLogin', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_UpdateLastLogin;
GO

CREATE PROCEDURE sp_Auth_UpdateLastLogin
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE users
    SET last_login = GETDATE()
    WHERE user_id = @user_id;
END;
GO

-- =============================================
-- sp_Auth_GetCurrentUser - Get current user details by user_id
-- =============================================
IF OBJECT_ID('sp_Auth_GetCurrentUser', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_GetCurrentUser;
GO

CREATE PROCEDURE sp_Auth_GetCurrentUser
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        u.user_id,
        u.username,
        u.email,
        u.full_name,
        u.full_name_urdu,
        u.role_id,
        r.role_name,
        u.phone,
        u.is_active,
        u.created_at,
        u.updated_at
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE u.user_id = @user_id;
END;
GO

-- =============================================
-- sp_Auth_ChangePassword - Change user password
-- =============================================
IF OBJECT_ID('sp_Auth_ChangePassword', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_ChangePassword;
GO

CREATE PROCEDURE sp_Auth_ChangePassword
    @user_id INT,
    @new_password_hash NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE users
    SET password_hash = @new_password_hash,
        updated_at = GETDATE()
    WHERE user_id = @user_id;
    
    SELECT @@ROWCOUNT AS rows_affected;
END;
GO

-- =============================================
-- sp_Auth_GetPasswordHash - Get user's password hash for verification
-- =============================================
IF OBJECT_ID('sp_Auth_GetPasswordHash', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_GetPasswordHash;
GO

CREATE PROCEDURE sp_Auth_GetPasswordHash
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT password_hash
    FROM users
    WHERE user_id = @user_id;
END;
GO

-- =============================================
-- sp_Auth_GetAllRoles - Get all active roles
-- =============================================
IF OBJECT_ID('sp_Auth_GetAllRoles', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_GetAllRoles;
GO

CREATE PROCEDURE sp_Auth_GetAllRoles
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT *
    FROM roles
    WHERE is_active = 1
    ORDER BY role_name;
END;
GO

-- =============================================
-- sp_Auth_CreateUser - Create a new user (Register)
-- =============================================
IF OBJECT_ID('sp_Auth_CreateUser', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_CreateUser;
GO

CREATE PROCEDURE sp_Auth_CreateUser
    @username NVARCHAR(50),
    @password_hash NVARCHAR(255),
    @full_name NVARCHAR(255),
    @full_name_urdu NVARCHAR(255) = NULL,
    @email NVARCHAR(255) = NULL,
    @phone NVARCHAR(50) = NULL,
    @role_id INT
AS
BEGIN
    SET NOCOUNT ON;

    -- Check if username already exists
    IF EXISTS (SELECT 1 FROM users WHERE username = @username)
    BEGIN
        RAISERROR('Username already exists', 16, 1);
        RETURN;
    END

    -- Check if email already exists (if provided)
    IF @email IS NOT NULL AND EXISTS (SELECT 1 FROM users WHERE email = @email)
    BEGIN
        RAISERROR('Email already exists', 16, 1);
        RETURN;
    END

    -- Insert new user
    INSERT INTO users (
        username,
        password_hash,
        full_name,
        full_name_urdu,
        email,
        phone,
        role_id,
        is_active,
        created_at,
        updated_at
    )
    VALUES (
        @username,
        @password_hash,
        @full_name,
        @full_name_urdu,
        @email,
        @phone,
        @role_id,
        1,
        GETDATE(),
        GETDATE()
    );

    -- Return the new user ID
    SELECT SCOPE_IDENTITY() AS user_id;
END;
GO

-- =============================================
-- sp_Auth_ValidateUser - Validate user credentials and return user info
-- =============================================
IF OBJECT_ID('sp_Auth_ValidateUser', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_ValidateUser;
GO

CREATE PROCEDURE sp_Auth_ValidateUser
    @username NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        u.user_id,
        u.username,
        u.password_hash,
        u.full_name,
        u.full_name_urdu,
        u.email,
        u.phone,
        u.role_id,
        r.role_name,
        u.is_active,
        u.last_login,
        u.created_at,
        u.updated_at
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE u.username = @username
    AND u.is_active = 1;
END;
GO

-- =============================================
-- sp_Auth_CheckUserExists - Check if username or email exists
-- =============================================
IF OBJECT_ID('sp_Auth_CheckUserExists', 'P') IS NOT NULL
    DROP PROCEDURE sp_Auth_CheckUserExists;
GO

CREATE PROCEDURE sp_Auth_CheckUserExists
    @username NVARCHAR(50) = NULL,
    @email NVARCHAR(255) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    SELECT
        CASE
            WHEN EXISTS (SELECT 1 FROM users WHERE username = @username) THEN 1
            ELSE 0
        END AS username_exists,
        CASE
            WHEN EXISTS (SELECT 1 FROM users WHERE email = @email) THEN 1
            ELSE 0
        END AS email_exists;
END;
GO

PRINT 'Authentication stored procedures created successfully!';
GO

