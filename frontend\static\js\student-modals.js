/**
 * Student Modal Functions - Handles student-related modal dialogs
 */

async function showAddStudentModal() {
    // Load classes first
    const response = await fetch(SMS.apiUrl('/classes'), {
        headers: {
            'Authorization': `Bearer ${authToken}`
        }
    });

    const classes = response.ok ? await response.json() : [];

    const classOptions = classes.map(cls =>
        `<option value="${cls.class_id}">${cls.class_name} - ${cls.section}</option>`
    ).join('');

    Swal.fire({
        title: 'Add New Student',
        html: `
            <form id="addStudentForm" class="text-start">
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Admission Number *</label>
                        <input type="text" class="form-control" id="admissionNumber" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Full Name *</label>
                        <input type="text" class="form-control" id="fullName" required>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Full Name (Urdu)</label>
                        <input type="text" class="form-control" id="fullNameUrdu" dir="rtl">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Father Name *</label>
                        <input type="text" class="form-control" id="fatherName" required>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Father Name (Urdu)</label>
                        <input type="text" class="form-control" id="fatherNameUrdu" dir="rtl">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Date of Birth *</label>
                        <input type="date" class="form-control" id="dateOfBirth" required>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Gender *</label>
                        <select class="form-control" id="gender" required>
                            <option value="">Select Gender</option>
                            <option value="Male">Male</option>
                            <option value="Female">Female</option>
                        </select>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Class *</label>
                        <select class="form-control" id="classId" required>
                            <option value="">Select Class</option>
                            ${classOptions}
                        </select>
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">CNIC</label>
                        <input type="text" class="form-control" id="cnic" placeholder="xxxxx-xxxxxxx-x">
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">B-Form</label>
                        <input type="text" class="form-control" id="bForm">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Parent Phone *</label>
                        <input type="tel" class="form-control" id="parentPhone" required>
                    </div>
                    <div class="col-md-6 mb-3">
                        <label class="form-label">Parent Email</label>
                        <input type="email" class="form-control" id="parentEmail">
                    </div>
                </div>
                <div class="mb-3">
                    <label class="form-label">Address</label>
                    <textarea class="form-control" id="address" rows="2"></textarea>
                </div>
                <div class="mb-3">
                    <label class="form-label">Address (Urdu)</label>
                    <textarea class="form-control" id="addressUrdu" rows="2" dir="rtl"></textarea>
                </div>
            </form>
        `,
        width: '800px',
        showCancelButton: true,
        confirmButtonText: 'Add Student',
        cancelButtonText: 'Cancel',
        preConfirm: () => {
            const form = document.getElementById('addStudentForm');
            if (!form.checkValidity()) {
                Swal.showValidationMessage('Please fill all required fields');
                return false;
            }

            return {
                admission_number: document.getElementById('admissionNumber').value,
                full_name: document.getElementById('fullName').value,
                full_name_urdu: document.getElementById('fullNameUrdu').value || null,
                father_name: document.getElementById('fatherName').value,
                father_name_urdu: document.getElementById('fatherNameUrdu').value || null,
                date_of_birth: document.getElementById('dateOfBirth').value,
                gender: document.getElementById('gender').value,
                class_id: parseInt(document.getElementById('classId').value),
                cnic: document.getElementById('cnic').value || null,
                b_form: document.getElementById('bForm').value || null,
                parent_phone: document.getElementById('parentPhone').value,
                parent_email: document.getElementById('parentEmail').value || null,
                address: document.getElementById('address').value || null,
                address_urdu: document.getElementById('addressUrdu').value || null
            };
        }
    }).then(async (result) => {
        if (result.isConfirmed) {
            await createStudent(result.value);
        }
    });
}

async function createStudent(studentData) {
    try {
        const response = await fetch(SMS.apiUrl('/students'), {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(studentData)
        });

        if (response.ok) {
            showSuccess('Student added successfully!');
            loadStudents();
        } else {
            const error = await response.json();
            showError(error.detail || 'Failed to add student');
        }
    } catch (error) {
        console.error('Error creating student:', error);
        showError('Error creating student');
    }
}

async function viewStudent(studentId) {
    try {
        const response = await fetch(SMS.apiUrl(`/students/${studentId}`), {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const student = await response.json();
            Swal.fire({
                title: 'Student Details',
                html: `
                    <div class="text-start">
                        <p><strong>Admission Number:</strong> ${student.admission_number}</p>
                        <p><strong>Name:</strong> ${student.full_name}</p>
                        ${student.full_name_urdu ? `<p><strong>نام:</strong> ${student.full_name_urdu}</p>` : ''}
                        <p><strong>Father Name:</strong> ${student.father_name}</p>
                        <p><strong>Date of Birth:</strong> ${new Date(student.date_of_birth).toLocaleDateString()}</p>
                        <p><strong>Gender:</strong> ${student.gender}</p>
                        <p><strong>Class:</strong> ${student.class_name || 'N/A'} ${student.section || ''}</p>
                        <p><strong>CNIC:</strong> ${student.cnic || 'N/A'}</p>
                        <p><strong>B-Form:</strong> ${student.b_form || 'N/A'}</p>
                        <p><strong>Parent Phone:</strong> ${student.parent_phone || 'N/A'}</p>
                        <p><strong>Parent Email:</strong> ${student.parent_email || 'N/A'}</p>
                        <p><strong>Address:</strong> ${student.address || 'N/A'}</p>
                        <p><strong>Status:</strong> <span class="badge bg-${student.status === 'Active' ? 'success' : 'secondary'}">${student.status}</span></p>
                    </div>
                `,
                width: '600px'
            });
        }
    } catch (error) {
        console.error('Error viewing student:', error);
        showError('Error loading student details');
    }
}

async function editStudent(studentId) {
    // TODO: Implement edit functionality
    showError('Edit functionality coming soon!');
}

async function deleteStudent(studentId) {
    Swal.fire({
        title: 'Are you sure?',
        text: 'Do you want to discharge this student?',
        icon: 'warning',
        showCancelButton: true,
        confirmButtonColor: '#d33',
        cancelButtonColor: '#3085d6',
        confirmButtonText: 'Yes, discharge student'
    }).then(async (result) => {
        if (result.isConfirmed) {
            try {
                const response = await fetch(SMS.apiUrl(`/students/${studentId}`), {
                    method: 'DELETE',
                    headers: {
                        'Authorization': `Bearer ${authToken}`
                    }
                });

                if (response.ok) {
                    showSuccess('Student discharged successfully!');
                    loadStudents();
                } else {
                    showError('Failed to discharge student');
                }
            } catch (error) {
                console.error('Error deleting student:', error);
                showError('Error discharging student');
            }
        }
    });
}

// Export functions to global scope
window.showAddStudentModal = showAddStudentModal;
window.createStudent = createStudent;
window.viewStudent = viewStudent;
window.editStudent = editStudent;
window.deleteStudent = deleteStudent;
