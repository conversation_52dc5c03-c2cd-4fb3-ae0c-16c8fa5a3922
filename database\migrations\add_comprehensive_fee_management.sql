-- =============================================
-- Migration: Comprehensive Fee Management System
-- Description: Add class-wise fee structure, student discounts, and fee ledger
-- =============================================

USE SchoolManagementDB;
GO

-- Create class_fee_structure table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[class_fee_structure]') AND type in (N'U'))
BEGIN
    CREATE TABLE class_fee_structure (
        fee_structure_id INT IDENTITY(1,1) PRIMARY KEY,
        class_id INT NOT NULL,
        academic_year INT NOT NULL,
        monthly_fee DECIMAL(10,2) NOT NULL DEFAULT 0,
        admission_fee DECIMAL(10,2) DEFAULT 0,
        annual_fee DECIMAL(10,2) DEFAULT 0,
        exam_fee DECIMAL(10,2) DEFAULT 0,
        transport_fee DECIMAL(10,2) DEFAULT 0,
        other_fee DECIMAL(10,2) DEFAULT 0,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (class_id) REFERENCES classes(class_id)
    );
    PRINT 'Table class_fee_structure created successfully';
END
ELSE
BEGIN
    PRINT 'Table class_fee_structure already exists';
END
GO

-- Create student_fee_discounts table
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[student_fee_discounts]') AND type in (N'U'))
BEGIN
    CREATE TABLE student_fee_discounts (
        discount_id INT IDENTITY(1,1) PRIMARY KEY,
        student_id INT NOT NULL,
        academic_year INT NOT NULL,
        discount_type NVARCHAR(50) NOT NULL,  -- Scholarship, Sibling Discount, Merit, Financial Aid
        discount_percentage DECIMAL(5,2) DEFAULT 0,  -- Percentage discount
        discount_amount DECIMAL(10,2) DEFAULT 0,  -- Fixed amount discount
        apply_to NVARCHAR(50) DEFAULT 'Monthly Fee',  -- Monthly Fee, Annual Fee, All Fees
        remarks NVARCHAR(500) NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        created_by INT NULL,
        FOREIGN KEY (student_id) REFERENCES students(student_id)
    );
    PRINT 'Table student_fee_discounts created successfully';
END
ELSE
BEGIN
    PRINT 'Table student_fee_discounts already exists';
END
GO

-- Create fee_ledger table (like bank account)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[fee_ledger]') AND type in (N'U'))
BEGIN
    CREATE TABLE fee_ledger (
        ledger_id INT IDENTITY(1,1) PRIMARY KEY,
        student_id INT NOT NULL,
        academic_year INT NOT NULL,
        transaction_date DATETIME2 DEFAULT GETDATE(),
        transaction_type NVARCHAR(20) NOT NULL,  -- Debit (fee charged), Credit (payment received)
        fee_type NVARCHAR(50) NOT NULL,  -- Monthly Fee, Admission Fee, Exam Fee, etc.
        fee_month INT NULL,  -- 1-12 for monthly fees
        fee_year INT NULL,  -- Year for monthly fees
        amount DECIMAL(10,2) NOT NULL,
        balance DECIMAL(10,2) NOT NULL,  -- Running balance (negative = outstanding, positive = advance)
        payment_method NVARCHAR(50) NULL,  -- Cash, Bank, Online, Cheque
        reference_number NVARCHAR(100) NULL,  -- Receipt number, transaction ID
        description NVARCHAR(500) NULL,
        created_by INT NULL,
        created_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (student_id) REFERENCES students(student_id)
    );
    PRINT 'Table fee_ledger created successfully';
END
ELSE
BEGIN
    PRINT 'Table fee_ledger already exists';
END
GO

-- Create student_monthly_fees table (tracks each month's fee status)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[student_monthly_fees]') AND type in (N'U'))
BEGIN
    CREATE TABLE student_monthly_fees (
        monthly_fee_id INT IDENTITY(1,1) PRIMARY KEY,
        student_id INT NOT NULL,
        class_id INT NOT NULL,
        academic_year INT NOT NULL,
        fee_month INT NOT NULL,  -- 1-12
        fee_year INT NOT NULL,
        base_fee DECIMAL(10,2) NOT NULL,  -- Base fee for the class
        discount_amount DECIMAL(10,2) DEFAULT 0,  -- Discount applied
        final_fee DECIMAL(10,2) NOT NULL,  -- After discount
        paid_amount DECIMAL(10,2) DEFAULT 0,
        outstanding_amount DECIMAL(10,2) NOT NULL,
        status NVARCHAR(20) DEFAULT 'Pending',  -- Pending, Paid, Partial, Overdue
        due_date DATE NULL,
        paid_date DATE NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (student_id) REFERENCES students(student_id),
        FOREIGN KEY (class_id) REFERENCES classes(class_id)
    );
    PRINT 'Table student_monthly_fees created successfully';
END
ELSE
BEGIN
    PRINT 'Table student_monthly_fees already exists';
END
GO

-- Add indexes for faster queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_class_fee_structure_class_year' AND object_id = OBJECT_ID('class_fee_structure'))
BEGIN
    CREATE INDEX IX_class_fee_structure_class_year ON class_fee_structure(class_id, academic_year);
    PRINT 'Index IX_class_fee_structure_class_year created';
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_student_fee_discounts_student_year' AND object_id = OBJECT_ID('student_fee_discounts'))
BEGIN
    CREATE INDEX IX_student_fee_discounts_student_year ON student_fee_discounts(student_id, academic_year);
    PRINT 'Index IX_student_fee_discounts_student_year created';
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_fee_ledger_student_date' AND object_id = OBJECT_ID('fee_ledger'))
BEGIN
    CREATE INDEX IX_fee_ledger_student_date ON fee_ledger(student_id, transaction_date);
    PRINT 'Index IX_fee_ledger_student_date created';
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_student_monthly_fees_student_month' AND object_id = OBJECT_ID('student_monthly_fees'))
BEGIN
    CREATE INDEX IX_student_monthly_fees_student_month ON student_monthly_fees(student_id, fee_year, fee_month);
    PRINT 'Index IX_student_monthly_fees_student_month created';
END
GO

PRINT 'Comprehensive fee management migration completed successfully!';
GO

