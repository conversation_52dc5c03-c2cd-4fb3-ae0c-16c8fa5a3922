/**
 * Authentication Module
 * Handles login, logout, and authentication state
 */
(function(window){
    const SMS = window.SMS || (window.SMS = {});
    
    // ==================== AUTHENTICATION FUNCTIONS ====================
    
    /**
     * Initialize authentication module
     */
    SMS.initAuth = function() {
        console.log('Initializing authentication module...');

        // Always show login form first
        SMS.showLoginForm();

        // Check if user has a valid token and validate it
        const token = localStorage.getItem('authToken');
        if (token) {
            console.log('[AUTH] Found existing token, validating...');
            SMS.authToken = token;
            SMS.validateTokenAndShowDashboard();
        } else {
            console.log('[AUTH] No token found, showing login form');
        }

        // Setup login form handler
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.addEventListener('submit', SMS.handleLogin);
        }

        // Setup logout button handler
        const logoutBtn = document.getElementById('logoutBtn');
        if (logoutBtn) {
            logoutBtn.addEventListener('click', SMS.handleLogout);
        }
    };
    
    /**
     * Handle login form submission
     */
    SMS.handleLogin = async function(e) {
        e.preventDefault();
        console.log('[AUTH] Login form submitted');

        const username = document.getElementById('loginUsername').value.trim();
        const password = document.getElementById('loginPassword').value;

        console.log('[AUTH] Username:', username);

        if (!username || !password) {
            console.log('[AUTH] Missing username or password');
            if (typeof SMS.showError === 'function') {
                SMS.showError('Please enter both username and password');
            } else {
                alert('Please enter both username and password');
            }
            return;
        }

        try {
            // Show loading state
            const submitBtn = e.target.querySelector('button[type="submit"]');
            const originalText = submitBtn.innerHTML;
            submitBtn.disabled = true;
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i> Logging in...';

            console.log('[AUTH] Calling login API...');

            // Call login API
            const response = await fetch('/api/auth/login', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    username: username,
                    password: password
                })
            });

            console.log('[AUTH] Response status:', response.status);

            const data = await response.json();
            console.log('[AUTH] Response data:', data);

            if (response.ok) {
                console.log('[AUTH] Login successful!');

                // Store authentication token
                localStorage.setItem('authToken', data.access_token);
                localStorage.setItem('refreshToken', data.refresh_token);
                localStorage.setItem('userId', data.user_id);
                localStorage.setItem('username', data.username);
                localStorage.setItem('userRole', data.role);

                SMS.authToken = data.access_token;
                SMS.currentUser = {
                    user_id: data.user_id,
                    username: data.username,
                    role: data.role
                };

                // Show success message
                console.log('[AUTH] Showing success message');
                if (typeof SMS.showSuccess === 'function') {
                    SMS.showSuccess('Login successful! Welcome ' + data.username);
                } else {
                    console.log('SUCCESS: Login successful! Welcome ' + data.username);
                }

                // Clear form
                document.getElementById('loginUsername').value = '';
                document.getElementById('loginPassword').value = '';

                // Load user profile and show dashboard
                console.log('[AUTH] Loading user profile...');
                await SMS.loadUserProfile();

                console.log('[AUTH] Showing dashboard...');
                SMS.showDashboard();

            } else {
                // Show error message
                console.log('[AUTH] Login failed:', data.detail);
                if (typeof SMS.showError === 'function') {
                    SMS.showError(data.detail || 'Login failed. Please check your credentials.');
                } else {
                    alert(data.detail || 'Login failed. Please check your credentials.');
                }
                submitBtn.disabled = false;
                submitBtn.innerHTML = originalText;
            }

        } catch (error) {
            console.error('[AUTH] Login error:', error);
            if (typeof SMS.showError === 'function') {
                SMS.showError('An error occurred during login. Please try again.');
            } else {
                alert('An error occurred during login. Please try again.');
            }
            const submitBtn = e.target.querySelector('button[type="submit"]');
            if (submitBtn) {
                submitBtn.disabled = false;
                submitBtn.innerHTML = '<i class="fas fa-sign-in-alt"></i> Login';
            }
        }
    };
    
    /**
     * Clear authentication data
     */
    SMS.clearAuthData = function() {
        console.log('[AUTH] Clearing authentication data');
        localStorage.removeItem('authToken');
        localStorage.removeItem('refreshToken');
        localStorage.removeItem('userId');
        localStorage.removeItem('username');
        localStorage.removeItem('userRole');

        SMS.authToken = null;
        SMS.currentUser = null;
    };

    /**
     * Handle logout
     */
    SMS.handleLogout = function() {
        Swal.fire({
            title: 'Logout',
            text: 'Are you sure you want to logout?',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Logout',
            cancelButtonText: 'Cancel'
        }).then((result) => {
            if (result.isConfirmed) {
                // Clear authentication data
                SMS.clearAuthData();

                // Show login form
                SMS.showLoginForm();

                SMS.showSuccess('Logged out successfully');
            }
        });
    };
    
    /**
     * Validate existing token and show dashboard if valid
     */
    SMS.validateTokenAndShowDashboard = async function() {
        try {
            console.log('[AUTH] Validating token...');
            const response = await fetch('/api/auth/me', {
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`
                }
            });

            if (response.ok) {
                const user = await response.json();
                SMS.currentUser = user;
                console.log('[AUTH] Token is valid, showing dashboard');

                // Update UI with user info
                const userNameElement = document.getElementById('userName');
                if (userNameElement) {
                    userNameElement.textContent = user.full_name || user.username;
                }

                // Show dashboard
                SMS.showDashboard();
            } else {
                console.log('[AUTH] Token is invalid, clearing and showing login');
                // Token is invalid, clear it and show login
                SMS.clearAuthData();
                SMS.showLoginForm();
            }
        } catch (error) {
            console.error('[AUTH] Error validating token:', error);
            // On error, clear token and show login
            SMS.clearAuthData();
            SMS.showLoginForm();
        }
    };

    /**
     * Load user profile
     */
    SMS.loadUserProfile = async function() {
        try {
            const response = await fetch('/api/auth/me', {
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`
                }
            });

            if (response.ok) {
                const user = await response.json();
                SMS.currentUser = user;

                // Update UI with user info
                const userNameElement = document.getElementById('userName');
                if (userNameElement) {
                    userNameElement.textContent = user.full_name || user.username;
                }
            } else {
                // If profile loading fails, token might be invalid
                console.log('[AUTH] Failed to load user profile, token might be invalid');
                SMS.clearAuthData();
                SMS.showLoginForm();
            }
        } catch (error) {
            console.error('Error loading user profile:', error);
            SMS.clearAuthData();
            SMS.showLoginForm();
        }
    };

    /**
     * Show login form
     */
    SMS.showLoginForm = function() {
        const loginSection = document.getElementById('loginSection');
        const dashboardSection = document.getElementById('dashboardSection');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');

        if (loginSection) loginSection.style.display = 'block';
        if (dashboardSection) dashboardSection.style.display = 'none';
        if (sidebar) sidebar.style.display = 'none';
        if (mainContent) {
            mainContent.classList.remove('col-md-10');
            mainContent.classList.add('col-md-12');
        }
    };

    /**
     * Show dashboard
     */
    SMS.showDashboard = function() {
        console.log('[AUTH] Showing dashboard...');

        const loginSection = document.getElementById('loginSection');
        const dashboardSection = document.getElementById('dashboardSection');
        const sidebar = document.getElementById('sidebar');
        const mainContent = document.getElementById('mainContent');

        if (loginSection) loginSection.style.display = 'none';
        if (dashboardSection) dashboardSection.style.display = 'block';
        if (sidebar) sidebar.style.display = 'block';
        if (mainContent) {
            mainContent.classList.remove('col-md-12');
            mainContent.classList.add('col-md-10');
        }

        // Setup navigation after showing sidebar
        SMS.setupNavigation();

        // Load dashboard data
        if (typeof SMS.loadDashboardData === 'function') {
            console.log('[AUTH] Loading dashboard data...');
            SMS.loadDashboardData();
        } else {
            console.log('[AUTH] loadDashboardData function not found');
        }
    };

    // ==================== BACKWARDS COMPATIBILITY ====================

    window.handleLogin = function(e) { return SMS.handleLogin(e); };
    window.handleLogout = function() { return SMS.handleLogout(); };
    window.loadUserProfile = function() { return SMS.loadUserProfile(); };

    // ==================== NAVIGATION ====================

    /**
     * Setup navigation event listeners
     */
    SMS.setupNavigation = function() {
        console.log('[AUTH] Setting up navigation...');

        // Get all navigation links
        const navLinks = document.querySelectorAll('.sidebar .nav-link');
        console.log('[AUTH] Found navigation links:', navLinks.length);

        if (navLinks.length === 0) {
            console.error('[AUTH] No navigation links found! Sidebar might be hidden or not loaded yet.');
            return;
        }

        navLinks.forEach((link, index) => {
            const page = link.getAttribute('data-page');
            console.log(`[AUTH] Setting up link ${index + 1}: ${page}`);

            // Remove any existing listeners by cloning the node
            const newLink = link.cloneNode(true);
            link.parentNode.replaceChild(newLink, link);

            // Add new click listener
            newLink.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                const clickedPage = this.getAttribute('data-page');
                console.log('[NAV] ========================================');
                console.log('[NAV] Navigation link clicked:', clickedPage);
                console.log('[NAV] ========================================');

                // Remove active class from all links
                document.querySelectorAll('.sidebar .nav-link').forEach(l => l.classList.remove('active'));

                // Add active class to clicked link
                this.classList.add('active');

                // Load the page
                console.log('[NAV] Checking for loadPage function...');
                if (typeof window.loadPage === 'function') {
                    console.log('[NAV] loadPage function found, calling with page:', clickedPage);
                    try {
                        window.loadPage(clickedPage);
                    } catch (error) {
                        console.error('[NAV] Error calling loadPage:', error);
                    }
                } else {
                    console.error('[NAV] loadPage function NOT FOUND!');
                    console.error('[NAV] window.loadPage type:', typeof window.loadPage);
                }
            });
        });

        console.log('[AUTH] Navigation setup complete. Total links configured:', navLinks.length);
    };

    // ==================== INITIALIZATION ====================

    // Initialize when DOM is ready
    if (document.readyState === 'loading') {
        document.addEventListener('DOMContentLoaded', function() {
            console.log('[AUTH] DOM loaded, initializing...');
            SMS.initAuth();
            // Note: setupNavigation() is called in showDashboard() after sidebar is visible
        });
    } else {
        console.log('[AUTH] DOM already loaded, initializing...');
        SMS.initAuth();
        // Note: setupNavigation() is called in showDashboard() after sidebar is visible
    }

})(window);

