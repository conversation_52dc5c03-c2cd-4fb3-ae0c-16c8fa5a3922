"""
Timetable Routes - Main Router
Combines all timetable-related route modules
"""
from fastapi import APIRouter
from .timetable_config_routes import router as config_router
from .timetable_management_routes import router as management_router
from .timetable_views_routes import router as views_router

# Create main router
router = APIRouter()

# Include all sub-routers
router.include_router(config_router, tags=["Timetable Configuration"])
router.include_router(management_router, tags=["Timetable Management"])
router.include_router(views_router, tags=["Timetable Views"])

# Note: All timetable functionality has been modularized:
# - timetable_config_routes.py: School configuration and period templates
# - timetable_management_routes.py: Period assignments and availability checks
# - timetable_views_routes.py: Class, teacher, and grid view endpoints
