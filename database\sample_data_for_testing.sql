-- =============================================
-- Sample Data for Testing (Temporary)
-- Run this script to add sample data for testing
-- This data can be removed when application goes live
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Check and Insert Sample Teachers (if not exist)
-- =============================================
IF NOT EXISTS (SELECT 1 FROM users WHERE role_id = 2)
BEGIN
    -- Insert sample teachers (role_id = 2 is Teacher)
    INSERT INTO users (username, password_hash, full_name, full_name_urdu, email, phone, role_id, is_active) VALUES
    ('teacher1', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewY5GyYIr.8VfGSO', 'Ahmed Khan', N'احمد خان', '<EMAIL>', '03001111111', 2, 1),
    ('teacher2', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewY5GyYIr.8VfGSO', 'Sara Ali', N'سارہ علی', '<EMAIL>', '03002222222', 2, 1),
    ('teacher3', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewY5GyYIr.8VfGSO', 'Muhammad Aslam', N'محمد اسلم', '<EMAIL>', '03003333333', 2, 1),
    ('teacher4', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewY5GyYIr.8VfGSO', 'Fatima Bibi', N'فاطمہ بی بی', '<EMAIL>', '03004444444', 2, 1),
    ('teacher5', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewY5GyYIr.8VfGSO', 'Imran Hussain', N'عمران حسین', '<EMAIL>', '03005555555', 2, 1);
    PRINT 'Sample teachers inserted.';
END
ELSE
    PRINT 'Teachers already exist.';
GO

-- =============================================
-- Check and Insert Sample Classes (if not exist)
-- =============================================
IF NOT EXISTS (SELECT 1 FROM classes)
BEGIN
    INSERT INTO classes (class_name, class_name_urdu, section, academic_year, capacity, is_active) VALUES
    ('Nursery', N'نرسری', 'A', 2025, 30, 1),
    ('Prep', N'پریپ', 'A', 2025, 30, 1),
    ('Class 1', N'کلاس 1', 'A', 2025, 35, 1),
    ('Class 1', N'کلاس 1', 'B', 2025, 35, 1),
    ('Class 2', N'کلاس 2', 'A', 2025, 35, 1),
    ('Class 3', N'کلاس 3', 'A', 2025, 40, 1),
    ('Class 4', N'کلاس 4', 'A', 2025, 40, 1),
    ('Class 5', N'کلاس 5', 'A', 2025, 40, 1),
    ('Class 6', N'کلاس 6', 'A', 2025, 40, 1),
    ('Class 7', N'کلاس 7', 'A', 2025, 40, 1),
    ('Class 8', N'کلاس 8', 'A', 2025, 40, 1);
    PRINT 'Sample classes inserted.';
END
ELSE
    PRINT 'Classes already exist.';
GO

-- =============================================
-- Check and Insert Sample Students (if not exist)
-- =============================================
IF NOT EXISTS (SELECT 1 FROM students)
BEGIN
    DECLARE @class1 INT = (SELECT TOP 1 class_id FROM classes WHERE class_name = 'Class 1' AND section = 'A');
    DECLARE @class2 INT = (SELECT TOP 1 class_id FROM classes WHERE class_name = 'Class 2' AND section = 'A');
    DECLARE @class3 INT = (SELECT TOP 1 class_id FROM classes WHERE class_name = 'Class 3' AND section = 'A');

    INSERT INTO students (admission_number, full_name, full_name_urdu, father_name, date_of_birth, gender, class_id, status, is_active) VALUES
    ('2025001', 'Ali Ahmed', N'علی احمد', 'Ahmed Khan', '2018-05-15', 'Male', @class1, 'Active', 1),
    ('2025002', 'Ayesha Fatima', N'عائشہ فاطمہ', 'Muhammad Ali', '2018-03-22', 'Female', @class1, 'Active', 1),
    ('2025003', 'Hassan Raza', N'حسن رضا', 'Raza Ahmed', '2017-08-10', 'Male', @class2, 'Active', 1),
    ('2025004', 'Zainab Bibi', N'زینب بی بی', 'Arif Shah', '2017-11-05', 'Female', @class2, 'Active', 1),
    ('2025005', 'Usman Khan', N'عثمان خان', 'Khan Sahib', '2016-02-18', 'Male', @class3, 'Active', 1),
    ('2025006', 'Maryam Ali', N'مریم علی', 'Ali Hassan', '2016-07-25', 'Female', @class3, 'Active', 1),
    ('2025007', 'Bilal Ahmed', N'بلال احمد', 'Ahmed Shah', '2018-01-12', 'Male', @class1, 'Active', 1),
    ('2025008', 'Sara Khan', N'سارہ خان', 'Imran Khan', '2017-09-30', 'Female', @class2, 'Active', 1);
    PRINT 'Sample students inserted.';
END
ELSE
    PRINT 'Students already exist.';
GO

-- =============================================
-- Check and Insert Sample Holidays (if not exist)
-- =============================================
IF NOT EXISTS (SELECT 1 FROM holidays WHERE YEAR(holiday_date) = 2025)
BEGIN
    INSERT INTO holidays (holiday_name, holiday_name_urdu, holiday_date, holiday_type, is_active) VALUES
    ('Kashmir Day', N'یوم کشمیر', '2025-02-05', 'Public', 1),
    ('Pakistan Day', N'یوم پاکستان', '2025-03-23', 'Public', 1),
    ('Labour Day', N'یوم مزدور', '2025-05-01', 'Public', 1),
    ('Independence Day', N'یوم آزادی', '2025-08-14', 'Public', 1),
    ('Iqbal Day', N'یوم اقبال', '2025-11-09', 'Public', 1),
    ('Quaid-e-Azam Day', N'یوم قائد اعظم', '2025-12-25', 'Public', 1),
    ('New Year', N'نیا سال', '2025-01-01', 'Public', 1);
    PRINT 'Sample holidays inserted.';
END
ELSE
    PRINT 'Holidays for 2025 already exist.';
GO

-- =============================================
-- Check and Insert Sample Subjects (if not exist)
-- =============================================
IF NOT EXISTS (SELECT 1 FROM subjects)
BEGIN
    INSERT INTO subjects (subject_name, subject_name_urdu, subject_code, total_marks, passing_marks, is_active) VALUES
    ('English', N'انگلش', 'ENG', 100, 40, 1),
    ('Urdu', N'اردو', 'URD', 100, 40, 1),
    ('Mathematics', N'ریاضی', 'MATH', 100, 40, 1),
    ('Science', N'سائنس', 'SCI', 100, 40, 1),
    ('Social Studies', N'معاشرتی علوم', 'SST', 100, 40, 1),
    ('Islamiyat', N'اسلامیات', 'ISL', 100, 40, 1),
    ('Computer', N'کمپیوٹر', 'COMP', 100, 40, 1);
    PRINT 'Sample subjects inserted.';
END
ELSE
    PRINT 'Subjects already exist.';
GO

-- =============================================
-- Check and Insert Sample School Config (if not exist)
-- =============================================
IF OBJECT_ID('school_config', 'U') IS NOT NULL
BEGIN
    IF NOT EXISTS (SELECT 1 FROM school_config WHERE config_key = 'school_start_time')
    BEGIN
        INSERT INTO school_config (config_key, config_value, description) VALUES
        ('school_start_time', '08:00', 'School start time'),
        ('school_end_time', '14:00', 'School end time'),
        ('period_duration', '40', 'Period duration in minutes'),
        ('break_duration', '30', 'Break duration in minutes'),
        ('periods_before_break', '3', 'Number of periods before break'),
        ('total_periods', '8', 'Total periods per day');
        PRINT 'School config inserted.';
    END
    ELSE
        PRINT 'School config already exists.';
END
ELSE
    PRINT 'school_config table does not exist - run 07_school_configuration.sql first';
GO

PRINT '=================================================';
PRINT 'Sample data insertion completed!';
PRINT '=================================================';
GO

