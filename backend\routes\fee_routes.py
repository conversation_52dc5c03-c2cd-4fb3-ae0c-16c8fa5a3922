"""
Fee Routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional
from datetime import date
from decimal import Decimal
from models import (FeeCollectionRequest, FeeStructureCreate, SuccessResponse,
                    ClassFeeStructureCreate, StudentDiscountCreate)
from auth import get_current_user, require_accountant
from services.fee_service import FeeService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/collect", response_model=SuccessResponse)
async def collect_fee(
    request: FeeCollectionRequest,
    current_user: dict = Depends(require_accountant)
):
    """
    Collect fee from student (Accountant/Admin only)
    
    Args:
        request: Fee collection request
        current_user: Current authenticated user
    
    Returns:
        Success response with receipt number
    """
    result = FeeService.collect_fee(request, current_user['user_id'])
    
    if result and result.get('Result') == 'Success':
        return SuccessResponse(
            message=result.get('Message', 'Fee collected successfully'),
            data={
                'receipt_number': result.get('ReceiptNumber'),
                'total_amount': result.get('TotalAmount')
            }
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result.get('Message', 'Failed to collect fee')
        )


@router.get("/calculate-fine/{student_id}", response_model=dict)
async def calculate_fine(
    student_id: int,
    fee_month: int = Query(..., ge=1, le=12),
    academic_year: int = Query(...),
    current_user: dict = Depends(require_accountant)
):
    """
    Calculate fine for late payment (Accountant/Admin only)
    
    Args:
        student_id: Student ID
        fee_month: Fee month (1-12)
        academic_year: Academic year
        current_user: Current authenticated user
    
    Returns:
        Calculated fine details
    """
    fine_data = FeeService.calculate_fine(student_id, fee_month, academic_year)
    return fine_data


@router.post("/calculate-arrears", response_model=SuccessResponse)
async def calculate_arrears(
    academic_year: int = Query(...),
    current_month: int = Query(..., ge=1, le=12),
    current_user: dict = Depends(require_accountant)
):
    """
    Calculate arrears for all students (Accountant/Admin only)
    
    Args:
        academic_year: Academic year
        current_month: Current month (1-12)
        current_user: Current authenticated user
    
    Returns:
        Success response
    """
    result = FeeService.calculate_arrears(academic_year, current_month)
    
    if result and result.get('Result') == 'Success':
        return SuccessResponse(message=result.get('Message', 'Arrears calculated successfully'))
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result.get('Message', 'Failed to calculate arrears')
        )


@router.post("/structures", response_model=SuccessResponse)
async def create_fee_structure(
    structure: FeeStructureCreate,
    current_user: dict = Depends(require_accountant)
):
    """
    Create fee structure for a class (Accountant/Admin only)
    
    Args:
        structure: Fee structure data
        current_user: Current authenticated user
    
    Returns:
        Success response with fee structure ID
    """
    try:
        fee_structure_id = FeeService.create_fee_structure(structure)
        
        return SuccessResponse(
            message="Fee structure created successfully",
            data={"fee_structure_id": fee_structure_id}
        )
    except Exception as e:
        logger.error(f"Error creating fee structure: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create fee structure: {str(e)}"
        )


@router.get("/structures", response_model=list)
async def get_fee_structures(
    class_id: Optional[int] = Query(None),
    academic_year: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get fee structures
    
    Args:
        class_id: Optional class ID filter
        academic_year: Optional academic year filter
        current_user: Current authenticated user
    
    Returns:
        List of fee structures
    """
    structures = FeeService.get_fee_structures(class_id, academic_year)
    return structures


@router.get("/account/{student_id}", response_model=dict)
async def get_fee_account(
    student_id: int,
    academic_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get fee account for a student
    
    Args:
        student_id: Student ID
        academic_year: Academic year
        current_user: Current authenticated user
    
    Returns:
        Fee account details
    """
    account = FeeService.get_fee_account(student_id, academic_year)
    
    if not account:
        # Create account if doesn't exist
        if current_user['role_name'] in ['Accountant', 'Administrator']:
            fee_account_id = FeeService.create_fee_account(student_id, academic_year)
            account = FeeService.get_fee_account(student_id, academic_year)
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Fee account not found"
            )
    
    return account


@router.get("/transactions/{student_id}", response_model=list)
async def get_fee_transactions(
    student_id: int,
    academic_year: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get fee transaction history for a student
    
    Args:
        student_id: Student ID
        academic_year: Optional academic year filter
        current_user: Current authenticated user
    
    Returns:
        List of fee transactions
    """
    transactions = FeeService.get_fee_transactions(student_id, academic_year)
    return transactions


@router.get("/defaulters", response_model=list)
async def get_defaulters(
    academic_year: int = Query(...),
    min_arrears: Decimal = Query(Decimal('1000')),
    current_user: dict = Depends(require_accountant)
):
    """
    Get list of fee defaulters (Accountant/Admin only)
    
    Args:
        academic_year: Academic year
        min_arrears: Minimum arrears amount
        current_user: Current authenticated user
    
    Returns:
        List of defaulter students
    """
    defaulters = FeeService.get_defaulters(academic_year, min_arrears)
    return defaulters


@router.get("/collection/daily", response_model=dict)
async def get_daily_collection(
    collection_date: date = Query(...),
    current_user: dict = Depends(require_accountant)
):
    """
    Get daily fee collection summary (Accountant/Admin only)
    
    Args:
        collection_date: Date
        current_user: Current authenticated user
    
    Returns:
        Collection summary
    """
    summary = FeeService.get_daily_collection(collection_date)
    return summary


@router.get("/collection/monthly", response_model=dict)
async def get_monthly_collection(
    year: int = Query(...),
    month: int = Query(..., ge=1, le=12),
    current_user: dict = Depends(require_accountant)
):
    """
    Get monthly fee collection summary (Accountant/Admin only)
    
    Args:
        year: Year
        month: Month (1-12)
        current_user: Current authenticated user
    
    Returns:
        Collection summary
    """
    summary = FeeService.get_monthly_collection(year, month)
    return summary

@router.post("/class-fee-structure", response_model=SuccessResponse)
async def set_class_fee_structure(
    fee_structure: ClassFeeStructureCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Set or update fee structure for a class

    Args:
        fee_structure: Class fee structure data
        current_user: Current authenticated user

    Returns:
        Success response
    """
    try:
        result = FeeService.set_class_fee_structure(fee_structure)
        return SuccessResponse(
            message=result.get('message', 'Fee structure set successfully'),
            data=result
        )
    except Exception as e:
        logger.error(f"Error setting class fee structure: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to set fee structure: {str(e)}"
        )


@router.get("/class-fee-structure/all", response_model=list)
async def get_all_class_fee_structures(
    academic_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get fee structures for all classes

    Args:
        academic_year: Academic year
        current_user: Current authenticated user

    Returns:
        List of fee structures with class details
    """
    try:
        structures = FeeService.get_all_class_fee_structures(academic_year)
        return structures
    except Exception as e:
        logger.error(f"Error getting all class fee structures: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get fee structures: {str(e)}"
        )


@router.get("/class-fee-structure/{class_id}", response_model=dict)
async def get_class_fee_structure(
    class_id: int,
    academic_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get fee structure for a class

    Args:
        class_id: Class ID
        academic_year: Academic year
        current_user: Current authenticated user

    Returns:
        Fee structure details
    """
    try:
        structure = FeeService.get_class_fee_structure(class_id, academic_year)
        if not structure:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Fee structure not found"
            )
        return structure
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting class fee structure: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get fee structure: {str(e)}"
        )


@router.post("/student-discount", response_model=SuccessResponse)
async def set_student_discount(
    discount: StudentDiscountCreate,
    current_user: dict = Depends(get_current_user)
):
    """
    Set discount for a student

    Args:
        discount: Student discount data
        current_user: Current authenticated user

    Returns:
        Success response
    """
    try:
        result = FeeService.set_student_discount(discount, current_user['user_id'])
        return SuccessResponse(
            message=result.get('message', 'Student discount set successfully'),
            data=result
        )
    except Exception as e:
        logger.error(f"Error setting student discount: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to set discount: {str(e)}"
        )


@router.get("/student-discount/all", response_model=list)
async def get_all_student_discounts(
    academic_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all student discounts for an academic year

    Args:
        academic_year: Academic year
        current_user: Current authenticated user

    Returns:
        List of student discounts
    """
    try:
        discounts = FeeService.get_all_student_discounts(academic_year)
        return discounts
    except Exception as e:
        logger.error(f"Error getting all student discounts: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get discounts: {str(e)}"
        )


@router.get("/student-discount/{student_id}", response_model=dict)
async def get_student_discount(
    student_id: int,
    academic_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get discount for a student

    Args:
        student_id: Student ID
        academic_year: Academic year
        current_user: Current authenticated user

    Returns:
        Discount details
    """
    try:
        discount = FeeService.get_student_discount(student_id, academic_year)
        return discount if discount else {}
    except Exception as e:
        logger.error(f"Error getting student discount: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get discount: {str(e)}"
        )


@router.post("/collect-comprehensive", response_model=SuccessResponse)
async def collect_fee_comprehensive(
    request: FeeCollectionRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Collect fee with comprehensive ledger management

    Args:
        request: Fee collection request
        current_user: Current authenticated user

    Returns:
        Success response with balance and advance amount
    """
    try:
        result = FeeService.collect_fee_comprehensive(request, current_user['user_id'])
        return SuccessResponse(
            message=result.get('message', 'Fee collected successfully'),
            data=result
        )
    except Exception as e:
        logger.error(f"Error collecting fee: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to collect fee: {str(e)}"
        )


@router.get("/ledger/{student_id}", response_model=list)
async def get_student_ledger(
    student_id: int,
    academic_year: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get complete fee ledger for a student

    Args:
        student_id: Student ID
        academic_year: Optional academic year filter
        current_user: Current authenticated user

    Returns:
        List of ledger entries
    """
    try:
        ledger = FeeService.get_student_ledger(student_id, academic_year)
        return ledger
    except Exception as e:
        logger.error(f"Error getting student ledger: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get ledger: {str(e)}"
        )


@router.get("/outstanding/{student_id}", response_model=dict)
async def get_student_outstanding(
    student_id: int,
    academic_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get total outstanding amount for a student

    Args:
        student_id: Student ID
        academic_year: Academic year
        current_user: Current authenticated user

    Returns:
        Outstanding summary
    """
    try:
        outstanding = FeeService.get_student_outstanding(student_id, academic_year)
        return outstanding
    except Exception as e:
        logger.error(f"Error getting student outstanding: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get outstanding: {str(e)}"
        )


@router.get("/class/{class_id}/students-for-collection", response_model=list)
async def get_students_for_fee_collection(
    class_id: int,
    academic_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all students in a class with their fee status

    Args:
        class_id: Class ID
        academic_year: Academic year
        current_user: Current authenticated user

    Returns:
        List of students with fee status
    """
    try:
        students = FeeService.get_students_for_collection(class_id, academic_year)
        return students
    except Exception as e:
        logger.error(f"Error getting students for collection: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get students: {str(e)}"
        )


@router.get("/class/{class_id}/monthly-overview", response_model=dict)
async def get_class_monthly_fee_overview(
    class_id: int,
    academic_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get month-by-month fee status for all students in a class

    Args:
        class_id: Class ID
        academic_year: Academic year
        current_user: Current authenticated user

    Returns:
        Dictionary with students and their monthly fee status
    """
    try:
        overview = FeeService.get_class_monthly_fee_overview(class_id, academic_year)
        return overview
    except Exception as e:
        logger.error(f"Error getting class monthly fee overview: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get monthly overview: {str(e)}"
        )


@router.post("/generate-monthly-fees", response_model=dict)
async def generate_monthly_fees(
    class_id: Optional[int] = Query(None),
    academic_year: int = Query(...),
    start_month: int = Query(1, ge=1, le=12),
    end_month: int = Query(12, ge=1, le=12),
    current_user: dict = Depends(get_current_user)
):
    """
    Generate monthly fee records for students

    Args:
        class_id: Class ID (None for all classes)
        academic_year: Academic year
        start_month: Starting month (1-12)
        end_month: Ending month (1-12)
        current_user: Current authenticated user

    Returns:
        Generation result with statistics
    """
    try:
        result = FeeService.generate_monthly_fees(class_id, academic_year, start_month, end_month)
        return result
    except Exception as e:
        logger.error(f"Error generating monthly fees: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to generate monthly fees: {str(e)}"
        )


