-- =============================================
-- Add Sample Timetable Data
-- =============================================

USE SchoolManagementDB;
GO

-- Clear existing timetable data
DELETE FROM class_timetable;

-- Add sample timetable entries for different classes
-- Assuming we have subjects and teachers in the database

-- Get some sample data
DECLARE @class1_id INT = (SELECT TOP 1 class_id FROM classes WHERE class_name LIKE '%Class 1%');
DECLARE @class2_id INT = (SELECT TOP 1 class_id FROM classes WHERE class_name LIKE '%Class 2%');
DECLARE @teacher_id INT = (SELECT TOP 1 staff_id FROM staff WHERE designation = 'Teacher');
DECLARE @subject1_id INT = (SELECT TOP 1 subject_id FROM subjects WHERE subject_name LIKE '%Math%');
DECLARE @subject2_id INT = (SELECT TOP 1 subject_id FROM subjects WHERE subject_name LIKE '%English%');
DECLARE @subject3_id INT = (SELECT TOP 1 subject_id FROM subjects WHERE subject_name LIKE '%Science%');

-- If we don't have specific subjects, get any available subjects
IF @subject1_id IS NULL SET @subject1_id = (SELECT TOP 1 subject_id FROM subjects);
IF @subject2_id IS NULL SET @subject2_id = (SELECT subject_id FROM subjects WHERE subject_id != @subject1_id ORDER BY subject_id OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY);
IF @subject3_id IS NULL SET @subject3_id = (SELECT subject_id FROM subjects WHERE subject_id NOT IN (@subject1_id, @subject2_id) ORDER BY subject_id OFFSET 0 ROWS FETCH NEXT 1 ROWS ONLY);

-- Add sample timetable for Class 1 (Monday to Friday)
IF @class1_id IS NOT NULL AND @teacher_id IS NOT NULL AND @subject1_id IS NOT NULL
BEGIN
    -- Monday
    INSERT INTO class_timetable (class_id, day_of_week, period_number, start_time, end_time, subject_id, teacher_id, academic_year, is_active, created_at, updated_at)
    VALUES
    (@class1_id, 1, 1, '08:30:00', '09:10:00', @subject1_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 1, 2, '09:10:00', '09:50:00', @subject2_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 1, 3, '10:00:00', '10:40:00', @subject3_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 1, 4, '10:40:00', '11:20:00', @subject1_id, @teacher_id, 2025, 1, GETDATE(), GETDATE());
    
    -- Add all weekdays at once
    INSERT INTO class_timetable (class_id, day_of_week, period_number, start_time, end_time, subject_id, teacher_id, academic_year, is_active, created_at, updated_at)
    VALUES
    -- Tuesday
    (@class1_id, 2, 1, '08:30:00', '09:10:00', @subject2_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 2, 2, '09:10:00', '09:50:00', @subject1_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 2, 3, '10:00:00', '10:40:00', @subject3_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 2, 4, '10:40:00', '11:20:00', @subject2_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    -- Wednesday
    (@class1_id, 3, 1, '08:30:00', '09:10:00', @subject3_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 3, 2, '09:10:00', '09:50:00', @subject1_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 3, 3, '10:00:00', '10:40:00', @subject2_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 3, 4, '10:40:00', '11:20:00', @subject3_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    -- Thursday
    (@class1_id, 4, 1, '08:30:00', '09:10:00', @subject1_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 4, 2, '09:10:00', '09:50:00', @subject3_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 4, 3, '10:00:00', '10:40:00', @subject2_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 4, 4, '10:40:00', '11:20:00', @subject1_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    -- Friday
    (@class1_id, 5, 1, '08:30:00', '09:10:00', @subject2_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 5, 2, '09:10:00', '09:50:00', @subject1_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 5, 3, '10:00:00', '10:40:00', @subject3_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()),
    (@class1_id, 5, 4, '10:40:00', '11:20:00', @subject2_id, @teacher_id, 2025, 1, GETDATE(), GETDATE());
    
    PRINT 'Sample timetable data added for Class 1';
END
ELSE
BEGIN
    PRINT 'Could not add timetable data - missing required data (class, teacher, or subjects)';
    PRINT 'Class 1 ID: ' + ISNULL(CAST(@class1_id AS VARCHAR), 'NULL');
    PRINT 'Teacher ID: ' + ISNULL(CAST(@teacher_id AS VARCHAR), 'NULL');
    PRINT 'Subject 1 ID: ' + ISNULL(CAST(@subject1_id AS VARCHAR), 'NULL');
END

-- Verify the data
SELECT 'Timetable entries created' as data_type, COUNT(*) as count FROM class_timetable;
SELECT 'Classes with timetables' as data_type, COUNT(DISTINCT class_id) as count FROM class_timetable;

PRINT 'Sample timetable data setup complete!';
GO
