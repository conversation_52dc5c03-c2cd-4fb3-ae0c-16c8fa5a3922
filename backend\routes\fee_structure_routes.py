"""
Fee Structure Routes
Handles fee structure management
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional
from models import FeeStructureCreate, SuccessResponse
from auth import require_accountant, get_current_user
from services.fee_service import FeeService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/structures", response_model=SuccessResponse)
async def create_fee_structure(
    structure: FeeStructureCreate,
    current_user: dict = Depends(require_accountant)
):
    """
    Create a new fee structure (Accountant/Admin only)
    
    Args:
        structure: Fee structure data
        current_user: Current authenticated user
    
    Returns:
        Success response
    """
    try:
        result = FeeService.create_fee_structure(structure, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return SuccessResponse(
                message=result.get('Message', 'Fee structure created successfully'),
                data={'structure_id': result.get('StructureId')}
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to create fee structure')
            )
    except Exception as e:
        logger.error(f"Error creating fee structure: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during fee structure creation"
        )


@router.get("/structures", response_model=list)
async def get_fee_structures(
    class_id: Optional[int] = Query(None),
    academic_year: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get fee structures with optional filtering
    
    Args:
        class_id: Optional class ID filter
        academic_year: Optional academic year filter
        current_user: Current authenticated user
    
    Returns:
        List of fee structures
    """
    try:
        result = FeeService.get_fee_structures(class_id, academic_year)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching fee structures: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching fee structures"
        )
