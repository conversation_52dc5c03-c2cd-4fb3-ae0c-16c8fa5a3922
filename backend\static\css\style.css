/* School Management System - Custom Styles */

:root {
    --primary-color: #4CAF50;
    --secondary-color: #2196F3;
    --danger-color: #f44336;
    --warning-color: #ff9800;
    --success-color: #4CAF50;
    --info-color: #00bcd4;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
}

.urdu {
    font-family: 'Noto Nastaliq Urdu', serif;
    direction: rtl;
    text-align: right;
}

/* Navbar */
.navbar {
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.navbar-brand {
    font-weight: bold;
    color: white !important;
}

/* Sidebar */
.sidebar {
    min-height: calc(100vh - 56px);
    background-color: #2c3e50;
    padding: 20px 0;
}

.sidebar .nav-link {
    color: #ecf0f1;
    padding: 12px 20px;
    margin: 5px 10px;
    border-radius: 5px;
    transition: all 0.3s;
}

.sidebar .nav-link:hover {
    background-color: #34495e;
    color: white;
}

.sidebar .nav-link.active {
    background-color: var(--primary-color);
    color: white;
}

.sidebar .nav-link i {
    margin-right: 10px;
    width: 20px;
}

/* Main Content */
.main-content {
    padding: 30px;
}

/* Cards */
.stat-card {
    border-radius: 10px;
    padding: 20px;
    color: white;
    margin-bottom: 20px;
    transition: transform 0.3s;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 5px 15px rgba(0,0,0,0.3);
}

.stat-card.primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
}

.stat-card.success {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.stat-card.warning {
    background: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    color: #333;
}

.stat-card.danger {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    color: #333;
}

.stat-card h3 {
    font-size: 2.5rem;
    font-weight: bold;
    margin: 10px 0;
}

.stat-card p {
    margin: 0;
    opacity: 0.9;
}

/* Login Form */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
}

.login-card {
    background: white;
    border-radius: 15px;
    padding: 40px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.2);
    max-width: 400px;
    width: 100%;
}

.login-card h2 {
    text-align: center;
    margin-bottom: 30px;
    color: var(--primary-color);
}

/* Buttons */
.btn-primary {
    background-color: var(--primary-color);
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background-color: #45a049;
    border-color: #45a049;
}

/* Tables */
.table-container {
    background: white;
    border-radius: 10px;
    padding: 20px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table thead {
    background-color: var(--primary-color);
    color: white;
}

/* Forms */
.form-container {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

/* Quick Actions */
.quick-actions {
    background: white;
    border-radius: 10px;
    padding: 20px;
    margin-top: 20px;
}

.quick-action-btn {
    width: 100%;
    margin-bottom: 10px;
    padding: 15px;
    border-radius: 8px;
    border: none;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s;
}

.quick-action-btn:hover {
    transform: translateX(5px);
}

/* Loading Spinner */
.spinner {
    border: 3px solid #f3f3f3;
    border-top: 3px solid var(--primary-color);
    border-radius: 50%;
    width: 40px;
    height: 40px;
    animation: spin 1s linear infinite;
    margin: 20px auto;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Alerts */
.alert {
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

/* Responsive */
@media (max-width: 768px) {
    .sidebar {
        min-height: auto;
    }
    
    .main-content {
        padding: 15px;
    }
    
    .stat-card h3 {
        font-size: 2rem;
    }
}

/* Hidden class */
.hidden {
    display: none !important;
}

/* Page content */
.page-content {
    background: white;
    border-radius: 10px;
    padding: 30px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    min-height: 500px;
}

.page-header {
    border-bottom: 2px solid var(--primary-color);
    padding-bottom: 15px;
    margin-bottom: 25px;
}

.page-header h2 {
    color: var(--primary-color);
    margin: 0;
}

/* Badge */
.badge {
    padding: 5px 10px;
    border-radius: 5px;
    font-size: 12px;
}

/* Modal improvements */
.modal-header {
    background-color: var(--primary-color);
    color: white;
}

.modal-footer {
    border-top: 1px solid #dee2e6;
}

