"""
Report Routes - PDF and Excel generation
"""
from fastapi import APIRouter, Depends, HTTPException, Query, Response
from typing import Optional
from datetime import date
from auth import get_current_user
from services.attendance_service import AttendanceService
from services.fee_service import FeeService
from services.exam_service import ExamService
from database import execute_query
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/attendance/monthly-pdf")
async def generate_attendance_monthly_pdf(
    class_id: int = Query(...),
    year: int = Query(...),
    month: int = Query(..., ge=1, le=12),
    current_user: dict = Depends(get_current_user)
):
    """
    Generate monthly attendance report as PDF
    
    Args:
        class_id: Class ID
        year: Year
        month: Month (1-12)
        current_user: Current authenticated user
    
    Returns:
        PDF file
    """
    try:
        from io import BytesIO
        from weasyprint import HTML, CSS
        from jinja2 import Template
        
        # Get data
        report_data = AttendanceService.get_monthly_report(class_id, year, month)
        
        # Get class info
        class_query = "SELECT * FROM classes WHERE class_id = ?"
        class_info = execute_query(class_query, (class_id,))
        
        if not class_info:
            raise HTTPException(status_code=404, detail="Class not found")
        
        # HTML template
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; direction: ltr; }
                .header { text-align: center; margin-bottom: 20px; }
                .urdu { font-family: 'Jameel Noori Nastaleeq', 'Noto Nastalikh Urdu'; direction: rtl; }
                table { width: 100%; border-collapse: collapse; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #4CAF50; color: white; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Monthly Attendance Report</h1>
                <h2 class="urdu">ماہانہ حاضری رپورٹ</h2>
                <p>Class: {{ class_name }} - {{ section }} | Month: {{ month }}/{{ year }}</p>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Admission No</th>
                        <th>Student Name</th>
                        <th>Total Days</th>
                        <th>Present</th>
                        <th>Absent</th>
                        <th>Leave</th>
                        <th>Late</th>
                        <th>Percentage</th>
                    </tr>
                </thead>
                <tbody>
                    {% for student in students %}
                    <tr>
                        <td>{{ student.admission_number }}</td>
                        <td>{{ student.full_name }}</td>
                        <td>{{ student.total_days }}</td>
                        <td>{{ student.present_days }}</td>
                        <td>{{ student.absent_days }}</td>
                        <td>{{ student.leave_days }}</td>
                        <td>{{ student.late_days }}</td>
                        <td>{{ student.attendance_percentage }}%</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
        </body>
        </html>
        """
        
        template = Template(html_template)
        html_content = template.render(
            class_name=class_info[0]['class_name'],
            section=class_info[0]['section'],
            year=year,
            month=month,
            students=report_data
        )
        
        # Generate PDF
        pdf_file = BytesIO()
        HTML(string=html_content).write_pdf(pdf_file)
        pdf_file.seek(0)
        
        return Response(
            content=pdf_file.getvalue(),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=attendance_report_{class_id}_{year}_{month}.pdf"
            }
        )
    except Exception as e:
        logger.error(f"Error generating PDF: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate PDF: {str(e)}")


@router.get("/fees/defaulters-excel")
async def generate_defaulters_excel(
    academic_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Generate fee defaulters report as Excel
    
    Args:
        academic_year: Academic year
        current_user: Current authenticated user
    
    Returns:
        Excel file
    """
    try:
        from io import BytesIO
        from openpyxl import Workbook
        from openpyxl.styles import Font, PatternFill, Alignment
        
        # Get data
        defaulters = FeeService.get_defaulters(academic_year)
        
        # Create workbook
        wb = Workbook()
        ws = wb.active
        ws.title = "Fee Defaulters"
        
        # Headers
        headers = [
            "Admission No", "Student Name", "Class", "Section",
            "Total Fee", "Paid", "Arrears", "Fines", "Balance",
            "Last Payment", "Parent Phone", "Parent Email"
        ]
        
        # Style headers
        header_fill = PatternFill(start_color="4CAF50", end_color="4CAF50", fill_type="solid")
        header_font = Font(bold=True, color="FFFFFF")
        
        for col, header in enumerate(headers, 1):
            cell = ws.cell(row=1, column=col, value=header)
            cell.fill = header_fill
            cell.font = header_font
            cell.alignment = Alignment(horizontal="center")
        
        # Data rows
        for row, student in enumerate(defaulters, 2):
            ws.cell(row=row, column=1, value=student['admission_number'])
            ws.cell(row=row, column=2, value=student['full_name'])
            ws.cell(row=row, column=3, value=student['class_name'])
            ws.cell(row=row, column=4, value=student['section'])
            ws.cell(row=row, column=5, value=float(student['total_fee']))
            ws.cell(row=row, column=6, value=float(student['total_paid']))
            ws.cell(row=row, column=7, value=float(student['total_arrears']))
            ws.cell(row=row, column=8, value=float(student['total_fines']))
            ws.cell(row=row, column=9, value=float(student['balance']))
            ws.cell(row=row, column=10, value=str(student['last_payment_date']) if student['last_payment_date'] else 'N/A')
            ws.cell(row=row, column=11, value=student['parent_phone'])
            ws.cell(row=row, column=12, value=student['parent_email'])
        
        # Auto-adjust column widths
        for column in ws.columns:
            max_length = 0
            column_letter = column[0].column_letter
            for cell in column:
                try:
                    if len(str(cell.value)) > max_length:
                        max_length = len(str(cell.value))
                except:
                    pass
            adjusted_width = min(max_length + 2, 50)
            ws.column_dimensions[column_letter].width = adjusted_width
        
        # Save to BytesIO
        excel_file = BytesIO()
        wb.save(excel_file)
        excel_file.seek(0)
        
        return Response(
            content=excel_file.getvalue(),
            media_type="application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            headers={
                "Content-Disposition": f"attachment; filename=fee_defaulters_{academic_year}.xlsx"
            }
        )
    except Exception as e:
        logger.error(f"Error generating Excel: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate Excel: {str(e)}")


@router.get("/exams/report-card-pdf/{student_id}/{exam_id}")
async def generate_report_card_pdf(
    student_id: int,
    exam_id: int,
    current_user: dict = Depends(get_current_user)
):
    """
    Generate student report card as PDF
    
    Args:
        student_id: Student ID
        exam_id: Exam ID
        current_user: Current authenticated user
    
    Returns:
        PDF file
    """
    try:
        from io import BytesIO
        from weasyprint import HTML
        from jinja2 import Template
        
        # Get report card data
        report_card = ExamService.get_report_card(student_id, exam_id)
        
        if not report_card:
            raise HTTPException(status_code=404, detail="Report card not found")
        
        # HTML template
        html_template = """
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; border-bottom: 2px solid #333; padding-bottom: 10px; }
                .student-info { margin: 20px 0; }
                .urdu { font-family: 'Jameel Noori Nastaleeq'; direction: rtl; }
                table { width: 100%; border-collapse: collapse; margin: 20px 0; }
                th, td { border: 1px solid #ddd; padding: 10px; text-align: center; }
                th { background-color: #4CAF50; color: white; }
                .summary { margin-top: 20px; font-size: 18px; font-weight: bold; }
                .grade { font-size: 24px; color: #4CAF50; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>School Management System</h1>
                <h2>Report Card - {{ exam_name }}</h2>
            </div>
            <div class="student-info">
                <p><strong>Student Name:</strong> {{ student_name }}</p>
                <p><strong>Admission No:</strong> {{ admission_number }}</p>
                <p><strong>Class:</strong> {{ class_name }} - {{ section }}</p>
            </div>
            <table>
                <thead>
                    <tr>
                        <th>Subject</th>
                        <th>Obtained Marks</th>
                        <th>Total Marks</th>
                        <th>Percentage</th>
                        <th>Grade</th>
                    </tr>
                </thead>
                <tbody>
                    {% for result in results %}
                    <tr>
                        <td>{{ result.subject_name }}</td>
                        <td>{{ result.obtained_marks }}</td>
                        <td>{{ result.total_marks }}</td>
                        <td>{{ result.percentage }}%</td>
                        <td>{{ result.grade }}</td>
                    </tr>
                    {% endfor %}
                </tbody>
            </table>
            <div class="summary">
                <p>Total Obtained: {{ total_obtained }} / {{ total_marks }}</p>
                <p>Overall Percentage: {{ percentage }}%</p>
                <p>Overall Grade: <span class="grade">{{ overall_grade }}</span></p>
                <p>Status: {{ status }}</p>
            </div>
        </body>
        </html>
        """
        
        template = Template(html_template)
        html_content = template.render(
            exam_name=report_card['student_info']['exam_name'],
            student_name=report_card['student_info']['full_name'],
            admission_number=report_card['student_info']['admission_number'],
            class_name=report_card['student_info']['class_name'],
            section=report_card['student_info']['section'],
            results=report_card['results'],
            total_obtained=report_card['total_obtained'],
            total_marks=report_card['total_marks'],
            percentage=report_card['percentage'],
            overall_grade=report_card['overall_grade'],
            status=report_card['status']
        )
        
        # Generate PDF
        pdf_file = BytesIO()
        HTML(string=html_content).write_pdf(pdf_file)
        pdf_file.seek(0)
        
        return Response(
            content=pdf_file.getvalue(),
            media_type="application/pdf",
            headers={
                "Content-Disposition": f"attachment; filename=report_card_{student_id}_{exam_id}.pdf"
            }
        )
    except Exception as e:
        logger.error(f"Error generating report card PDF: {e}")
        raise HTTPException(status_code=500, detail=f"Failed to generate PDF: {str(e)}")

