"""
Dashboard Routes
"""
from fastapi import APIRouter, Depends, HTTPException, status
from auth import get_current_user
from database import execute_procedure_single, execute_procedure, execute_query
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/stats")
async def get_dashboard_stats(current_user: dict = Depends(get_current_user)):
    """
    Get dashboard statistics
    
    Args:
        current_user: Current authenticated user
    
    Returns:
        Dashboard statistics including student count, teacher count, attendance, etc.
    """
    try:
        # Use stored procedure to get dashboard stats
        stats = execute_procedure_single('sp_GetDashboardStats')
        
        if not stats:
            # Fallback to individual queries if stored procedure fails
            logger.warning("Stored procedure failed, using fallback queries")
            
            # Get student count
            student_result = execute_query("SELECT COUNT(*) as count FROM students WHERE is_active = 1")
            total_students = student_result[0]['count'] if student_result else 0
            
            # Get teacher count
            teacher_result = execute_query("SELECT COUNT(*) as count FROM users WHERE role_id = 2 AND is_active = 1")
            total_teachers = teacher_result[0]['count'] if teacher_result else 0
            
            # Get class count
            class_result = execute_query("SELECT COUNT(*) as count FROM classes WHERE is_active = 1")
            total_classes = class_result[0]['count'] if class_result else 0
            
            # Get today's attendance percentage
            today_attendance = execute_query("""
                SELECT 
                    CASE 
                        WHEN COUNT(*) > 0 THEN 
                            CAST(SUM(CASE WHEN status IN ('Present', 'Late') THEN 1 ELSE 0 END) * 100.0 / COUNT(*) AS DECIMAL(5,2))
                        ELSE 0.0
                    END as percentage
                FROM attendance 
                WHERE attendance_date = CAST(GETDATE() AS DATE)
            """)
            today_attendance_percentage = today_attendance[0]['percentage'] if today_attendance else 0.0
            
            # Get fee defaulters count
            defaulters_result = execute_query("""
                SELECT COUNT(DISTINCT student_id) as count
                FROM fee_accounts 
                WHERE academic_year = YEAR(GETDATE()) 
                AND total_arrears > 1000
            """)
            fee_defaulters = defaulters_result[0]['count'] if defaulters_result else 0
            
            stats = {
                'total_students': total_students,
                'total_teachers': total_teachers,
                'total_classes': total_classes,
                'total_staff': 0,
                'today_attendance_percentage': float(today_attendance_percentage),
                'fee_defaulters': fee_defaulters,
                'today_date': None,
                'current_year': 2025
            }
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting dashboard stats: {e}")
        # Return default stats in case of error
        return {
            'total_students': 0,
            'total_teachers': 0,
            'total_classes': 0,
            'total_staff': 0,
            'today_attendance_percentage': 0.0,
            'fee_defaulters': 0,
            'today_date': None,
            'current_year': 2025
        }


@router.get("/student-stats")
async def get_student_stats(current_user: dict = Depends(get_current_user)):
    """
    Get detailed student statistics
    
    Args:
        current_user: Current authenticated user
    
    Returns:
        Detailed student statistics by status
    """
    try:
        stats = execute_procedure_single('sp_GetStudentStats')
        
        if not stats:
            # Fallback query
            stats_result = execute_query("""
                SELECT 
                    COUNT(*) AS total_students,
                    SUM(CASE WHEN status = 'Active' THEN 1 ELSE 0 END) AS active_students,
                    SUM(CASE WHEN status = 'Discharged' THEN 1 ELSE 0 END) AS discharged_students,
                    SUM(CASE WHEN status = 'Suspended' THEN 1 ELSE 0 END) AS suspended_students,
                    SUM(CASE WHEN is_active = 1 THEN 1 ELSE 0 END) AS enabled_students
                FROM students
            """)
            stats = stats_result[0] if stats_result else {}
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting student stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get student statistics"
        )


@router.get("/staff-stats")
async def get_staff_stats(current_user: dict = Depends(get_current_user)):
    """
    Get staff statistics by role
    
    Args:
        current_user: Current authenticated user
    
    Returns:
        Staff statistics grouped by role
    """
    try:
        stats = execute_procedure('sp_GetStaffStats')
        
        if not stats:
            # Fallback query
            stats = execute_query("""
                SELECT 
                    r.role_name,
                    COUNT(*) AS count,
                    SUM(CASE WHEN u.is_active = 1 THEN 1 ELSE 0 END) AS active_count
                FROM users u
                INNER JOIN roles r ON u.role_id = r.role_id
                GROUP BY r.role_id, r.role_name
                ORDER BY r.role_name
            """)
        
        return stats
        
    except Exception as e:
        logger.error(f"Error getting staff stats: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to get staff statistics"
        )
