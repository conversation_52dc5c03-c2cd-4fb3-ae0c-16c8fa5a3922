-- Attendance management stored procedures
USE SchoolManagementDB;
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetStudentAttendance]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetStudentAttendance;
GO
CREATE PROCEDURE sp_GetStudentAttendance
    @student_id INT,
    @start_date DATE,
    @end_date DATE
AS
BEGIN
    SET NOCOUNT ON;
    SELECT a.attendance_id, a.student_id, s.full_name AS student_name,
           a.class_id, c.class_name, c.section,
           a.attendance_date, a.status, a.remarks,
           a.marked_by, u.full_name AS marked_by_name
    FROM attendance a
    INNER JOIN students s ON a.student_id = s.student_id
    INNER JOIN classes c ON a.class_id = c.class_id
    INNER JOIN users u ON a.marked_by = u.user_id
    WHERE a.student_id = @student_id
      AND a.attendance_date BETWEEN @start_date AND @end_date
    ORDER BY a.attendance_date DESC;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetClassAttendance]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetClassAttendance;
GO
CREATE PROCEDURE sp_GetClassAttendance
    @attendance_date DATE,
    @class_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT s.student_id, s.admission_number, s.full_name, s.full_name_urdu,
           ISNULL(a.status, 'Not Marked') AS status,
           a.remarks, a.attendance_id
    FROM students s
    LEFT JOIN attendance a ON s.student_id = a.student_id AND a.attendance_date = @attendance_date
    WHERE s.class_id = @class_id AND s.is_active = 1
    ORDER BY s.full_name;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetAttendancePercentage]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetAttendancePercentage;
GO
CREATE PROCEDURE sp_GetAttendancePercentage
    @student_id INT,
    @start_date DATE,
    @end_date DATE
AS
BEGIN
    SET NOCOUNT ON;
    SELECT 
        COUNT(*) AS total_days,
        SUM(CASE WHEN status = 'Present' THEN 1 ELSE 0 END) AS present_days,
        SUM(CASE WHEN status = 'Absent' THEN 1 ELSE 0 END) AS absent_days,
        SUM(CASE WHEN status = 'Leave' THEN 1 ELSE 0 END) AS leave_days,
        SUM(CASE WHEN status = 'Late' THEN 1 ELSE 0 END) AS late_days,
        CAST(SUM(CASE WHEN status = 'Present' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) AS attendance_percentage
    FROM attendance
    WHERE student_id = @student_id
      AND attendance_date BETWEEN @start_date AND @end_date;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetMonthlyReport]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetMonthlyReport;
GO
CREATE PROCEDURE sp_GetMonthlyReport
    @class_id INT,
    @year INT,
    @month INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT 
        s.student_id,
        s.admission_number,
        s.full_name,
        COUNT(a.attendance_id) AS total_days,
        SUM(CASE WHEN a.status = 'Present' THEN 1 ELSE 0 END) AS present_days,
        SUM(CASE WHEN a.status = 'Absent' THEN 1 ELSE 0 END) AS absent_days,
        SUM(CASE WHEN a.status = 'Leave' THEN 1 ELSE 0 END) AS leave_days,
        SUM(CASE WHEN a.status = 'Late' THEN 1 ELSE 0 END) AS late_days,
        CAST(SUM(CASE WHEN a.status = 'Present' THEN 1 ELSE 0 END) * 100.0 / NULLIF(COUNT(a.attendance_id), 0) AS DECIMAL(5,2)) AS attendance_percentage
    FROM students s
    LEFT JOIN attendance a ON s.student_id = a.student_id
        AND YEAR(a.attendance_date) = @year
        AND MONTH(a.attendance_date) = @month
    WHERE s.class_id = @class_id AND s.is_active = 1
    GROUP BY s.student_id, s.admission_number, s.full_name
    ORDER BY s.full_name;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_CheckHoliday]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_CheckHoliday;
GO
CREATE PROCEDURE sp_CheckHoliday
    @check_date DATE
AS
BEGIN
    SET NOCOUNT ON;
    SELECT COUNT(*) AS count FROM holidays WHERE holiday_date = @check_date AND is_active = 1;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetAbsentees]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetAbsentees;
GO
CREATE PROCEDURE sp_GetAbsentees
    @class_id INT,
    @attendance_date DATE
AS
BEGIN
    SET NOCOUNT ON;
    SELECT s.student_id, s.full_name, s.parent_phone, s.parent_email,
           a.status, a.remarks
    FROM students s
    INNER JOIN attendance a ON s.student_id = a.student_id
    WHERE s.class_id = @class_id
      AND a.attendance_date = @attendance_date
      AND a.status = 'Absent'
      AND s.is_active = 1;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetClassStudentsForAttendance]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetClassStudentsForAttendance;
GO
CREATE PROCEDURE sp_GetClassStudentsForAttendance
    @attendance_date DATE,
    @class_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        s.student_id,
        s.full_name,
        s.admission_number,
        a.attendance_id,
        a.status,
        a.remarks,
        a.arrival_time,
        a.departure_time,
        a.leave_type,
        a.is_edited,
        a.edited_at
    FROM students s
    LEFT JOIN attendance a ON s.student_id = a.student_id AND a.attendance_date = @attendance_date
    WHERE s.class_id = @class_id AND s.is_active = 1
    ORDER BY s.full_name;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_EditAttendance]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_EditAttendance;
GO
CREATE PROCEDURE sp_EditAttendance
    @attendance_id INT,
    @status NVARCHAR(50),
    @remarks NVARCHAR(1000) = NULL,
    @edited_by INT = NULL,
    @edit_reason NVARCHAR(500) = NULL,
    @arrival_time TIME = NULL,
    @departure_time TIME = NULL,
    @leave_type NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @old_status NVARCHAR(50), @old_remarks NVARCHAR(1000);
    SELECT @old_status = status, @old_remarks = remarks FROM attendance WHERE attendance_id = @attendance_id;
    IF @old_status IS NULL
    BEGIN
        SELECT 'NotFound' AS Result, 'Attendance record not found' AS Message;
        RETURN;
    END

    UPDATE attendance
    SET status = @status,
        remarks = @remarks,
        is_edited = 1,
        edited_by = @edited_by,
        edit_reason = @edit_reason,
        edited_at = GETDATE(),
        arrival_time = @arrival_time,
        departure_time = @departure_time,
        leave_type = @leave_type
    WHERE attendance_id = @attendance_id;

    INSERT INTO attendance_audit_log (attendance_id, action_type, old_status, new_status, old_remarks, new_remarks, changed_by, change_reason)
    VALUES (@attendance_id, 'UPDATE', @old_status, @status, @old_remarks, @remarks, @edited_by, @edit_reason);

    SELECT 'Success' AS Result, 'Attendance updated' AS Message;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetStudentAttendanceReport]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetStudentAttendanceReport;
GO
CREATE PROCEDURE sp_GetStudentAttendanceReport
    @student_id INT,
    @start_date DATE,
    @end_date DATE
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        s.student_id,
        s.full_name,
        c.class_name,
        COUNT(CASE WHEN a.status = 'Present' THEN 1 END) as present_days,
        COUNT(CASE WHEN a.status = 'Absent' THEN 1 END) as absent_days,
        COUNT(CASE WHEN a.status = 'Late' THEN 1 END) as late_days,
        COUNT(CASE WHEN a.status = 'Half-Day' THEN 1 END) as half_days,
        COUNT(CASE WHEN a.status IN ('Leave', 'Sick Leave', 'Casual Leave') THEN 1 END) as leave_days,
        COUNT(*) as total_days,
        CAST(COUNT(CASE WHEN a.status = 'Present' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) as attendance_percentage
    FROM students s
    INNER JOIN classes c ON s.class_id = c.class_id
    LEFT JOIN attendance a ON s.student_id = a.student_id AND a.attendance_date BETWEEN @start_date AND @end_date
    WHERE s.student_id = @student_id
    GROUP BY s.student_id, s.full_name, c.class_name;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetClassAttendanceSummary]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetClassAttendanceSummary;
GO
CREATE PROCEDURE sp_GetClassAttendanceSummary
    @class_id INT,
    @month INT,
    @year INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        c.class_id,
        c.class_name,
        COUNT(DISTINCT s.student_id) as total_students,
        COUNT(DISTINCT a.attendance_date) as total_working_days,
        AVG(CASE WHEN a.status = 'Present' THEN 100.0 ELSE 0 END) as average_attendance,
        (SELECT TOP 1 s2.full_name FROM students s2 INNER JOIN attendance a2 ON s2.student_id = a2.student_id WHERE s2.class_id = c.class_id AND MONTH(a2.attendance_date) = @month AND YEAR(a2.attendance_date) = @year GROUP BY s2.student_id, s2.full_name ORDER BY COUNT(CASE WHEN a2.status = 'Present' THEN 1 END) DESC) as best_attendance_student,
        (SELECT TOP 1 s2.full_name FROM students s2 INNER JOIN attendance a2 ON s2.student_id = a2.student_id WHERE s2.class_id = c.class_id AND MONTH(a2.attendance_date) = @month AND YEAR(a2.attendance_date) = @year GROUP BY s2.student_id, s2.full_name ORDER BY COUNT(CASE WHEN a2.status = 'Present' THEN 1 END) ASC) as worst_attendance_student
    FROM classes c
    INNER JOIN students s ON c.class_id = s.class_id
    LEFT JOIN attendance a ON s.student_id = a.student_id AND MONTH(a.attendance_date) = @month AND YEAR(a.attendance_date) = @year
    WHERE c.class_id = @class_id
    GROUP BY c.class_id, c.class_name;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetStudentMonthlyCalendar]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetStudentMonthlyCalendar;
GO
CREATE PROCEDURE sp_GetStudentMonthlyCalendar
    @student_id INT,
    @month INT,
    @year INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        attendance_date,
        status,
        remarks,
        arrival_time,
        departure_time,
        leave_type
    FROM attendance
    WHERE student_id = @student_id AND MONTH(attendance_date) = @month AND YEAR(attendance_date) = @year
    ORDER BY attendance_date;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_MarkAllPresent]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_MarkAllPresent;
GO
CREATE PROCEDURE sp_MarkAllPresent
    @class_id INT,
    @attendance_date DATE,
    @marked_by INT,
    @period NVARCHAR(50) = 'Morning'
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRANSACTION;
    BEGIN TRY
        DECLARE @student_id INT;
        DECLARE student_cursor CURSOR FOR SELECT student_id FROM students WHERE class_id = @class_id AND is_active = 1;
        OPEN student_cursor;
        FETCH NEXT FROM student_cursor INTO @student_id;
        WHILE @@FETCH_STATUS = 0
        BEGIN
            IF EXISTS (SELECT 1 FROM attendance WHERE student_id = @student_id AND attendance_date = @attendance_date)
            BEGIN
                UPDATE attendance SET status = 'Present', period = @period, updated_at = GETDATE() WHERE student_id = @student_id AND attendance_date = @attendance_date;
            END
            ELSE
            BEGIN
                INSERT INTO attendance (student_id, class_id, attendance_date, status, marked_by, period, attendance_type, created_at, updated_at)
                VALUES (@student_id, @class_id, @attendance_date, 'Present', @marked_by, @period, 'Daily Class Attendance', GETDATE(), GETDATE());
            END
            FETCH NEXT FROM student_cursor INTO @student_id;
        END
        CLOSE student_cursor;
        DEALLOCATE student_cursor;
        COMMIT TRANSACTION;
        SELECT 'Success' AS Result, 'Marked all present' AS Message;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0 ROLLBACK TRANSACTION;
        DECLARE @err NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@err, 16, 1);
    END CATCH
END
GO

PRINT 'Attendance procedures created';
GO
