"""
Database connection and utilities
"""
import pyodbc
from typing import Optional, List, Dict, Any
from contextlib import contextmanager
from config import get_connection_string, get_settings
import logging

logger = logging.getLogger(__name__)
settings = get_settings()


class DatabaseConnection:
    """Database connection manager"""
    
    def __init__(self):
        self.connection_string = get_connection_string()
    
    @contextmanager
    def get_connection(self):
        """Get database connection context manager"""
        conn = None
        try:
            conn = pyodbc.connect(self.connection_string)
            yield conn
        except pyodbc.Error as e:
            logger.error(f"Database connection error: {e}")
            raise
        finally:
            if conn:
                conn.close()
    
    @contextmanager
    def get_cursor(self):
        """Get database cursor context manager"""
        with self.get_connection() as conn:
            cursor = conn.cursor()
            try:
                yield cursor
                conn.commit()
            except Exception as e:
                conn.rollback()
                logger.error(f"Database operation error: {e}")
                raise
            finally:
                cursor.close()


# Global database instance
db = DatabaseConnection()


def execute_query(query: str, params: tuple = None) -> List[Dict[str, Any]]:
    """
    Execute SELECT query and return results as list of dictionaries
    
    Args:
        query: SQL query string
        params: Query parameters tuple
    
    Returns:
        List of dictionaries with column names as keys
    """
    with db.get_cursor() as cursor:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        columns = [column[0] for column in cursor.description]
        results = []
        
        for row in cursor.fetchall():
            results.append(dict(zip(columns, row)))
        
        return results


def execute_non_query(query: str, params: tuple = None) -> int:
    """
    Execute INSERT, UPDATE, DELETE query
    
    Args:
        query: SQL query string
        params: Query parameters tuple
    
    Returns:
        Number of affected rows
    """
    with db.get_cursor() as cursor:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)
        
        return cursor.rowcount


def execute_scalar(query: str, params: tuple = None) -> Any:
    """
    Execute query and return single value

    Args:
        query: SQL query string
        params: Query parameters tuple

    Returns:
        Single value from first row, first column
    """
    with db.get_cursor() as cursor:
        if params:
            cursor.execute(query, params)
        else:
            cursor.execute(query)

        row = cursor.fetchone()
        return row[0] if row else None


def execute_procedure(proc_name: str, params: Dict[str, Any] = None) -> List[Dict[str, Any]]:
    """
    Execute stored procedure and return results
    
    Args:
        proc_name: Stored procedure name
        params: Dictionary of parameter names and values
    
    Returns:
        List of dictionaries with results
    """
    with db.get_cursor() as cursor:
        if params:
            # Build parameter placeholders
            param_placeholders = ', '.join([f'@{key}=?' for key in params.keys()])
            query = f"EXEC {proc_name} {param_placeholders}"
            cursor.execute(query, tuple(params.values()))
        else:
            cursor.execute(f"EXEC {proc_name}")
        
        # Get all result sets
        results = []
        columns = [column[0] for column in cursor.description] if cursor.description else []
        
        if columns:
            for row in cursor.fetchall():
                results.append(dict(zip(columns, row)))
        
        return results


def execute_procedure_single(proc_name: str, params: Dict[str, Any] = None) -> Optional[Dict[str, Any]]:
    """
    Execute stored procedure and return single result
    
    Args:
        proc_name: Stored procedure name
        params: Dictionary of parameter names and values
    
    Returns:
        Single dictionary with result or None
    """
    results = execute_procedure(proc_name, params)
    return results[0] if results else None


def test_connection() -> bool:
    """
    Test database connection
    
    Returns:
        True if connection successful, False otherwise
    """
    try:
        with db.get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT 1")
            cursor.close()
            return True
    except Exception as e:
        logger.error(f"Connection test failed: {e}")
        return False


# Encryption utilities for sensitive data
def encrypt_data(data: str) -> bytes:
    """Encrypt sensitive data (CNIC, phone numbers)"""
    from cryptography.fernet import Fernet
    
    if not settings.ENCRYPTION_KEY:
        # Generate key if not exists (should be stored in env)
        key = Fernet.generate_key()
        logger.warning("Using generated encryption key. Set ENCRYPTION_KEY in .env for production!")
    else:
        key = settings.ENCRYPTION_KEY.encode()
    
    f = Fernet(key)
    return f.encrypt(data.encode())


def decrypt_data(encrypted_data: bytes) -> str:
    """Decrypt sensitive data"""
    from cryptography.fernet import Fernet
    
    if not settings.ENCRYPTION_KEY:
        logger.error("ENCRYPTION_KEY not set!")
        return ""
    
    f = Fernet(settings.ENCRYPTION_KEY.encode())
    return f.decrypt(encrypted_data).decode()

