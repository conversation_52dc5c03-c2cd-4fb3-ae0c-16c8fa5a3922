-- =============================================
-- School Management System - Database Creation
-- =============================================

USE master;
GO

-- Drop database if exists
IF EXISTS (SELECT name FROM sys.databases WHERE name = 'SchoolManagementDB')
BEGIN
    ALTER DATABASE SchoolManagementDB SET SINGLE_USER WITH ROLLBACK IMMEDIATE;
    DROP DATABASE SchoolManagementDB;
END
GO

-- Create database
CREATE DATABASE SchoolManagementDB;
GO

USE SchoolManagementDB;
GO

-- Set database options
ALTER DATABASE SchoolManagementDB SET RECOVERY SIMPLE;
ALTER DATABASE SchoolManagementDB SET AUTO_SHRINK OFF;
ALTER DATABASE SchoolManagementDB SET AUTO_UPDATE_STATISTICS ON;
GO

PRINT 'Database SchoolManagementDB created successfully';
GO

