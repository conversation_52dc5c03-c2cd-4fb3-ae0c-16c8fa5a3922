-- =============================================
-- School Management System - Table Schemas
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Roles Table
-- =============================================
CREATE TABLE roles (
    role_id INT IDENTITY(1,1) PRIMARY KEY,
    role_name NVARCHAR(50) NOT NULL UNIQUE,
    role_name_urdu NVARCHAR(100),
    description NVARCHAR(255),
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);
GO

-- =============================================
-- Users Table
-- =============================================
CREATE TABLE users (
    user_id INT IDENTITY(1,1) PRIMARY KEY,
    username NVARCHAR(50) NOT NULL UNIQUE,
    password_hash NVARCHAR(255) NOT NULL,
    full_name NVARCHAR(100) NOT NULL,
    full_name_urdu NVARCHAR(200),
    email NVARCHAR(100) UNIQUE,
    phone NVARCHAR(20),
    cnic VARBINARY(256), -- Encrypted
    role_id INT NOT NULL,
    is_active BIT DEFAULT 1,
    last_login DATETIME2,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_Users_Roles FOREIGN KEY (role_id) REFERENCES roles(role_id)
);
GO

CREATE INDEX IX_Users_Username ON users(username);
CREATE INDEX IX_Users_Role ON users(role_id);
GO

-- =============================================
-- Classes Table
-- =============================================
CREATE TABLE classes (
    class_id INT IDENTITY(1,1) PRIMARY KEY,
    class_name NVARCHAR(50) NOT NULL,
    class_name_urdu NVARCHAR(100),
    section NVARCHAR(10),
    academic_year INT NOT NULL,
    class_teacher_id INT,
    capacity INT DEFAULT 40,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_Classes_Teacher FOREIGN KEY (class_teacher_id) REFERENCES users(user_id),
    CONSTRAINT UQ_Class_Section_Year UNIQUE (class_name, section, academic_year)
);
GO

CREATE INDEX IX_Classes_AcademicYear ON classes(academic_year);
GO

-- =============================================
-- Students Table
-- =============================================
CREATE TABLE students (
    student_id INT IDENTITY(1,1) PRIMARY KEY,
    admission_number NVARCHAR(20) NOT NULL UNIQUE,
    full_name NVARCHAR(100) NOT NULL,
    full_name_urdu NVARCHAR(200),
    father_name NVARCHAR(100),
    father_name_urdu NVARCHAR(200),
    date_of_birth DATE,
    gender NVARCHAR(10),
    cnic VARBINARY(256), -- Encrypted (for older students)
    b_form NVARCHAR(20),
    class_id INT,
    admission_date DATE DEFAULT CAST(GETDATE() AS DATE),
    discharge_date DATE,
    status NVARCHAR(20) DEFAULT 'Active', -- Active, Discharged, Promoted, Archived
    parent_phone VARBINARY(256), -- Encrypted
    parent_email NVARCHAR(100),
    address NVARCHAR(500),
    address_urdu NVARCHAR(1000),
    photo_url NVARCHAR(255),
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_Students_Classes FOREIGN KEY (class_id) REFERENCES classes(class_id)
);
GO

CREATE INDEX IX_Students_AdmissionNumber ON students(admission_number);
CREATE INDEX IX_Students_Class ON students(class_id);
CREATE INDEX IX_Students_Status ON students(status);
GO

-- =============================================
-- Staff Table
-- =============================================
CREATE TABLE staff (
    staff_id INT IDENTITY(1,1) PRIMARY KEY,
    user_id INT NOT NULL,
    employee_code NVARCHAR(20) NOT NULL UNIQUE,
    designation NVARCHAR(100),
    designation_urdu NVARCHAR(200),
    department NVARCHAR(100),
    joining_date DATE,
    salary DECIMAL(18,2),
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_Staff_Users FOREIGN KEY (user_id) REFERENCES users(user_id)
);
GO

CREATE INDEX IX_Staff_EmployeeCode ON staff(employee_code);
GO

-- =============================================
-- Subjects Table
-- =============================================
CREATE TABLE subjects (
    subject_id INT IDENTITY(1,1) PRIMARY KEY,
    subject_name NVARCHAR(100) NOT NULL,
    subject_name_urdu NVARCHAR(200),
    subject_code NVARCHAR(20) UNIQUE,
    total_marks INT DEFAULT 100,
    passing_marks INT DEFAULT 40,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);
GO

-- =============================================
-- Class Subjects Mapping
-- =============================================
CREATE TABLE class_subjects (
    class_subject_id INT IDENTITY(1,1) PRIMARY KEY,
    class_id INT NOT NULL,
    subject_id INT NOT NULL,
    teacher_id INT,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_ClassSubjects_Classes FOREIGN KEY (class_id) REFERENCES classes(class_id),
    CONSTRAINT FK_ClassSubjects_Subjects FOREIGN KEY (subject_id) REFERENCES subjects(subject_id),
    CONSTRAINT FK_ClassSubjects_Teacher FOREIGN KEY (teacher_id) REFERENCES users(user_id),
    CONSTRAINT UQ_Class_Subject UNIQUE (class_id, subject_id)
);
GO

-- =============================================
-- Holidays Table
-- =============================================
CREATE TABLE holidays (
    holiday_id INT IDENTITY(1,1) PRIMARY KEY,
    holiday_name NVARCHAR(100) NOT NULL,
    holiday_name_urdu NVARCHAR(200),
    holiday_date DATE NOT NULL,
    holiday_type NVARCHAR(50), -- Public, School Event, etc.
    description NVARCHAR(500),
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE()
);
GO

CREATE INDEX IX_Holidays_Date ON holidays(holiday_date);
GO

-- =============================================
-- Attendance Table
-- =============================================
CREATE TABLE attendance (
    attendance_id INT IDENTITY(1,1) PRIMARY KEY,
    student_id INT NOT NULL,
    class_id INT NOT NULL,
    attendance_date DATE NOT NULL,
    status NVARCHAR(10) NOT NULL, -- Present, Absent, Leave, Late
    marked_by INT NOT NULL,
    remarks NVARCHAR(500),
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_Attendance_Students FOREIGN KEY (student_id) REFERENCES students(student_id),
    CONSTRAINT FK_Attendance_Classes FOREIGN KEY (class_id) REFERENCES classes(class_id),
    CONSTRAINT FK_Attendance_MarkedBy FOREIGN KEY (marked_by) REFERENCES users(user_id),
    CONSTRAINT UQ_Student_Date UNIQUE (student_id, attendance_date)
);
GO

CREATE INDEX IX_Attendance_Date ON attendance(attendance_date);
CREATE INDEX IX_Attendance_Student ON attendance(student_id);
CREATE INDEX IX_Attendance_Class ON attendance(class_id);
GO

-- =============================================
-- Fee Structures Table
-- =============================================
CREATE TABLE fee_structures (
    fee_structure_id INT IDENTITY(1,1) PRIMARY KEY,
    class_id INT NOT NULL,
    academic_year INT NOT NULL,
    fee_type NVARCHAR(50) NOT NULL, -- Monthly, Admission, Annual, Exam, etc.
    fee_type_urdu NVARCHAR(100),
    amount DECIMAL(18,2) NOT NULL,
    due_day INT DEFAULT 10, -- Day of month when fee is due
    fine_per_day DECIMAL(18,2) DEFAULT 0,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_FeeStructures_Classes FOREIGN KEY (class_id) REFERENCES classes(class_id)
);
GO

CREATE INDEX IX_FeeStructures_Class ON fee_structures(class_id);
GO

-- =============================================
-- Fee Accounts Table (Student Ledger)
-- =============================================
CREATE TABLE fee_accounts (
    fee_account_id INT IDENTITY(1,1) PRIMARY KEY,
    student_id INT NOT NULL,
    academic_year INT NOT NULL,
    total_fee DECIMAL(18,2) DEFAULT 0,
    total_paid DECIMAL(18,2) DEFAULT 0,
    total_arrears DECIMAL(18,2) DEFAULT 0,
    total_fines DECIMAL(18,2) DEFAULT 0,
    balance DECIMAL(18,2) DEFAULT 0,
    last_payment_date DATE,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_FeeAccounts_Students FOREIGN KEY (student_id) REFERENCES students(student_id),
    CONSTRAINT UQ_Student_Year UNIQUE (student_id, academic_year)
);
GO

CREATE INDEX IX_FeeAccounts_Student ON fee_accounts(student_id);
GO

-- =============================================
-- Fee Transactions Table
-- =============================================
CREATE TABLE fee_transactions (
    transaction_id INT IDENTITY(1,1) PRIMARY KEY,
    fee_account_id INT NOT NULL,
    student_id INT NOT NULL,
    transaction_date DATE NOT NULL DEFAULT CAST(GETDATE() AS DATE),
    fee_month INT, -- 1-12 for monthly fees
    fee_type NVARCHAR(50) NOT NULL,
    amount DECIMAL(18,2) NOT NULL,
    fine_amount DECIMAL(18,2) DEFAULT 0,
    total_amount DECIMAL(18,2) NOT NULL,
    payment_method NVARCHAR(50), -- Cash, Bank, Online
    receipt_number NVARCHAR(50) UNIQUE,
    collected_by INT NOT NULL,
    remarks NVARCHAR(500),
    created_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_FeeTransactions_FeeAccounts FOREIGN KEY (fee_account_id) REFERENCES fee_accounts(fee_account_id),
    CONSTRAINT FK_FeeTransactions_Students FOREIGN KEY (student_id) REFERENCES students(student_id),
    CONSTRAINT FK_FeeTransactions_CollectedBy FOREIGN KEY (collected_by) REFERENCES users(user_id)
);
GO

CREATE INDEX IX_FeeTransactions_Student ON fee_transactions(student_id);
CREATE INDEX IX_FeeTransactions_Date ON fee_transactions(transaction_date);
CREATE INDEX IX_FeeTransactions_Receipt ON fee_transactions(receipt_number);
GO

-- =============================================
-- Exams Table
-- =============================================
CREATE TABLE exams (
    exam_id INT IDENTITY(1,1) PRIMARY KEY,
    exam_name NVARCHAR(100) NOT NULL,
    exam_name_urdu NVARCHAR(200),
    exam_type NVARCHAR(50) NOT NULL, -- First Term, Second Term, Final Term
    academic_year INT NOT NULL,
    start_date DATE,
    end_date DATE,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);
GO

CREATE INDEX IX_Exams_AcademicYear ON exams(academic_year);
GO

-- =============================================
-- Exam Results Table
-- =============================================
CREATE TABLE exam_results (
    result_id INT IDENTITY(1,1) PRIMARY KEY,
    exam_id INT NOT NULL,
    student_id INT NOT NULL,
    subject_id INT NOT NULL,
    obtained_marks DECIMAL(5,2),
    total_marks DECIMAL(5,2),
    grade NVARCHAR(5),
    remarks NVARCHAR(500),
    entered_by INT NOT NULL,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_ExamResults_Exams FOREIGN KEY (exam_id) REFERENCES exams(exam_id),
    CONSTRAINT FK_ExamResults_Students FOREIGN KEY (student_id) REFERENCES students(student_id),
    CONSTRAINT FK_ExamResults_Subjects FOREIGN KEY (subject_id) REFERENCES subjects(subject_id),
    CONSTRAINT FK_ExamResults_EnteredBy FOREIGN KEY (entered_by) REFERENCES users(user_id),
    CONSTRAINT UQ_Exam_Student_Subject UNIQUE (exam_id, student_id, subject_id)
);
GO

CREATE INDEX IX_ExamResults_Exam ON exam_results(exam_id);
CREATE INDEX IX_ExamResults_Student ON exam_results(student_id);
GO

-- =============================================
-- Audit Logs Table
-- =============================================
CREATE TABLE audit_logs (
    log_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    user_id INT,
    action NVARCHAR(100) NOT NULL,
    table_name NVARCHAR(100),
    record_id INT,
    old_values NVARCHAR(MAX),
    new_values NVARCHAR(MAX),
    ip_address NVARCHAR(50),
    user_agent NVARCHAR(500),
    created_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_AuditLogs_Users FOREIGN KEY (user_id) REFERENCES users(user_id)
);
GO

CREATE INDEX IX_AuditLogs_User ON audit_logs(user_id);
CREATE INDEX IX_AuditLogs_CreatedAt ON audit_logs(created_at);
CREATE INDEX IX_AuditLogs_TableName ON audit_logs(table_name);
GO

-- =============================================
-- Archives Table (For old session data)
-- =============================================
CREATE TABLE archives (
    archive_id INT IDENTITY(1,1) PRIMARY KEY,
    archive_type NVARCHAR(50) NOT NULL, -- Student, Attendance, Exam, Fee
    academic_year INT NOT NULL,
    data NVARCHAR(MAX) NOT NULL, -- JSON data
    archived_by INT NOT NULL,
    archived_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_Archives_ArchivedBy FOREIGN KEY (archived_by) REFERENCES users(user_id)
);
GO

CREATE INDEX IX_Archives_Type ON archives(archive_type);
CREATE INDEX IX_Archives_Year ON archives(academic_year);
GO

-- =============================================
-- Notifications Table
-- =============================================
CREATE TABLE notifications (
    notification_id BIGINT IDENTITY(1,1) PRIMARY KEY,
    recipient_type NVARCHAR(50) NOT NULL, -- Student, Parent, Staff
    recipient_id INT NOT NULL,
    notification_type NVARCHAR(50) NOT NULL, -- SMS, Email
    subject NVARCHAR(200),
    message NVARCHAR(MAX) NOT NULL,
    status NVARCHAR(20) DEFAULT 'Pending', -- Pending, Sent, Failed
    sent_at DATETIME2,
    error_message NVARCHAR(500),
    created_at DATETIME2 DEFAULT GETDATE()
);
GO

CREATE INDEX IX_Notifications_Status ON notifications(status);
CREATE INDEX IX_Notifications_CreatedAt ON notifications(created_at);
GO

PRINT 'All tables created successfully';
GO

