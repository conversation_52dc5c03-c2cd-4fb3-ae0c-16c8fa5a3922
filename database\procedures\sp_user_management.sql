/* Stored procedures for user management */
SET ANSI_NULLS ON
SET QUOTED_IDENTIFIER ON
GO

-- Get all active roles
IF OBJECT_ID('sp_GetAllRoles', 'P') IS NOT NULL
    DROP PROCEDURE sp_GetAllRoles
GO
CREATE PROCEDURE sp_GetAllRoles
AS
BEGIN
    SET NOCOUNT ON;
    SELECT role_id, role_name, role_name_urdu, description, is_active
    FROM roles
    WHERE is_active = 1
    ORDER BY role_name;
END
GO

-- Get all users (including role fields)
IF OBJECT_ID('sp_GetAllUsers', 'P') IS NOT NULL
    DROP PROCEDURE sp_GetAllUsers
GO
CREATE PROCEDURE sp_GetAllUsers
AS
BEGIN
    SET NOCOUNT ON;
    SELECT u.user_id, u.username, u.email, u.full_name, u.full_name_urdu,
           u.role_id, r.role_name, r.role_name_urdu, u.phone, u.is_active,
           u.last_login, u.created_at, u.updated_at
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    ORDER BY u.created_at DESC;
END
GO

-- Get a single user by id
IF OBJECT_ID('sp_GetUserById', 'P') IS NOT NULL
    DROP PROCEDURE sp_GetUserById
GO
CREATE PROCEDURE sp_GetUserById
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT u.user_id, u.username, u.email, u.full_name, u.full_name_urdu,
           u.role_id, r.role_name, r.role_name_urdu, u.phone, u.is_active,
           u.last_login, u.created_at, u.updated_at
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE u.user_id = @user_id;
END
GO

-- Check if username exists
IF OBJECT_ID('sp_CheckUsernameExists', 'P') IS NOT NULL
    DROP PROCEDURE sp_CheckUsernameExists
GO
CREATE PROCEDURE sp_CheckUsernameExists
    @username NVARCHAR(150)
AS
BEGIN
    SET NOCOUNT ON;
    IF EXISTS (SELECT 1 FROM users WHERE username = @username)
        SELECT 1 AS exists_flag;
END
GO

-- Check if email exists
IF OBJECT_ID('sp_CheckEmailExists', 'P') IS NOT NULL
    DROP PROCEDURE sp_CheckEmailExists
GO
CREATE PROCEDURE sp_CheckEmailExists
    @email NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    IF EXISTS (SELECT 1 FROM users WHERE email = @email)
        SELECT 1 AS exists_flag;
END
GO

-- Check if email exists for another user
IF OBJECT_ID('sp_CheckEmailExistsExceptUser', 'P') IS NOT NULL
    DROP PROCEDURE sp_CheckEmailExistsExceptUser
GO
CREATE PROCEDURE sp_CheckEmailExistsExceptUser
    @email NVARCHAR(255),
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    IF EXISTS (SELECT 1 FROM users WHERE email = @email AND user_id != @user_id)
        SELECT 1 AS exists_flag;
END
GO

-- Create user and return created user record
IF OBJECT_ID('sp_CreateUser', 'P') IS NOT NULL
    DROP PROCEDURE sp_CreateUser
GO
CREATE PROCEDURE sp_CreateUser
    @username NVARCHAR(150),
    @password_hash NVARCHAR(255),
    @email NVARCHAR(255),
    @full_name NVARCHAR(255),
    @full_name_urdu NVARCHAR(255) = NULL,
    @role_id INT,
    @phone NVARCHAR(50) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO users (username, password_hash, email, full_name, full_name_urdu, role_id, phone, is_active, created_at, updated_at)
    VALUES (@username, @password_hash, @email, @full_name, @full_name_urdu, @role_id, @phone, 1, GETDATE(), GETDATE());

    DECLARE @new_id INT = SCOPE_IDENTITY();

    SELECT u.user_id, u.username, u.email, u.full_name, u.full_name_urdu,
           u.role_id, r.role_name, r.role_name_urdu, u.phone, u.is_active,
           u.last_login, u.created_at, u.updated_at
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE u.user_id = @new_id;
END
GO

-- Update user fields (partial update)
IF OBJECT_ID('sp_UpdateUser', 'P') IS NOT NULL
    DROP PROCEDURE sp_UpdateUser
GO
CREATE PROCEDURE sp_UpdateUser
    @user_id INT,
    @email NVARCHAR(255) = NULL,
    @full_name NVARCHAR(255) = NULL,
    @full_name_urdu NVARCHAR(255) = NULL,
    @role_id INT = NULL,
    @phone NVARCHAR(50) = NULL,
    @is_active INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE users
    SET
        email = COALESCE(@email, email),
        full_name = COALESCE(@full_name, full_name),
        full_name_urdu = COALESCE(@full_name_urdu, full_name_urdu),
        role_id = COALESCE(@role_id, role_id),
        phone = COALESCE(@phone, phone),
        is_active = COALESCE(@is_active, is_active),
        updated_at = GETDATE()
    WHERE user_id = @user_id;

    SELECT u.user_id, u.username, u.email, u.full_name, u.full_name_urdu,
           u.role_id, r.role_name, r.role_name_urdu, u.phone, u.is_active,
           u.last_login, u.created_at, u.updated_at
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE u.user_id = @user_id;
END
GO

-- Get password hash for a user
IF OBJECT_ID('sp_GetPasswordHashByUserId', 'P') IS NOT NULL
    DROP PROCEDURE sp_GetPasswordHashByUserId
GO
CREATE PROCEDURE sp_GetPasswordHashByUserId
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT password_hash FROM users WHERE user_id = @user_id;
END
GO

-- Update password
IF OBJECT_ID('sp_UpdatePassword', 'P') IS NOT NULL
    DROP PROCEDURE sp_UpdatePassword
GO
CREATE PROCEDURE sp_UpdatePassword
    @user_id INT,
    @password_hash NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE users SET password_hash = @password_hash, updated_at = GETDATE() WHERE user_id = @user_id;
END
GO

-- Soft delete user
IF OBJECT_ID('sp_SoftDeleteUser', 'P') IS NOT NULL
    DROP PROCEDURE sp_SoftDeleteUser
GO
CREATE PROCEDURE sp_SoftDeleteUser
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE users SET is_active = 0, updated_at = GETDATE() WHERE user_id = @user_id;
END
GO
-- Stored procedures for user management
IF OBJECT_ID('dbo.sp_CreateUser', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_CreateUser;
GO

CREATE PROCEDURE dbo.sp_CreateUser
    @username NVARCHAR(150),
    @password_hash NVARCHAR(256),
    @email NVARCHAR(256),
    @full_name NVARCHAR(256),
    @full_name_urdu NVARCHAR(256) = NULL,
    @role_id INT,
    @phone NVARCHAR(50) = NULL
AS
BEGIN
    SET NOCOUNT ON;

    INSERT INTO users (username, password_hash, email, full_name, full_name_urdu, role_id, phone, is_active, created_at)
    VALUES (@username, @password_hash, @email, @full_name, @full_name_urdu, @role_id, @phone, 1, GETDATE());

    DECLARE @new_id INT = SCOPE_IDENTITY();

    SELECT u.user_id, u.username, u.email, u.full_name, u.full_name_urdu,
           u.role_id, r.role_name, r.role_name_urdu, u.phone, u.is_active,
           u.last_login, u.created_at, u.updated_at
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE u.user_id = @new_id;
END
GO
