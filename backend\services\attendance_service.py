"""
Attendance Service - Business logic for attendance management
"""
from typing import List, Dict, Optional
from datetime import date, datetime, timedelta
from decimal import Decimal
from database import execute_procedure, execute_procedure_single, execute_query, execute_non_query, execute_scalar
from models import (
    AttendanceMarkRequest, AttendanceResponse, AttendanceSummaryResponse,
    AttendanceEditRequest, StudentAttendanceReport, ClassAttendanceSummary
)
import logging

logger = logging.getLogger(__name__)


class AttendanceService:
    """Service class for attendance operations"""

    @staticmethod
    def get_attendance_value(status: str) -> float:
        """
        Get attendance value based on status

        Args:
            status: Attendance status

        Returns:
            Attendance value (1.0 = full day, 0.5 = half day, 0.0 = absent)
        """
        status_values = {
            'Present': 1.0,
            'Late': 1.0,
            'Holiday': 1.0,
            'Half-Day': 0.5,
            'Short Leave': 0.5,
            'Absent': 0.0,
            'Leave': 0.0,
            'Sick Leave': 0.0,
            'Casual Leave': 0.0
        }
        return status_values.get(status, 1.0)

    @staticmethod
    def mark_attendance(request: AttendanceMarkRequest, marked_by: int) -> Dict:
        """
        Mark attendance for a student
        
        Args:
            request: Attendance mark request
            marked_by: User ID who is marking attendance
        
        Returns:
            Result dictionary
        """
        params = {
            'student_id': request.student_id,
            'class_id': request.class_id,
            'attendance_date': request.attendance_date,
            'status': request.status,
            'marked_by': marked_by,
            'remarks': request.remarks
        }
        
        result = execute_procedure_single('sp_MarkAttendance', params)
        return result
    
    @staticmethod
    def mark_bulk_attendance(class_id: int, attendance_date: date, 
                            attendance_records: List[Dict], marked_by: int) -> Dict:
        """
        Mark attendance for multiple students
        
        Args:
            class_id: Class ID
            attendance_date: Date of attendance
            attendance_records: List of attendance records
            marked_by: User ID who is marking attendance
        
        Returns:
            Result dictionary with success count
        """
        success_count = 0
        errors = []
        
        for record in attendance_records:
            try:
                params = {
                    'student_id': record['student_id'],
                    'class_id': class_id,
                    'attendance_date': attendance_date,
                    'status': record.get('status', 'Present'),
                    'marked_by': marked_by,
                    'remarks': record.get('remarks')
                }
                
                result = execute_procedure_single('sp_MarkAttendance', params)
                
                if result and result.get('Result') == 'Success':
                    success_count += 1
                else:
                    errors.append({
                        'student_id': record['student_id'],
                        'error': result.get('Message', 'Unknown error')
                    })
            except Exception as e:
                logger.error(f"Error marking attendance for student {record['student_id']}: {e}")
                errors.append({
                    'student_id': record['student_id'],
                    'error': str(e)
                })
        
        return {
            'success_count': success_count,
            'total_count': len(attendance_records),
            'errors': errors
        }
    
    @staticmethod
    def get_daily_summary(attendance_date: date, class_id: Optional[int] = None) -> List[AttendanceSummaryResponse]:
        """
        Get daily attendance summary
        
        Args:
            attendance_date: Date to get summary for
            class_id: Optional class ID filter
        
        Returns:
            List of attendance summaries
        """
        params = {
            'attendance_date': attendance_date,
            'class_id': class_id
        }
        
        results = execute_procedure('sp_GetDailyAttendanceSummary', params)
        return results
    
    @staticmethod
    def get_student_attendance(student_id: int, start_date: date, end_date: date) -> List[Dict]:
        """
        Get attendance records for a student
        
        Args:
            student_id: Student ID
            start_date: Start date
            end_date: End date
        
        Returns:
            List of attendance records
        """
        return execute_procedure('sp_GetStudentAttendance', {'student_id': student_id, 'start_date': start_date, 'end_date': end_date})
    
    @staticmethod
    def get_class_attendance(class_id: int, attendance_date: date) -> List[Dict]:
        """
        Get attendance for all students in a class on a specific date
        
        Args:
            class_id: Class ID
            attendance_date: Date
        
        Returns:
            List of attendance records
        """
        return execute_procedure('sp_GetClassAttendance', {'attendance_date': attendance_date, 'class_id': class_id})
    
    @staticmethod
    def get_attendance_percentage(student_id: int, start_date: date, end_date: date) -> Dict:
        """
        Calculate attendance percentage for a student
        
        Args:
            student_id: Student ID
            start_date: Start date
            end_date: End date
        
        Returns:
            Dictionary with attendance statistics
        """
        return execute_procedure_single('sp_GetAttendancePercentage', {'student_id': student_id, 'start_date': start_date, 'end_date': end_date})
    
    @staticmethod
    def get_monthly_report(class_id: int, year: int, month: int) -> List[Dict]:
        """
        Get monthly attendance report for a class
        
        Args:
            class_id: Class ID
            year: Year
            month: Month (1-12)
        
        Returns:
            List of student attendance statistics
        """
        return execute_procedure('sp_GetMonthlyReport', {'class_id': class_id, 'year': year, 'month': month})
    
    @staticmethod
    def check_holiday(check_date: date) -> bool:
        """
        Check if a date is a holiday
        
        Args:
            check_date: Date to check
        
        Returns:
            True if holiday, False otherwise
        """
        res = execute_procedure_single('sp_CheckHoliday', {'check_date': check_date})
        return res.get('count', 0) > 0 if res else False
    
    @staticmethod
    def get_absentees(class_id: int, attendance_date: date) -> List[Dict]:
        """
        Get list of absent students for notification

        Args:
            class_id: Class ID
            attendance_date: Date

        Returns:
            List of absent students with contact info
        """
        return execute_procedure('sp_GetAbsentees', {'class_id': class_id, 'attendance_date': attendance_date})

    @staticmethod
    def get_class_students_for_attendance(class_id: int, attendance_date: date) -> List[Dict]:
        """
        Get all students in a class with their existing attendance for the date

        Args:
            class_id: Class ID
            attendance_date: Date

        Returns:
            List of students with attendance status
        """
        return execute_procedure('sp_GetClassStudentsForAttendance', {'attendance_date': attendance_date, 'class_id': class_id})

    @staticmethod
    def edit_attendance(request: AttendanceEditRequest, edited_by: int) -> Dict:
        """
        Edit existing attendance record with audit trail

        Args:
            request: Edit request
            edited_by: User ID making the edit

        Returns:
            Result dictionary
        """
        try:
            params = {
                'attendance_id': request.attendance_id,
                'status': request.status,
                'remarks': request.remarks,
                'edited_by': edited_by,
                'edit_reason': request.edit_reason,
                'arrival_time': request.arrival_time,
                'departure_time': request.departure_time,
                'leave_type': request.leave_type
            }
            res = execute_procedure_single('sp_EditAttendance', params)
            if res and res.get('Result') == 'Success':
                return {'success': True, 'message': 'Attendance updated successfully'}
            return {'success': False, 'message': res.get('Message') if res else 'Failed to update attendance'}

        except Exception as e:
            logger.error(f"Error editing attendance: {e}")
            return {'success': False, 'message': str(e)}

    @staticmethod
    def get_student_attendance_report(student_id: int, start_date: date, end_date: date) -> Dict:
        """
        Get comprehensive attendance report for a student

        Args:
            student_id: Student ID
            start_date: Start date
            end_date: End date

        Returns:
            Detailed attendance report
        """
        query = """
            SELECT
                s.student_id,
                s.full_name,
                c.class_name,
                COUNT(CASE WHEN a.status = 'Present' THEN 1 END) as present_days,
                COUNT(CASE WHEN a.status = 'Absent' THEN 1 END) as absent_days,
                COUNT(CASE WHEN a.status = 'Late' THEN 1 END) as late_days,
                COUNT(CASE WHEN a.status = 'Half-Day' THEN 1 END) as half_days,
                COUNT(CASE WHEN a.status IN ('Leave', 'Sick Leave', 'Casual Leave') THEN 1 END) as leave_days,
                COUNT(*) as total_days,
                CAST(COUNT(CASE WHEN a.status = 'Present' THEN 1 END) * 100.0 / NULLIF(COUNT(*), 0) AS DECIMAL(5,2)) as attendance_percentage
            FROM students s
            INNER JOIN classes c ON s.class_id = c.class_id
            LEFT JOIN attendance a ON s.student_id = a.student_id
                AND a.attendance_date BETWEEN ? AND ?
            WHERE s.student_id = ?
            GROUP BY s.student_id, s.full_name, c.class_name
        """

        result = execute_query(query, (start_date, end_date, student_id))

        if result:
            report = result[0]
            # Calculate trend (simplified - compare with previous period)
            report['trend'] = 'Stable'
            report['status'] = 'Good' if report['attendance_percentage'] >= 90 else 'Warning' if report['attendance_percentage'] >= 75 else 'Poor'
            return report

        return None

    @staticmethod
    def get_class_attendance_summary(class_id: int, month: int, year: int) -> Dict:
        """
        Get class-wise attendance summary for a month

        Args:
            class_id: Class ID
            month: Month (1-12)
            year: Year

        Returns:
            Class attendance summary
        """
        query = """
            SELECT
                c.class_id,
                c.class_name,
                COUNT(DISTINCT s.student_id) as total_students,
                COUNT(DISTINCT a.attendance_date) as total_working_days,
                AVG(CASE WHEN a.status = 'Present' THEN 100.0 ELSE 0 END) as average_attendance,
                (SELECT TOP 1 s2.full_name
                 FROM students s2
                 INNER JOIN attendance a2 ON s2.student_id = a2.student_id
                 WHERE s2.class_id = c.class_id
                   AND MONTH(a2.attendance_date) = ?
                   AND YEAR(a2.attendance_date) = ?
                 GROUP BY s2.student_id, s2.full_name
                 ORDER BY COUNT(CASE WHEN a2.status = 'Present' THEN 1 END) DESC
                ) as best_attendance_student,
                (SELECT TOP 1 s2.full_name
                 FROM students s2
                 INNER JOIN attendance a2 ON s2.student_id = a2.student_id
                 WHERE s2.class_id = c.class_id
                   AND MONTH(a2.attendance_date) = ?
                   AND YEAR(a2.attendance_date) = ?
                 GROUP BY s2.student_id, s2.full_name
                 ORDER BY COUNT(CASE WHEN a2.status = 'Present' THEN 1 END) ASC
                ) as worst_attendance_student
            FROM classes c
            INNER JOIN students s ON c.class_id = s.class_id
            LEFT JOIN attendance a ON s.student_id = a.student_id
                AND MONTH(a.attendance_date) = ?
                AND YEAR(a.attendance_date) = ?
            WHERE c.class_id = ?
            GROUP BY c.class_id, c.class_name
        """

        result = execute_query(query, (month, year, month, year, month, year, class_id))

        if result:
            summary = result[0]
            summary['month'] = month
            summary['year'] = year
            return summary

        return None

    @staticmethod
    def get_student_monthly_calendar(student_id: int, month: int, year: int) -> List[Dict]:
        """
        Get student's attendance for entire month in calendar format

        Args:
            student_id: Student ID
            month: Month (1-12)
            year: Year

        Returns:
            List of daily attendance records
        """
        query = """
            SELECT
                attendance_date,
                status,
                remarks,
                arrival_time,
                departure_time,
                leave_type
            FROM attendance
            WHERE student_id = ?
                AND MONTH(attendance_date) = ?
                AND YEAR(attendance_date) = ?
            ORDER BY attendance_date
        """

        return execute_query(query, (student_id, month, year))

    @staticmethod
    def mark_all_present(class_id: int, attendance_date: date, marked_by: int, period: str = "Morning") -> Dict:
        """
        Mark all students in a class as present

        Args:
            class_id: Class ID
            attendance_date: Date
            marked_by: User ID
            period: Period (Morning, Afternoon, etc.)

        Returns:
            Result dictionary
        """
        try:
            # Get all active students in the class
            students_query = """
                SELECT student_id
                FROM students
                WHERE class_id = ? AND is_active = 1
            """
            students = execute_query(students_query, (class_id,))

            success_count = 0
            for student in students:
                try:
                    # Check if attendance already exists
                    check_query = """
                        SELECT attendance_id
                        FROM attendance
                        WHERE student_id = ? AND attendance_date = ?
                    """
                    existing = execute_query(check_query, (student['student_id'], attendance_date))

                    if existing:
                        # Update existing
                        update_query = """
                            UPDATE attendance
                            SET status = 'Present',
                                period = ?,
                                updated_at = GETDATE()
                            WHERE student_id = ? AND attendance_date = ?
                        """
                        execute_non_query(update_query, (period, student['student_id'], attendance_date))
                    else:
                        # Insert new
                        insert_query = """
                            INSERT INTO attendance
                            (student_id, class_id, attendance_date, status, marked_by, period, attendance_type)
                            VALUES (?, ?, ?, 'Present', ?, ?, 'Daily Class Attendance')
                        """
                        execute_non_query(insert_query, (
                            student['student_id'], class_id, attendance_date, marked_by, period
                        ))

                    success_count += 1
                except Exception as e:
                    logger.error(f"Error marking student {student['student_id']} present: {e}")

            return {
                'success': True,
                'message': f'Marked {success_count} students as present',
                'count': success_count
            }

        except Exception as e:
            logger.error(f"Error in mark_all_present: {e}")
            return {'success': False, 'message': str(e)}

