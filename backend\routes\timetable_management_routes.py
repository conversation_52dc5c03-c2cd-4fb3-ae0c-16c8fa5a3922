"""
Timetable Management Routes
Handles timetable assignments and teacher availability
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import Optional
from models import (ClassTimetableCreate, TeacherAvailabilityCheck)
from auth import require_admin, get_current_user
from services.timetable_service import TimetableService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/assign", status_code=status.HTTP_201_CREATED)
async def assign_class_period(
    timetable: ClassTimetableCreate,
    current_user: dict = Depends(require_admin)
):
    """
    Assign a teacher to a class period (Admin only)
    
    Args:
        timetable: Timetable assignment data
        current_user: Current authenticated user
    
    Returns:
        Success response with assignment details
    """
    try:
        result = TimetableService.assign_class_period(timetable, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return {
                "message": result.get('Message', 'Class period assigned successfully'),
                "timetable_id": result.get('TimetableId'),
                "assignment_details": result.get('AssignmentDetails', {})
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to assign class period')
            )
    except Exception as e:
        logger.error(f"Error assigning class period: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during class period assignment"
        )


@router.delete("/assign/{timetable_id}")
async def remove_class_period(
    timetable_id: int,
    current_user: dict = Depends(require_admin)
):
    """
    Remove a class period assignment (Admin only)
    
    Args:
        timetable_id: Timetable assignment ID to remove
        current_user: Current authenticated user
    
    Returns:
        Success response
    """
    try:
        result = TimetableService.remove_class_period(timetable_id, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return {"message": result.get('Message', 'Class period removed successfully')}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to remove class period')
            )
    except Exception as e:
        logger.error(f"Error removing class period: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during class period removal"
        )


@router.post("/check-availability", response_model=TeacherAvailabilityCheck)
async def check_teacher_availability(
    teacher_id: int,
    day_of_week: int,
    period_id: int,
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Check if a teacher is available for a specific period
    
    Args:
        teacher_id: Teacher ID to check
        day_of_week: Day of week (1=Monday, 7=Sunday)
        period_id: Period ID to check
        academic_year: Optional academic year
        current_user: Current authenticated user
    
    Returns:
        Teacher availability details
    """
    try:
        result = TimetableService.check_teacher_availability(
            teacher_id, day_of_week, period_id, academic_year
        )
        
        if result:
            return result
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Unable to check teacher availability"
            )
    except Exception as e:
        logger.error(f"Error checking teacher availability: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while checking teacher availability"
        )
