"""
Fee Calculation Routes
Handles fee calculations, fines, and arrears
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from models import SuccessResponse
from auth import get_current_user
from services.fee_service import FeeService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/calculate-fine/{student_id}", response_model=dict)
async def calculate_fine(
    student_id: int,
    fee_month: int = Query(..., ge=1, le=12),
    fee_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Calculate fine for a student for a specific month
    
    Args:
        student_id: Student ID
        fee_month: Fee month (1-12)
        fee_year: Fee year
        current_user: Current authenticated user
    
    Returns:
        Fine calculation details
    """
    try:
        result = FeeService.calculate_fine(student_id, fee_month, fee_year)
        
        if result:
            return result
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Unable to calculate fine for the specified student and month"
            )
    except Exception as e:
        logger.error(f"Error calculating fine: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during fine calculation"
        )


@router.post("/calculate-arrears", response_model=SuccessResponse)
async def calculate_arrears(
    academic_year: int = Query(...),
    current_month: int = Query(..., ge=1, le=12),
    current_user: dict = Depends(get_current_user)
):
    """
    Calculate arrears for all students
    
    Args:
        academic_year: Academic year
        current_month: Current month (1-12)
        current_user: Current authenticated user
    
    Returns:
        Success response with arrears calculation summary
    """
    try:
        result = FeeService.calculate_arrears(academic_year, current_month)
        
        if result and result.get('Result') == 'Success':
            return SuccessResponse(
                message=result.get('Message', 'Arrears calculated successfully'),
                data={
                    'total_students_processed': result.get('TotalStudentsProcessed', 0),
                    'total_arrears_amount': result.get('TotalArrearsAmount', 0)
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to calculate arrears')
            )
    except Exception as e:
        logger.error(f"Error calculating arrears: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during arrears calculation"
        )
