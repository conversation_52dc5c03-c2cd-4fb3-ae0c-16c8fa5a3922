// API helper that wraps fetch with auth header and base URL
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    SMS.apiUrl = function(path) {
        const cleanPath = path.startsWith('/') ? path : '/' + path;
        // keep behavior identical to previous implementation: append trailing slash for most
        if (cleanPath.endsWith('/')) return SMS.API_BASE + cleanPath;
        return SMS.API_BASE + cleanPath + '/';
    };

    SMS.apiCall = async function(endpoint, method = 'GET', body = null, opts = {}) {
        const headers = Object.assign({
            'Authorization': `Bearer ${SMS.authToken}`,
            'Content-Type': 'application/json'
        }, opts.headers || {});

        const options = { method, headers };
        if (body) options.body = JSON.stringify(body);

        const response = await fetch(SMS.API_BASE + endpoint, options);

        if (!response.ok) {
            // Handle authentication errors
            if (response.status === 401) {
                console.log('[API] Authentication failed, clearing token and showing login');
                if (typeof SMS.clearAuthData === 'function') {
                    SMS.clearAuthData();
                }
                if (typeof SMS.showLoginForm === 'function') {
                    SMS.showLoginForm();
                }
            }

            let err = null;
            try { err = await response.json(); } catch(e) { err = { detail: response.statusText }; }
            const message = err?.detail || 'API call failed';
            throw new Error(message);
        }

        return response.json();
    };

})(window);
