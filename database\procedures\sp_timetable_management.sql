-- Timetable Management Stored Procedures
USE SchoolManagementDB;
GO

-- =============================================
-- Get All School Config
-- =============================================
IF OBJECT_ID('sp_Config_GetAll', 'P') IS NOT NULL DROP PROCEDURE sp_Config_GetAll;
GO
CREATE PROCEDURE sp_Config_GetAll
AS
BEGIN
    SET NOCOUNT ON;
    SELECT config_id, config_key, config_value, description, updated_at
    FROM school_config
    ORDER BY config_key;
END
GO

-- =============================================
-- Get School Config by Key
-- =============================================
IF OBJECT_ID('sp_Config_GetByKey', 'P') IS NOT NULL DROP PROCEDURE sp_Config_GetByKey;
GO
CREATE PROCEDURE sp_Config_GetByKey
    @config_key NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;
    SELECT config_id, config_key, config_value, description, updated_at
    FROM school_config
    WHERE config_key = @config_key;
END
GO

-- =============================================
-- Create or Update School Config
-- =============================================
IF OBJECT_ID('sp_Config_Upsert', 'P') IS NOT NULL DROP PROCEDURE sp_Config_Upsert;
GO
CREATE PROCEDURE sp_Config_Upsert
    @config_key NVARCHAR(100),
    @config_value NVARCHAR(MAX),
    @description NVARCHAR(500) = NULL,
    @updated_by INT
AS
BEGIN
    SET NOCOUNT ON;
    IF EXISTS (SELECT 1 FROM school_config WHERE config_key = @config_key)
    BEGIN
        UPDATE school_config
        SET config_value = @config_value, description = @description, updated_by = @updated_by, updated_at = GETDATE()
        WHERE config_key = @config_key;
    END
    ELSE
    BEGIN
        INSERT INTO school_config (config_key, config_value, description, updated_by)
        VALUES (@config_key, @config_value, @description, @updated_by);
    END
    SELECT 'Success' AS Result, 'Configuration saved' AS Message;
END
GO

-- =============================================
-- Get Period Templates
-- =============================================
IF OBJECT_ID('sp_PeriodTemplate_GetAll', 'P') IS NOT NULL DROP PROCEDURE sp_PeriodTemplate_GetAll;
GO
CREATE PROCEDURE sp_PeriodTemplate_GetAll
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT template_id, period_number, period_name,
           CONVERT(VARCHAR(8), start_time, 108) as start_time,
           CONVERT(VARCHAR(8), end_time, 108) as end_time,
           is_teaching_period, academic_year, is_active
    FROM period_templates
    WHERE academic_year = @academic_year AND is_active = 1
    ORDER BY period_number;
END
GO

-- =============================================
-- Create Period Template
-- =============================================
IF OBJECT_ID('sp_PeriodTemplate_Create', 'P') IS NOT NULL DROP PROCEDURE sp_PeriodTemplate_Create;
GO
CREATE PROCEDURE sp_PeriodTemplate_Create
    @period_number INT,
    @period_name NVARCHAR(100),
    @start_time TIME,
    @end_time TIME,
    @is_teaching_period BIT = 1,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO period_templates (period_number, period_name, start_time, end_time, is_teaching_period, academic_year)
    VALUES (@period_number, @period_name, @start_time, @end_time, @is_teaching_period, @academic_year);
    SELECT SCOPE_IDENTITY() AS template_id;
END
GO

-- =============================================
-- Update Period Template
-- =============================================
IF OBJECT_ID('sp_PeriodTemplate_Update', 'P') IS NOT NULL DROP PROCEDURE sp_PeriodTemplate_Update;
GO
CREATE PROCEDURE sp_PeriodTemplate_Update
    @template_id INT,
    @period_number INT,
    @period_name NVARCHAR(100),
    @start_time TIME,
    @end_time TIME,
    @is_teaching_period BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE period_templates
    SET period_number = @period_number, period_name = @period_name, start_time = @start_time,
        end_time = @end_time, is_teaching_period = @is_teaching_period, updated_at = GETDATE()
    WHERE template_id = @template_id;
    SELECT 'Success' AS Result, 'Template updated' AS Message;
END
GO

-- =============================================
-- Delete Period Template (Soft Delete)
-- =============================================
IF OBJECT_ID('sp_PeriodTemplate_Delete', 'P') IS NOT NULL DROP PROCEDURE sp_PeriodTemplate_Delete;
GO
CREATE PROCEDURE sp_PeriodTemplate_Delete
    @template_id INT
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE period_templates SET is_active = 0 WHERE template_id = @template_id;
    SELECT 'Success' AS Result, 'Template deleted' AS Message;
END
GO

-- =============================================
-- Get Classes for Timetable Grid
-- =============================================
IF OBJECT_ID('sp_Timetable_GetClasses', 'P') IS NOT NULL DROP PROCEDURE sp_Timetable_GetClasses;
GO
CREATE PROCEDURE sp_Timetable_GetClasses
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT class_id, class_name, section
    FROM classes
    WHERE academic_year = @academic_year AND is_active = 1
    ORDER BY class_name, section;
END
GO

-- =============================================
-- Check Period Exists for Class
-- =============================================
IF OBJECT_ID('sp_Timetable_CheckPeriodExists', 'P') IS NOT NULL DROP PROCEDURE sp_Timetable_CheckPeriodExists;
GO
CREATE PROCEDURE sp_Timetable_CheckPeriodExists
    @class_id INT,
    @day_of_week INT,
    @period_number INT,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT timetable_id FROM class_timetable
    WHERE class_id = @class_id AND day_of_week = @day_of_week
      AND period_number = @period_number AND academic_year = @academic_year AND is_active = 1;
END
GO

-- =============================================
-- Assign Class Period
-- =============================================
IF OBJECT_ID('sp_Timetable_AssignPeriod', 'P') IS NOT NULL DROP PROCEDURE sp_Timetable_AssignPeriod;
GO
CREATE PROCEDURE sp_Timetable_AssignPeriod
    @class_id INT,
    @subject_id INT = NULL,
    @teacher_id INT = NULL,
    @day_of_week INT,
    @period_number INT,
    @start_time TIME,
    @end_time TIME,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO class_timetable (class_id, subject_id, teacher_id, day_of_week, period_number, start_time, end_time, academic_year)
    VALUES (@class_id, @subject_id, @teacher_id, @day_of_week, @period_number, @start_time, @end_time, @academic_year);
    SELECT SCOPE_IDENTITY() AS timetable_id;
END
GO

-- =============================================
-- Update Class Period
-- =============================================
IF OBJECT_ID('sp_Timetable_UpdatePeriod', 'P') IS NOT NULL DROP PROCEDURE sp_Timetable_UpdatePeriod;
GO
CREATE PROCEDURE sp_Timetable_UpdatePeriod
    @timetable_id INT,
    @teacher_id INT = NULL,
    @subject_id INT = NULL,
    @start_time TIME = NULL,
    @end_time TIME = NULL
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE class_timetable
    SET teacher_id = @teacher_id, subject_id = @subject_id, start_time = @start_time, end_time = @end_time, updated_at = GETDATE()
    WHERE timetable_id = @timetable_id;
    SELECT 'Success' AS Result, 'Period updated' AS Message;
END
GO

-- =============================================
-- Remove Class Period (Soft Delete)
-- =============================================
IF OBJECT_ID('sp_Timetable_RemovePeriod', 'P') IS NOT NULL DROP PROCEDURE sp_Timetable_RemovePeriod;
GO
CREATE PROCEDURE sp_Timetable_RemovePeriod
    @timetable_id INT
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE class_timetable SET is_active = 0 WHERE timetable_id = @timetable_id;
    SELECT 'Success' AS Result, 'Period removed' AS Message;
END
GO

PRINT 'Timetable management procedures created successfully';
GO

