/**
 * Attendance Management Functions - Part 2
 * Contains bulk attendance marking and individual attendance functions
 */

async function showStudentAttendanceList(classId, date) {
    try {
        // Use the new comprehensive endpoint that includes existing attendance
        const response = await fetch(SMS.apiUrl(`/attendance/class/${classId}/students-for-marking?attendance_date=${date}`), {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (!response.ok) {
            showError('Failed to load students');
            return;
        }

        const students = await response.json();

        if (students.length === 0) {
            showError('No students found in this class');
            return;
        }

        const studentsHtml = students.map(student => {
            const currentStatus = student.status || 'Present';
            const isEdited = student.is_edited ? '<span class="badge bg-warning ms-1">Edited</span>' : '';

            return `
            <tr>
                <td>${student.admission_number || student.roll_number || 'N/A'}</td>
                <td>${student.full_name} ${isEdited}</td>
                <td>
                    <select class="form-control form-control-sm attendance-status" data-student-id="${student.student_id}">
                        <option value="Present" ${currentStatus === 'Present' ? 'selected' : ''}>✅ Present (1.0)</option>
                        <option value="Absent" ${currentStatus === 'Absent' ? 'selected' : ''}>❌ Absent (0.0)</option>
                        <option value="Late" ${currentStatus === 'Late' ? 'selected' : ''}>⏰ Late (1.0)</option>
                        <option value="Half-Day" ${currentStatus === 'Half-Day' ? 'selected' : ''}>🕛 Half-Day (0.5)</option>
                        <option value="Short Leave" ${currentStatus === 'Short Leave' ? 'selected' : ''}>⏱️ Short Leave (0.5)</option>
                        <option value="Leave" ${currentStatus === 'Leave' ? 'selected' : ''}>📝 Leave (0.0)</option>
                        <option value="Sick Leave" ${currentStatus === 'Sick Leave' ? 'selected' : ''}>🏥 Sick Leave (0.0)</option>
                        <option value="Casual Leave" ${currentStatus === 'Casual Leave' ? 'selected' : ''}>📅 Casual Leave (0.0)</option>
                        <option value="Holiday" ${currentStatus === 'Holiday' ? 'selected' : ''}>🎉 Holiday (1.0)</option>
                    </select>
                </td>
                <td>
                    <input type="text" class="form-control form-control-sm attendance-remarks"
                           data-student-id="${student.student_id}"
                           placeholder="Remarks (optional)"
                           value="${student.remarks || ''}">
                </td>
            </tr>
        `}).join('');

        Swal.fire({
            title: `📋 Mark Attendance - ${date}`,
            html: `
                <div class="text-start">
                    <div class="alert alert-info d-flex justify-content-between align-items-center">
                        <div>
                            <strong>Total Students:</strong> ${students.length}
                        </div>
                        <div>
                            <span class="badge bg-success">Present: <span id="presentCount">0</span></span>
                            <span class="badge bg-danger ms-1">Absent: <span id="absentCount">0</span></span>
                        </div>
                    </div>

                    <!-- Bulk Actions -->
                    <div class="mb-3 p-3 bg-light rounded">
                        <h6 class="mb-2"><i class="fas fa-users"></i> Quick Actions</h6>
                        <div class="btn-group w-100" role="group">
                            <button type="button" class="btn btn-sm btn-success" onclick="markAllAs('Present')">
                                ✅ All Present
                            </button>
                            <button type="button" class="btn btn-sm btn-danger" onclick="markAllAs('Absent')">
                                ❌ All Absent
                            </button>
                            <button type="button" class="btn btn-sm btn-warning" onclick="markAllAs('Leave')">
                                📝 All Leave
                            </button>
                            <button type="button" class="btn btn-sm btn-info" onclick="markAllAs('Late')">
                                ⏰ All Late
                            </button>
                        </div>
                    </div>

                    <div class="table-responsive" style="max-height: 450px; overflow-y: auto;">
                        <table class="table table-sm table-striped table-hover">
                            <thead class="sticky-top bg-white shadow-sm">
                                <tr>
                                    <th style="width: 15%;">Roll #</th>
                                    <th style="width: 30%;">Student Name</th>
                                    <th style="width: 25%;">Status</th>
                                    <th style="width: 30%;">Remarks</th>
                                </tr>
                            </thead>
                            <tbody>
                                ${studentsHtml}
                            </tbody>
                        </table>
                    </div>
                </div>
            `,
            width: '900px',
            showCancelButton: true,
            confirmButtonText: '💾 Submit Attendance',
            cancelButtonText: 'Cancel',
            didOpen: () => {
                // Function to update present/absent counts (DEFINE FIRST)
                window.updateAttendanceCounts = () => {
                    let presentCount = 0;
                    let absentCount = 0;
                    document.querySelectorAll('.attendance-status').forEach(select => {
                        if (select.value === 'Present') presentCount++;
                        else if (select.value === 'Absent') absentCount++;
                    });
                    document.getElementById('presentCount').textContent = presentCount;
                    document.getElementById('absentCount').textContent = absentCount;
                };

                // Add global function for bulk marking
                window.markAllAs = (status) => {
                    document.querySelectorAll('.attendance-status').forEach(select => {
                        select.value = status;
                    });
                    // Explicitly reference window.updateAttendanceCounts to ensure it's accessible
                    if (window.updateAttendanceCounts) {
                        window.updateAttendanceCounts();
                    }
                };

                // Update counts when status changes
                document.querySelectorAll('.attendance-status').forEach(select => {
                    select.addEventListener('change', () => {
                        if (window.updateAttendanceCounts) {
                            window.updateAttendanceCounts();
                        }
                    });
                });

                // Initial count
                if (window.updateAttendanceCounts) {
                    window.updateAttendanceCounts();
                }
            },
            preConfirm: () => {
                const attendanceData = [];
                document.querySelectorAll('.attendance-status').forEach(select => {
                    const studentId = parseInt(select.dataset.studentId);
                    const remarksInput = document.querySelector(`.attendance-remarks[data-student-id="${studentId}"]`);
                    attendanceData.push({
                        student_id: studentId,
                        status: select.value,
                        remarks: remarksInput ? remarksInput.value : ''
                    });
                });
                return { classId, date, attendance: attendanceData };
            }
        }).then(async (result) => {
            if (result.isConfirmed) {
                await submitAttendance(result.value);
            }
        });
    } catch (error) {
        console.error('Error loading students:', error);
        showError('Error loading students');
    }
}

// Export functions to global scope
window.showStudentAttendanceList = showStudentAttendanceList;
