-- =============================================
-- Stored Procedures for Exam Management
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Procedure: sp_AddExamSubjects
-- Description: Add subjects to an exam
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_AddExamSubjects]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_AddExamSubjects;
GO

CREATE PROCEDURE sp_AddExamSubjects
    @exam_id INT,
    @subject_id INT,
    @class_id INT = NULL,
    @max_marks DECIMAL(5,2) = 100,
    @passing_marks DECIMAL(5,2) = 40,
    @theory_max DECIMAL(5,2) = NULL,
    @practical_max DECIMAL(5,2) = NULL,
    @oral_max DECIMAL(5,2) = NULL,
    @assignment_max DECIMAL(5,2) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if mapping already exists
        IF EXISTS (SELECT 1 FROM exam_subjects 
                   WHERE exam_id = @exam_id 
                   AND subject_id = @subject_id 
                   AND (class_id = @class_id OR (class_id IS NULL AND @class_id IS NULL)))
        BEGIN
            -- Update existing mapping
            UPDATE exam_subjects
            SET max_marks = @max_marks,
                passing_marks = @passing_marks,
                theory_max = @theory_max,
                practical_max = @practical_max,
                oral_max = @oral_max,
                assignment_max = @assignment_max,
                is_active = 1
            WHERE exam_id = @exam_id 
            AND subject_id = @subject_id 
            AND (class_id = @class_id OR (class_id IS NULL AND @class_id IS NULL));
            
            SELECT 'Exam subject mapping updated successfully' AS message;
        END
        ELSE
        BEGIN
            -- Insert new mapping
            INSERT INTO exam_subjects (exam_id, subject_id, class_id, max_marks, passing_marks, 
                                       theory_max, practical_max, oral_max, assignment_max)
            VALUES (@exam_id, @subject_id, @class_id, @max_marks, @passing_marks,
                    @theory_max, @practical_max, @oral_max, @assignment_max);
            
            SELECT 'Exam subject mapping added successfully' AS message;
        END
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_EnterExamMarks
-- Description: Enter or update marks for a student in a subject
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_EnterExamMarks]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_EnterExamMarks;
GO

CREATE PROCEDURE sp_EnterExamMarks
    @exam_id INT,
    @student_id INT,
    @subject_id INT,
    @obtained_marks DECIMAL(5,2),
    @total_marks DECIMAL(5,2),
    @theory_marks DECIMAL(5,2) = NULL,
    @practical_marks DECIMAL(5,2) = NULL,
    @oral_marks DECIMAL(5,2) = NULL,
    @assignment_marks DECIMAL(5,2) = NULL,
    @remarks NVARCHAR(500) = NULL,
    @entered_by INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @grade NVARCHAR(5);
        DECLARE @percentage DECIMAL(5,2);
        
        -- Calculate percentage and grade
        IF @total_marks > 0
        BEGIN
            SET @percentage = (@obtained_marks / @total_marks) * 100;
            
            -- Assign grade based on percentage
            SET @grade = CASE
                WHEN @percentage >= 90 THEN 'A+'
                WHEN @percentage >= 80 THEN 'A'
                WHEN @percentage >= 70 THEN 'B'
                WHEN @percentage >= 60 THEN 'C'
                WHEN @percentage >= 50 THEN 'D'
                WHEN @percentage >= 40 THEN 'E'
                ELSE 'F'
            END;
        END
        
        -- Check if result already exists
        IF EXISTS (SELECT 1 FROM exam_results 
                   WHERE exam_id = @exam_id 
                   AND student_id = @student_id 
                   AND subject_id = @subject_id)
        BEGIN
            -- Update existing result
            UPDATE exam_results
            SET obtained_marks = @obtained_marks,
                total_marks = @total_marks,
                theory_marks = @theory_marks,
                practical_marks = @practical_marks,
                oral_marks = @oral_marks,
                assignment_marks = @assignment_marks,
                grade = @grade,
                remarks = @remarks,
                updated_at = GETDATE(),
                is_draft = 0
            WHERE exam_id = @exam_id 
            AND student_id = @student_id 
            AND subject_id = @subject_id;
            
            SELECT 'Marks updated successfully' AS message, @grade AS grade, @percentage AS percentage;
        END
        ELSE
        BEGIN
            -- Insert new result
            INSERT INTO exam_results (exam_id, student_id, subject_id, obtained_marks, total_marks,
                                      theory_marks, practical_marks, oral_marks, assignment_marks,
                                      grade, remarks, entered_by, is_draft)
            VALUES (@exam_id, @student_id, @subject_id, @obtained_marks, @total_marks,
                    @theory_marks, @practical_marks, @oral_marks, @assignment_marks,
                    @grade, @remarks, @entered_by, 0);
            
            SELECT 'Marks entered successfully' AS message, @grade AS grade, @percentage AS percentage;
        END
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_CalculateStudentExamSummary
-- Description: Calculate overall exam result for a student
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_CalculateStudentExamSummary]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_CalculateStudentExamSummary;
GO

CREATE PROCEDURE sp_CalculateStudentExamSummary
    @exam_id INT,
    @student_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DECLARE @class_id INT;
        DECLARE @total_obtained DECIMAL(7,2);
        DECLARE @total_max DECIMAL(7,2);
        DECLARE @percentage DECIMAL(5,2);
        DECLARE @grade NVARCHAR(5);
        DECLARE @result_status NVARCHAR(20);
        DECLARE @passing_marks DECIMAL(5,2);
        DECLARE @failed_subjects INT;
        
        -- Get student's class
        SELECT @class_id = class_id FROM students WHERE student_id = @student_id;
        
        -- Calculate total marks
        SELECT 
            @total_obtained = SUM(ISNULL(obtained_marks, 0)),
            @total_max = SUM(ISNULL(total_marks, 0))
        FROM exam_results
        WHERE exam_id = @exam_id AND student_id = @student_id;
        
        -- Calculate percentage
        IF @total_max > 0
            SET @percentage = (@total_obtained / @total_max) * 100;
        ELSE
            SET @percentage = 0;
        
        -- Assign overall grade
        SET @grade = CASE
            WHEN @percentage >= 90 THEN 'A+'
            WHEN @percentage >= 80 THEN 'A'
            WHEN @percentage >= 70 THEN 'B'
            WHEN @percentage >= 60 THEN 'C'
            WHEN @percentage >= 50 THEN 'D'
            WHEN @percentage >= 40 THEN 'E'
            ELSE 'F'
        END;
        
        -- Check if student failed in any subject
        SELECT @failed_subjects = COUNT(*)
        FROM exam_results er
        INNER JOIN exam_subjects es ON er.exam_id = es.exam_id AND er.subject_id = es.subject_id
        WHERE er.exam_id = @exam_id 
        AND er.student_id = @student_id
        AND er.obtained_marks < es.passing_marks;
        
        -- Determine result status
        IF @failed_subjects > 0
            SET @result_status = 'Fail';
        ELSE IF @percentage >= 40
            SET @result_status = 'Pass';
        ELSE
            SET @result_status = 'Fail';
        
        -- Insert or update summary
        IF EXISTS (SELECT 1 FROM student_exam_summary 
                   WHERE exam_id = @exam_id AND student_id = @student_id)
        BEGIN
            UPDATE student_exam_summary
            SET total_obtained_marks = @total_obtained,
                total_max_marks = @total_max,
                percentage = @percentage,
                grade = @grade,
                result_status = @result_status,
                updated_at = GETDATE()
            WHERE exam_id = @exam_id AND student_id = @student_id;
        END
        ELSE
        BEGIN
            INSERT INTO student_exam_summary (exam_id, student_id, class_id, total_obtained_marks,
                                              total_max_marks, percentage, grade, result_status)
            VALUES (@exam_id, @student_id, @class_id, @total_obtained, @total_max, 
                    @percentage, @grade, @result_status);
        END
        
        SELECT 'Summary calculated successfully' AS message,
               @total_obtained AS total_obtained,
               @total_max AS total_max,
               @percentage AS percentage,
               @grade AS grade,
               @result_status AS result_status;
    END TRY
    BEGIN CATCH
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        RAISERROR(@ErrorMessage, 16, 1);
    END CATCH
END
GO

PRINT 'Exam management stored procedures created successfully!';
GO

