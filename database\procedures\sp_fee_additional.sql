-- Additional fee procedures to replace inline SQL in FeeService
USE SchoolManagementDB;
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_CreateFeeStructure]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_CreateFeeStructure;
GO
CREATE PROCEDURE sp_CreateFeeStructure
    @class_id INT,
    @academic_year INT,
    @fee_type NVARCHAR(100),
    @fee_type_urdu NVARCHAR(255),
    @amount DECIMAL(10,2),
    @due_day INT,
    @fine_per_day DECIMAL(10,2)
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO fee_structures (class_id, academic_year, fee_type, fee_type_urdu, amount, due_day, fine_per_day, is_active, created_at, updated_at)
    VALUES (@class_id, @academic_year, @fee_type, @fee_type_urdu, @amount, @due_day, @fine_per_day, 1, GETDATE(), GETDATE());
    SELECT SCOPE_IDENTITY() AS fee_structure_id;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetFeeAccount]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetFeeAccount;
GO
CREATE PROCEDURE sp_GetFeeAccount
    @student_id INT,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT fa.*, s.full_name AS student_name, s.admission_number
    FROM fee_accounts fa
    INNER JOIN students s ON fa.student_id = s.student_id
    WHERE fa.student_id = @student_id AND fa.academic_year = @academic_year;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_CreateFeeAccount]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_CreateFeeAccount;
GO
CREATE PROCEDURE sp_CreateFeeAccount
    @student_id INT,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @total_fee DECIMAL(18,2) = 0;
    SELECT @total_fee = ISNULL(SUM(fs.amount), 0)
    FROM fee_structures fs
    INNER JOIN students s ON fs.class_id = s.class_id
    WHERE s.student_id = @student_id AND fs.academic_year = @academic_year;

    INSERT INTO fee_accounts (student_id, academic_year, total_fee, balance, created_at, updated_at)
    VALUES (@student_id, @academic_year, @total_fee, @total_fee, GETDATE(), GETDATE());

    SELECT SCOPE_IDENTITY() AS fee_account_id;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetFeeTransactions]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetFeeTransactions;
GO
CREATE PROCEDURE sp_GetFeeTransactions
    @student_id INT,
    @academic_year INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    IF @academic_year IS NOT NULL
    BEGIN
        SELECT ft.*, s.full_name AS student_name, u.full_name AS collected_by_name
        FROM fee_transactions ft
        INNER JOIN students s ON ft.student_id = s.student_id
        INNER JOIN users u ON ft.collected_by = u.user_id
        INNER JOIN fee_accounts fa ON ft.fee_account_id = fa.fee_account_id
        WHERE ft.student_id = @student_id AND fa.academic_year = @academic_year
        ORDER BY ft.transaction_date DESC;
    END
    ELSE
    BEGIN
        SELECT ft.*, s.full_name AS student_name, u.full_name AS collected_by_name
        FROM fee_transactions ft
        INNER JOIN students s ON ft.student_id = s.student_id
        INNER JOIN users u ON ft.collected_by = u.user_id
        WHERE ft.student_id = @student_id
        ORDER BY ft.transaction_date DESC;
    END
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetDefaulters]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetDefaulters;
GO
CREATE PROCEDURE sp_GetDefaulters
    @academic_year INT,
    @min_arrears DECIMAL(18,2)
AS
BEGIN
    SET NOCOUNT ON;
    SELECT s.student_id, s.admission_number, s.full_name, s.parent_phone, s.parent_email,
           c.class_name, c.section,
           fa.total_fee, fa.total_paid, fa.total_arrears, fa.total_fines, fa.balance,
           fa.last_payment_date
    FROM fee_accounts fa
    INNER JOIN students s ON fa.student_id = s.student_id
    INNER JOIN classes c ON s.class_id = c.class_id
    WHERE fa.academic_year = @academic_year
        AND fa.balance >= @min_arrears
        AND s.is_active = 1
    ORDER BY fa.balance DESC;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetDailyCollection]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetDailyCollection;
GO
CREATE PROCEDURE sp_GetDailyCollection
    @collection_date DATE
AS
BEGIN
    SET NOCOUNT ON;
    SELECT 
        COUNT(*) AS total_transactions,
        SUM(amount) AS total_amount,
        SUM(fine_amount) AS total_fines,
        SUM(total_amount) AS grand_total,
        COUNT(DISTINCT student_id) AS unique_students
    FROM fee_transactions
    WHERE CAST(transaction_date AS DATE) = @collection_date;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetMonthlyCollection]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetMonthlyCollection;
GO
CREATE PROCEDURE sp_GetMonthlyCollection
    @year INT,
    @month INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT 
        COUNT(*) AS total_transactions,
        SUM(amount) AS total_amount,
        SUM(fine_amount) AS total_fines,
        SUM(total_amount) AS grand_total,
        COUNT(DISTINCT student_id) AS unique_students
    FROM fee_transactions
    WHERE YEAR(transaction_date) = @year AND MONTH(transaction_date) = @month;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetFeeStructures]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetFeeStructures;
GO
CREATE PROCEDURE sp_GetFeeStructures
    @class_id INT = NULL,
    @academic_year INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SELECT fs.*, c.class_name, c.section
    FROM fee_structures fs
    INNER JOIN classes c ON fs.class_id = c.class_id
    WHERE (@class_id IS NULL OR fs.class_id = @class_id)
      AND (@academic_year IS NULL OR fs.academic_year = @academic_year)
      AND fs.is_active = 1
    ORDER BY fs.academic_year DESC, fs.fee_type;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetClassFeeStructure]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetClassFeeStructure;
GO
CREATE PROCEDURE sp_GetClassFeeStructure
    @class_id INT,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        fee_structure_id,
        class_id,
        academic_year,
        monthly_fee,
        admission_fee,
        annual_fee,
        exam_fee,
        transport_fee,
        other_fee,
        is_active
    FROM class_fee_structure
    WHERE class_id = @class_id AND academic_year = @academic_year AND is_active = 1;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetStudentsForCollection]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetStudentsForCollection;
GO
CREATE PROCEDURE sp_GetStudentsForCollection
    @class_id INT,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        s.student_id,
        s.full_name,
        s.admission_number,
        ISNULL(
            (SELECT TOP 1 balance FROM fee_ledger WHERE student_id = s.student_id ORDER BY ledger_id DESC), 0
        ) AS current_balance,
        ISNULL(
            (SELECT SUM(outstanding_amount) FROM student_monthly_fees WHERE student_id = s.student_id AND academic_year = @academic_year), 0
        ) AS total_outstanding,
        (SELECT discount_type FROM student_fee_discounts WHERE student_id = s.student_id AND academic_year = @academic_year AND is_active = 1) AS discount_type
    FROM students s
    WHERE s.class_id = @class_id AND s.is_active = 1
    ORDER BY s.admission_number, s.full_name;
END
GO

PRINT 'Additional fee procedures created';
GO
