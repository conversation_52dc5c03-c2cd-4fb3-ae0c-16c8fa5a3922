// =============================================
// Timetable Management - School Settings & Period Templates
// =============================================

(function(window){
    const SMS = window.SMS || (window.SMS = {});

// Global variables for timetable management
let periodTemplates = [];
let schoolConfig = {};
let currentAcademicYear = new Date().getFullYear();

// =============================================
// School Settings Page
// =============================================

SMS.loadSchoolSettingsPage = async function() {
    const contentArea = document.getElementById('contentArea');
    
    contentArea.innerHTML = `
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-cog"></i> School Settings</h2>
            </div>

            <!-- School Configuration Card -->
            <div class="card mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-school"></i> School Configuration</h5>
                </div>
                <div class="card-body" id="schoolConfigContainer">
                    <div class="text-center py-3">
                        <div class="spinner-border"></div>
                    </div>
                </div>
            </div>

            <!-- Time Slot Generator Card -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-sliders-h"></i> Time Slot Generator</h5>
                    <div>
                        <button class="btn btn-light btn-sm" onclick="applyGeneratedTemplates()">
                            <i class="fas fa-save"></i> Generate & Save Templates
                        </button>
                    </div>
                </div>
                <div class="card-body" id="slotGeneratorContainer">
                    <div class="text-center py-3"><div class="spinner-border"></div></div>
                </div>
            </div>

            <!-- Period Templates Card -->
            <div class="card">
                <div class="card-header bg-success text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-clock"></i> Period Templates</h5>
                    <button class="btn btn-light btn-sm" onclick="showAddPeriodTemplateModal()">
                        <i class="fas fa-plus"></i> Add Period
                    </button>
                </div>
                <div class="card-body" id="periodTemplatesContainer">
                    <div class="text-center py-3">
                        <div class="spinner-border"></div>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    await loadSchoolConfiguration();
    await loadSlotGenerator();
    await loadPeriodTemplates();
}

async function loadSchoolConfiguration() {
    const container = document.getElementById('schoolConfigContainer');

    try {
        const response = await fetch(SMS.apiUrl('/timetable/config'), {
            headers: { 'Authorization': `Bearer ${SMS.authToken}` }
        });
        
        if (response.ok) {
            const configs = await response.json();
            schoolConfig = {};
            configs.forEach(c => { schoolConfig[c.config_key] = c.config_value; });
            
            displaySchoolConfig(configs);
        } else {
            container.innerHTML = `<div class="alert alert-warning">Unable to load configuration</div>`;
        }
    } catch (error) {
        console.error('Error:', error);
        container.innerHTML = `<div class="alert alert-danger">Error loading configuration</div>`;
    }
}

function displaySchoolConfig(configs) {
    const container = document.getElementById('schoolConfigContainer');
    
    const configGroups = {
        'School Info': ['school_name'],
        'School Timings': ['school_start_time', 'school_end_time'],
        'Period Settings': ['periods_per_day', 'period_duration', 'break_duration', 'lunch_duration']
    };
    
    let html = '<form id="schoolConfigForm">';
    
    for (const [groupName, keys] of Object.entries(configGroups)) {
        html += `<h6 class="mt-3 mb-3"><strong>${groupName}</strong></h6><div class="row">`;
        
        keys.forEach(key => {
            const config = configs.find(c => c.config_key === key) || { config_key: key, config_value: '', description: '' };
            const label = key.split('_').map(w => w.charAt(0).toUpperCase() + w.slice(1)).join(' ');
            const inputType = key.includes('time') ? 'time' : 'text';
            
            html += `
                <div class="col-md-6 mb-3">
                    <label class="form-label">${label}</label>
                    <input type="${inputType}" class="form-control" 
                           data-config-key="${key}" 
                           value="${config.config_value}"
                           placeholder="${config.description || ''}">
                </div>
            `;
        });
        
        html += '</div>';
    }
    
    html += `
        <div class="mt-3">
            <button type="button" class="btn btn-primary" onclick="saveSchoolConfig()">
                <i class="fas fa-save"></i> Save Configuration
            </button>
        </div>
    </form>`;
    
    container.innerHTML = html;
}

async function saveSchoolConfig() {
    const inputs = document.querySelectorAll('#schoolConfigForm input[data-config-key]');
    const updates = [];
    
    inputs.forEach(input => {
        updates.push({
            config_key: input.dataset.configKey,
            config_value: input.value
        });
    });
    
    try {
        for (const config of updates) {
            await fetch(SMS.apiUrl('/timetable/config'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(config)
            });
        }
        
        showSuccess('School configuration saved successfully!');
        await loadSchoolConfiguration();
    } catch (error) {
        console.error('Error:', error);
        showError('Failed to save configuration');
    }
}

async function loadPeriodTemplates() {
    const container = document.getElementById('periodTemplatesContainer');

    try {
        const response = await fetch(SMS.apiUrl(`/timetable/periods/templates?academic_year=${currentAcademicYear}`), {
            headers: { 'Authorization': `Bearer ${SMS.authToken}` }
        });
        
        if (response.ok) {
            periodTemplates = await response.json();
            displayPeriodTemplates(periodTemplates);
        } else {
            container.innerHTML = `<div class="alert alert-warning">No period templates found</div>`;
        }
    } catch (error) {
        console.error('Error:', error);
        container.innerHTML = `<div class="alert alert-danger">Error loading period templates</div>`;
    }
}

function displayPeriodTemplates(templates) {
    const container = document.getElementById('periodTemplatesContainer');
    
    if (templates.length === 0) {
        container.innerHTML = `
            <div class="alert alert-info">
                No period templates configured. Click "Add Period" to create your school's period structure.
            </div>
        `;
        return;
    }
    
    container.innerHTML = `
        <table class="table table-hover">
            <thead class="table-light">
                <tr>
                    <th>Period #</th>
                    <th>Name</th>
                    <th>Start Time</th>
                    <th>End Time</th>
                    <th>Duration</th>
                    <th>Type</th>
                    <th>Actions</th>
                </tr>
            </thead>
            <tbody>
                ${templates.map(t => {
                    const start = t.start_time.substring(0, 5);
                    const end = t.end_time.substring(0, 5);
                    const duration = calculateDuration(start, end);
                    const badge = t.is_teaching_period ? 'bg-success' : 'bg-warning';
                    const type = t.is_teaching_period ? 'Teaching' : 'Break';
                    
                    return `
                        <tr>
                            <td><strong>${t.period_number}</strong></td>
                            <td>${t.period_name}</td>
                            <td>${start}</td>
                            <td>${end}</td>
                            <td>${duration} min</td>
                            <td><span class="badge ${badge}">${type}</span></td>
                            <td>
                                <button class="btn btn-sm btn-primary" onclick='editPeriodTemplate(${JSON.stringify(t)})'>
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-sm btn-danger" onclick="deletePeriodTemplate(${t.template_id})">
                                    <i class="fas fa-trash"></i>
                                </button>
                            </td>
                        </tr>
                    `;
                }).join('')}
            </tbody>
        </table>
    `;
}

function calculateDuration(start, end) {
    const [startH, startM] = start.split(':').map(Number);
    const [endH, endM] = end.split(':').map(Number);
    return (endH * 60 + endM) - (startH * 60 + startM);
}

function showAddPeriodTemplateModal() {
    const html = `
        <div class="modal fade" id="periodTemplateModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title"><i class="fas fa-plus"></i> Add Period Template</h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <form id="periodTemplateForm">
                            <div class="mb-3">
                                <label class="form-label">Period Number</label>
                                <input type="number" class="form-control" id="periodNumber" min="1" required>
                            </div>
                            <div class="mb-3">
                                <label class="form-label">Period Name</label>
                                <input type="text" class="form-control" id="periodName" 
                                       placeholder="e.g., Period 1, Break, Lunch" required>
                            </div>
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">Start Time</label>
                                    <input type="time" class="form-control" id="periodStartTime" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label class="form-label">End Time</label>
                                    <input type="time" class="form-control" id="periodEndTime" required>
                                </div>
                            </div>
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="isTeachingPeriod" checked>
                                    <label class="form-check-label" for="isTeachingPeriod">
                                        Teaching Period (uncheck for breaks/lunch)
                                    </label>
                                </div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" onclick="savePeriodTemplate()">Save</button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', html);
    const modal = new bootstrap.Modal(document.getElementById('periodTemplateModal'));
    modal.show();
    
    document.getElementById('periodTemplateModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

async function savePeriodTemplate() {
    const form = document.getElementById('periodTemplateForm');
    if (!form.checkValidity()) {
        form.reportValidity();
        return;
    }
    
    const data = {
        period_number: parseInt(document.getElementById('periodNumber').value),
        period_name: document.getElementById('periodName').value,
        start_time: document.getElementById('periodStartTime').value + ':00',
        end_time: document.getElementById('periodEndTime').value + ':00',
        is_teaching_period: document.getElementById('isTeachingPeriod').checked,
        academic_year: currentAcademicYear
    };
    
    try {
        const response = await fetch(SMS.apiUrl('/timetable/periods/templates'), {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SMS.authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            showSuccess('Period template saved successfully!');
            bootstrap.Modal.getInstance(document.getElementById('periodTemplateModal')).hide();
            await loadPeriodTemplates();
        } else {
            const error = await response.json();
            showError(error.detail || 'Failed to save period template');
        }
    } catch (error) {
        console.error('Error:', error);
        showError('Failed to save period template');
    }
}

async function deletePeriodTemplate(templateId) {
    if (!confirm('Are you sure you want to delete this period template?')) return;

    try {
        const response = await fetch(SMS.apiUrl(`/timetable/periods/templates/${templateId}`), {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${SMS.authToken}` }
        });
        
        if (response.ok) {
            showSuccess('Period template deleted successfully!');
            await loadPeriodTemplates();
        } else {
            showError('Failed to delete period template');
        }
    } catch (error) {
        console.error('Error:', error);
        showError('Failed to delete period template');
    }
}

// =============================================
// Time Slot Generator - UI, preview, and save
// =============================================

let generatedTemplates = [];

function readInt(value, fallback = 0) {
    const n = parseInt(value, 10);
    return Number.isFinite(n) ? n : fallback;
}

function toTimeString(date) {
    return date.toTimeString().substring(0, 8);
}

function addMinutes(timeStr, minutes) {
    const [h, m] = timeStr.split(':').map(Number);
    const base = new Date();
    base.setHours(h, m, 0, 0);
    base.setMinutes(base.getMinutes() + minutes);
    return toTimeString(base);
}

async function loadSlotGenerator() {
    generatedTemplates = [];
    renderGeneratedTemplatesPreview();
}

function renderGeneratedTemplatesPreview() {
    const container = document.getElementById('slotGeneratorContainer');
    if (!container) return;

    const timeInput = (id, label, value) => `
        <div class="col-md-4 mb-3">
            <label class="form-label">${label}</label>
            <input type="time" class="form-control" id="${id}" value="${(value || '08:30:00').substring(0,5)}" onchange="generateTemplatesFromInputs()">
        </div>`;

    const numInput = (id, label, value, min=0, step=1) => `
        <div class="col-md-4 mb-3">
            <label class="form-label">${label}</label>
            <input type="number" class="form-control" id="${id}" value="${value}" min="${min}" step="${step}" onchange="generateTemplatesFromInputs()">
        </div>`;

    const cfg = schoolConfig || {};
    const html = `
        <div class="mb-3">
            <div class="row">
                ${timeInput('genStartTime', 'School Start Time', cfg.school_start_time || '08:30:00')}
                ${timeInput('genEndTime', 'School End Time', cfg.school_end_time || '14:30:00')}
                ${numInput('genPeriodsPerDay', 'Teaching Periods Per Day', cfg.periods_per_day || 8, 1)}
                ${numInput('genPeriodDuration', 'Period Duration (minutes)', cfg.period_duration || 40, 5)}
                ${numInput('genBreakDuration', 'Break Duration (minutes)', cfg.break_duration || 10, 0)}
                ${numInput('genLunchDuration', 'Lunch Duration (minutes)', cfg.lunch_duration || 30, 0)}
                ${numInput('genBreakAfter', 'Short Break After Period #', 2, 0)}
                ${numInput('genLunchAfter', 'Lunch After Period #', 4, 0)}
            </div>
        </div>
        <div class="table-responsive">
            <table class="table table-sm table-bordered">
                <thead class="table-light">
                    <tr>
                        <th style="width:120px">Period #</th>
                        <th>Name</th>
                        <th style="width:180px">Time</th>
                        <th>Type</th>
                    </tr>
                </thead>
                <tbody id="genPreviewBody"></tbody>
            </table>
        </div>
    `;
    container.innerHTML = html;

    if (!generatedTemplates || generatedTemplates.length === 0) {
        generateTemplatesFromInputs();
        return;
    }

    const tbody = document.getElementById('genPreviewBody');
    tbody.innerHTML = generatedTemplates.map(t => `
        <tr>
            <td><strong>${t.period_number || '-'}</strong></td>
            <td>${t.period_name}</td>
            <td>${t.start_time.substring(0,5)} - ${t.end_time.substring(0,5)}</td>
            <td>${t.is_teaching_period ? '<span class="badge bg-success">Teaching</span>' : '<span class="badge bg-warning">Break</span>'}</td>
        </tr>
    `).join('');
}

function generateTemplatesFromInputs() {
    const start = document.getElementById('genStartTime').value || (schoolConfig.school_start_time || '08:30:00');
    const end = document.getElementById('genEndTime').value || (schoolConfig.school_end_time || '14:30:00');
    const periodDuration = readInt(document.getElementById('genPeriodDuration').value || schoolConfig.period_duration, 40);
    const breakDuration = readInt(document.getElementById('genBreakDuration').value || schoolConfig.break_duration, 10);
    const lunchDuration = readInt(document.getElementById('genLunchDuration').value || schoolConfig.lunch_duration, 30);
    const breakAfter = readInt(document.getElementById('genBreakAfter').value, 2);
    const lunchAfter = readInt(document.getElementById('genLunchAfter').value, 4);
    const periodsPerDay = readInt(document.getElementById('genPeriodsPerDay').value || schoolConfig.periods_per_day, 8);
    const year = currentAcademicYear;

    let cursor = start.length === 5 ? `${start}:00` : start;
    const result = [];
    let periodNumber = 1;

    for (let i = 1; i <= periodsPerDay; i++) {
        if (breakDuration > 0 && i === breakAfter + 1) {
            const breakStart = cursor;
            const breakEnd = addMinutes(breakStart, breakDuration);
            result.push({ period_number: 0, period_name: 'Break', start_time: breakStart, end_time: breakEnd, is_teaching_period: false, academic_year: year });
            cursor = breakEnd;
        }

        if (lunchDuration > 0 && i === lunchAfter + 1) {
            const lunchStart = cursor;
            const lunchEnd = addMinutes(lunchStart, lunchDuration);
            result.push({ period_number: 0, period_name: 'Lunch', start_time: lunchStart, end_time: lunchEnd, is_teaching_period: false, academic_year: year });
            cursor = lunchEnd;
        }

        const pStart = cursor;
        const pEnd = addMinutes(pStart, periodDuration);
        result.push({ period_number: periodNumber, period_name: `Period ${periodNumber}`, start_time: pStart, end_time: pEnd, is_teaching_period: true, academic_year: year });
        cursor = pEnd;
        periodNumber += 1;
        if (end && pEnd > end) break;
    }

    generatedTemplates = result;
    // Re-render the preview table with the new data
    renderGeneratedTemplatesPreview();
}

async function applyGeneratedTemplates() {
    if (!generatedTemplates || generatedTemplates.length === 0) {
        showError('No generated templates to save');
        return;
    }
    try {
        const existingRes = await fetch(SMS.apiUrl(`/timetable/periods/templates?academic_year=${currentAcademicYear}`), { headers: { 'Authorization': `Bearer ${SMS.authToken}` } });
        const existing = existingRes.ok ? await existingRes.json() : [];

        for (const t of generatedTemplates) {
            if (t.is_teaching_period) {
                const match = existing.find(e => e.is_teaching_period && e.period_number === t.period_number);
                if (match) {
                    await fetch(SMS.apiUrl(`/timetable/periods/templates/${match.template_id}`), {
                        method: 'PUT',
                        headers: { 'Authorization': `Bearer ${SMS.authToken}`, 'Content-Type': 'application/json' },
                        body: JSON.stringify({
                            period_number: t.period_number,
                            period_name: t.period_name,
                            start_time: t.start_time,
                            end_time: t.end_time,
                            is_teaching_period: true,
                            academic_year: currentAcademicYear
                        })
                    });
                } else {
                    await fetch(SMS.apiUrl('/timetable/periods/templates'), {
                        method: 'POST',
                        headers: { 'Authorization': `Bearer ${SMS.authToken}`, 'Content-Type': 'application/json' },
                        body: JSON.stringify(t)
                    });
                }
            } else {
                await fetch(SMS.apiUrl('/timetable/periods/templates'), {
                    method: 'POST',
                    headers: { 'Authorization': `Bearer ${SMS.authToken}`, 'Content-Type': 'application/json' },
                    body: JSON.stringify(t)
                });
            }
        }

        showSuccess('Period templates generated and saved');
        await loadPeriodTemplates();
    } catch (error) {
        console.error('Error applying templates:', error);
        showError('Failed to apply generated templates');
    }
}

// ==================== BACKWARDS COMPATIBILITY ====================

window.loadSchoolSettingsPage = function(){ return SMS.loadSchoolSettingsPage(); };
window.loadSchoolConfig = loadSchoolConfiguration;
window.loadPeriodTemplates = loadPeriodTemplates;
window.showAddPeriodTemplateModal = showAddPeriodTemplateModal;
window.editPeriodTemplate = editPeriodTemplate;
window.deletePeriodTemplate = deletePeriodTemplate;
window.generateTimeSlots = generateTimeSlots;
window.applyGeneratedTemplates = applyGeneratedTemplates;

})(window);
