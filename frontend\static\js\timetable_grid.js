// =============================================
// Timetable Grid View - Weekly Class Timetables
// =============================================

(function(window){
    const SMS = window.SMS || (window.SMS = {});

let allClasses = [];
let allTeachers = [];
let allSubjects = [];
let schoolConfig = {};
let timetableData = {};

// =============================================
// Main Timetable Grid Page
// =============================================

SMS.loadTimetableGridPage = async function() {
    const contentArea = document.getElementById('contentArea');
    const safeYear = (typeof currentAcademicYear !== 'undefined' && currentAcademicYear) || new Date().getFullYear();
    contentArea.innerHTML = `
        <div class="container-fluid">
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-table"></i> Class Timetables</h2>
                <div>
                    <button class="btn btn-success me-2" onclick="generateTimetablesFromTemplates()">
                        <i class="fas fa-magic"></i> Generate from Templates
                    </button>
                    <button class="btn btn-primary" onclick="showBulkAssignModal()">
                        <i class="fas fa-users-cog"></i> Bulk Assign Teachers
                    </button>
                </div>
            </div>

            <!-- Class Selection -->
            <div class="card mb-4">
                <div class="card-header bg-info text-white">
                    <div class="row align-items-center">
                        <div class="col-md-4">
                            <label class="form-label mb-0 text-white"><strong>Select Class:</strong></label>
                            <select class="form-select form-select-sm mt-2" id="classSelector" onchange="loadClassTimetable()">
                                <option value="">Loading classes...</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label class="form-label mb-0 text-white"><strong>Academic Year:</strong></label>
                            <input type="number" class="form-control form-control-sm mt-2" 
                                   id="academicYear" value="${safeYear}" 
                                   onchange="loadClassTimetable()">
                        </div>
                        <div class="col-md-4">
                            <label class="form-label mb-0 text-white"><strong>School Name:</strong></label>
                            <div class="mt-2 text-white" id="schoolNameDisplay">
                                ${schoolConfig.school_name || 'School'}
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Timetable Grid -->
            <div id="timetableGridContainer">
                <div class="alert alert-info">
                    <i class="fas fa-info-circle"></i> Please select a class to view and edit its timetable
                </div>
            </div>
        </div>
    `;
    
    await loadInitialData();
}

async function loadInitialData() {
    try {
        const [classesRes, teachersRes, subjectsRes] = await Promise.all([
            fetch(SMS.apiUrl('/classes'), { headers: { 'Authorization': `Bearer ${SMS.authToken}` } }),
            fetch(SMS.apiUrl('/staff'), { headers: { 'Authorization': `Bearer ${SMS.authToken}` } }),
            fetch(SMS.apiUrl('/subjects'), { headers: { 'Authorization': `Bearer ${SMS.authToken}` } })
        ]);
        
        allClasses = await classesRes.json();
        allTeachers = await teachersRes.json();
        allSubjects = await subjectsRes.json();
        
        // Populate class selector
        const selector = document.getElementById('classSelector');
        selector.innerHTML = `
            <option value="">-- Select a Class --</option>
            ${allClasses.map(c => `
                <option value="${c.class_id}">${c.class_name} - ${c.section}</option>
            `).join('')}
        `;
    } catch (error) {
        console.error('Error loading initial data:', error);
        showError('Failed to load classes, teachers, or subjects');
    }
}

async function loadClassTimetable() {
    const classId = document.getElementById('classSelector').value;
    const container = document.getElementById('timetableGridContainer');
    
    if (!classId) {
        container.innerHTML = `
            <div class="alert alert-info">
                <i class="fas fa-info-circle"></i> Please select a class to view its timetable
            </div>
        `;
        return;
    }
    
    container.innerHTML = `<div class="text-center py-5"><div class="spinner-border"></div></div>`;
    
    try {
        // Get period templates and class timetable
        const year = document.getElementById('academicYear').value;
        const [templatesRes, timetableRes] = await Promise.all([
            fetch(SMS.apiUrl(`/timetable/periods/templates?academic_year=${year}`), {
                headers: { 'Authorization': `Bearer ${SMS.authToken}` }
            }),
            fetch(SMS.apiUrl(`/timetable/classes/${classId}?academic_year=${year}`), {
                headers: { 'Authorization': `Bearer ${SMS.authToken}` }
            })
        ]);
        
        if (!templatesRes.ok) {
            container.innerHTML = `<div class="alert alert-warning">Could not load period templates for ${year}. Configure them in School Settings.</div>`;
            return;
        }
        if (!timetableRes.ok) {
            container.innerHTML = `<div class="alert alert-warning">Could not load class timetable. Please try another class or reload.</div>`;
            return;
        }

        const templates = Array.isArray(await templatesRes.json()) ? await templatesRes.json() : [];
        const timetable = Array.isArray(await timetableRes.json()) ? await timetableRes.json() : [];

        if (!templates || templates.length === 0) {
            container.innerHTML = `
                <div class="alert alert-info">
                    No period templates found for ${year}. Go to <strong>School Settings</strong> → <em>Time Slot Generator</em> to create them.
                </div>`;
            return;
        }

        displayTimetableGrid(classId, templates, timetable || []);
    } catch (error) {
        console.error('Error:', error);
        container.innerHTML = `<div class="alert alert-danger">Error loading timetable</div>`;
    }
}

function displayTimetableGrid(classId, templates, timetable) {
    const container = document.getElementById('timetableGridContainer');
    const selectedClass = allClasses.find(c => c.class_id == classId);
    
    if (!selectedClass) return;
    
    const days = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    // Use ALL templates, sorted by time, so the grid includes Break/Lunch rows like the sample chart
    const orderedTemplates = [...templates].sort((a,b) => a.start_time.localeCompare(b.start_time));
    
    // Organize timetable by day and period
    const grid = {};
    days.forEach((_, dayIndex) => {
        grid[dayIndex + 1] = {};
        orderedTemplates.filter(t => t.is_teaching_period).forEach(p => {
            grid[dayIndex + 1][p.period_number] = null;
        });
    });
    
    timetable.forEach(entry => {
        if (grid[entry.day_of_week]) {
            grid[entry.day_of_week][entry.period_number] = entry;
        }
    });
    
    let html = `
        <div class="card">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0">
                    <i class="fas fa-calendar-week"></i> 
                    ${schoolConfig.school_name || 'School'} - ${selectedClass.class_name} ${selectedClass.section} - Timetable
                </h5>
            </div>
            <div class="card-body p-0">
                <div class="table-responsive">
                    <table class="table table-bordered table-hover mb-0 timetable-grid">
                        <thead class="table-light">
                            <tr>
                                <th class="text-center" style="width: 120px;">Time</th>
                                ${days.map(day => `<th class="text-center">${day}</th>`).join('')}
                            </tr>
                        </thead>
                        <tbody>
    `;
    
    orderedTemplates.forEach(period => {
        const startTime = period.start_time.substring(0, 5);
        const endTime = period.end_time.substring(0, 5);
        
        html += `
            <tr>
                <td class="text-center align-middle bg-light">
                    <strong>${period.period_name}</strong><br>
                    <small>${startTime} - ${endTime}</small>
                </td>
        `;
        
        days.forEach((_, dayIndex) => {
            const dayNum = dayIndex + 1;
            html += `<td class="p-2" style="min-width: 150px;">`;

            if (!period.is_teaching_period) {
                // Non-teaching row (Break/Lunch) styled and not assignable
                html += `
                    <div class="period-cell text-center" style="background:#f8f9fa; border:1px dashed #ced4da; padding:10px; border-radius:4px;">
                        <strong>${period.period_name}</strong>
                    </div>
                `;
            } else {
                const entry = grid[dayNum][period.period_number];
                if (entry && entry.teacher_id) {
                    const bgColors = ['#e3f2fd', '#f3e5f5', '#e8f5e9', '#fff3e0', '#fce4ec'];
                    const bgColor = bgColors[dayIndex % bgColors.length];
                    html += `
                        <div class="period-cell" style="background: ${bgColor}; padding: 8px; border-radius: 4px;">
                            <div class="mb-1">
                                <strong class="text-primary">${entry.subject_name || 'General'}</strong>
                            </div>
                            <div class="mb-1">
                                <small><i class="fas fa-user"></i> ${entry.teacher_name || 'Unassigned'}</small>
                            </div>
                            <div>
                                <button class="btn btn-xs btn-outline-primary" 
                                        onclick='editPeriodAssignment(${classId}, ${dayNum}, ${period.period_number}, ${JSON.stringify(entry)})'>
                                    <i class="fas fa-edit"></i>
                                </button>
                                <button class="btn btn-xs btn-outline-danger" 
                                        onclick="removePeriodAssignment(${entry.timetable_id})">
                                    <i class="fas fa-times"></i>
                                </button>
                            </div>
                        </div>
                    `;
                } else {
                    html += `
                        <button class="btn btn-sm btn-outline-success w-100" 
                                onclick='assignPeriod(${classId}, ${dayNum}, ${period.period_number}, "${period.start_time}", "${period.end_time}")'>
                            <i class="fas fa-plus"></i> Assign
                        </button>
                    `;
                }
            }

            html += `</td>`;
        });
        
        html += `</tr>`;
    });
    
    html += `
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
        
        <style>
            .timetable-grid td {
                vertical-align: middle;
            }
            .btn-xs {
                padding: 2px 6px;
                font-size: 0.75rem;
            }
            .period-cell {
                min-height: 80px;
            }
        </style>
    `;
    
    container.innerHTML = html;
}

async function assignPeriod(classId, dayOfWeek, periodNumber, startTime, endTime) {
    showTeacherAssignmentModal(classId, dayOfWeek, periodNumber, startTime, endTime);
}

async function editPeriodAssignment(classId, dayOfWeek, periodNumber, currentEntry) {
    showTeacherAssignmentModal(classId, dayOfWeek, periodNumber, 
                               currentEntry.start_time, currentEntry.end_time, currentEntry);
}

function showTeacherAssignmentModal(classId, dayOfWeek, periodNumber, startTime, endTime, currentEntry = null) {
    const selectedClass = allClasses.find(c => c.class_id == classId);
    const days = ['', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'];
    
    const html = `
        <div class="modal fade" id="assignmentModal" tabindex="-1">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <h5 class="modal-title">
                            <i class="fas fa-user-plus"></i> Assign Teacher
                        </h5>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                    </div>
                    <div class="modal-body">
                        <div class="alert alert-info">
                            <strong>Class:</strong> ${selectedClass.class_name} ${selectedClass.section}<br>
                            <strong>Day:</strong> ${days[dayOfWeek]}<br>
                            <strong>Period:</strong> ${periodNumber} (${startTime.substring(0,5)} - ${endTime.substring(0,5)})
                        </div>
                        
                        <form id="assignmentForm">
                            <div class="mb-3">
                                <label class="form-label">Subject</label>
                                <select class="form-select" id="assignSubject">
                                    <option value="">-- Select Subject --</option>
                                    ${allSubjects.map(s => `
                                        <option value="${s.subject_id}" ${currentEntry && currentEntry.subject_id == s.subject_id ? 'selected' : ''}>
                                            ${s.subject_name}
                                        </option>
                                    `).join('')}
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label class="form-label">Teacher</label>
                                <select class="form-select" id="assignTeacher" onchange="checkTeacherAvailability()">
                                    <option value="">-- Select Teacher --</option>
                                    ${allTeachers.map(t => `
                                        <option value="${t.user_id}" ${currentEntry && currentEntry.teacher_id == t.user_id ? 'selected' : ''}>
                                            ${t.full_name}
                                        </option>
                                    `).join('')}
                                </select>
                                <div id="availabilityStatus" class="mt-2"></div>
                            </div>
                        </form>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="button" class="btn btn-primary" id="saveAssignmentBtn" 
                                onclick='saveAssignment(${classId}, ${dayOfWeek}, ${periodNumber}, "${startTime}", "${endTime}")'>
                            <i class="fas fa-save"></i> Save Assignment
                        </button>
                    </div>
                </div>
            </div>
        </div>
    `;
    
    document.body.insertAdjacentHTML('beforeend', html);
    const modal = new bootstrap.Modal(document.getElementById('assignmentModal'));
    modal.show();
    
    document.getElementById('assignmentModal').addEventListener('hidden.bs.modal', function() {
        this.remove();
    });
}

async function checkTeacherAvailability() {
    const teacherId = document.getElementById('assignTeacher').value;
    if (!teacherId) {
        document.getElementById('availabilityStatus').innerHTML = '';
        return;
    }
    
    // Extract day and period from the modal context
    const modalTitle = document.querySelector('#assignmentModal .alert-info').textContent;
    const dayMatch = modalTitle.match(/Day: (\w+)/);
    const periodMatch = modalTitle.match(/Period: (\d+)/);
    
    if (!dayMatch || !periodMatch) return;
    
    const days = { 'Monday': 1, 'Tuesday': 2, 'Wednesday': 3, 'Thursday': 4, 'Friday': 5 };
    const dayOfWeek = days[dayMatch[1]];
    const periodNumber = parseInt(periodMatch[1]);
    const year = document.getElementById('academicYear').value;
    
    try {
        const response = await fetch(SMS.apiUrl(`/timetable/check-availability?teacher_id=${teacherId}&day_of_week=${dayOfWeek}&period_number=${periodNumber}&academic_year=${year}`), {
            method: 'POST',
            headers: { 'Authorization': `Bearer ${SMS.authToken}` }
        });
        
        const result = await response.json();
        const statusDiv = document.getElementById('availabilityStatus');
        
        if (result.is_available) {
            statusDiv.innerHTML = `<div class="alert alert-success mb-0"><i class="fas fa-check-circle"></i> Teacher is available</div>`;
            document.getElementById('saveAssignmentBtn').disabled = false;
        } else {
            statusDiv.innerHTML = `
                <div class="alert alert-warning mb-0">
                    <i class="fas fa-exclamation-triangle"></i> Teacher is already assigned to 
                    <strong>${result.conflicting_class}</strong> for <strong>${result.conflicting_subject}</strong>
                </div>
            `;
            document.getElementById('saveAssignmentBtn').disabled = true;
        }
    } catch (error) {
        console.error('Error checking availability:', error);
    }
}

async function saveAssignment(classId, dayOfWeek, periodNumber, startTime, endTime) {
    const subjectId = document.getElementById('assignSubject').value;
    const teacherId = document.getElementById('assignTeacher').value;
    const year = document.getElementById('academicYear').value;
    
    if (!teacherId) {
        showError('Please select a teacher');
        return;
    }
    
    const data = {
        class_id: classId,
        subject_id: subjectId ? parseInt(subjectId) : null,
        teacher_id: parseInt(teacherId),
        day_of_week: dayOfWeek,
        period_number: periodNumber,
        start_time: startTime,
        end_time: endTime,
        academic_year: parseInt(year)
    };
    
    try {
        const response = await fetch(SMS.apiUrl('/timetable/assign'), {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${SMS.authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify(data)
        });
        
        if (response.ok) {
            showSuccess('Teacher assigned successfully!');
            bootstrap.Modal.getInstance(document.getElementById('assignmentModal')).hide();
            await loadClassTimetable();
        } else {
            const error = await response.json();
            showError(error.detail || 'Failed to assign teacher');
        }
    } catch (error) {
        console.error('Error:', error);
        showError('Failed to assign teacher');
    }
}

async function removePeriodAssignment(timetableId) {
    if (!confirm('Are you sure you want to remove this assignment?')) return;

    try {
        const response = await fetch(SMS.apiUrl(`/timetable/assign/${timetableId}`), {
            method: 'DELETE',
            headers: { 'Authorization': `Bearer ${SMS.authToken}` }
        });
        
        if (response.ok) {
            showSuccess('Assignment removed successfully!');
            await loadClassTimetable();
        } else {
            showError('Failed to remove assignment');
        }
    } catch (error) {
        console.error('Error:', error);
        showError('Failed to remove assignment');
    }
}

// Generate timetables from templates
async function generateTimetablesFromTemplates() {
    try {
        const result = await Swal.fire({
            title: 'Generate Timetables from Templates?',
            text: 'This will create timetables for all classes based on period templates.',
            icon: 'question',
            showCancelButton: true,
            confirmButtonText: 'Yes, Generate',
            cancelButtonText: 'Cancel'
        });

        if (result.isConfirmed) {
            showLoadingInline('Generating timetables...');

            const response = await fetch(SMS.apiUrl('/timetable/generate-from-templates'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`,
                    'Content-Type': 'application/json'
                }
            });

            if (response.ok) {
                showSuccess('Timetables generated successfully!');
                await loadClassTimetable(); // Reload the current view
            } else {
                throw new Error('Failed to generate timetables');
            }
        }
    } catch (error) {
        console.error('Error generating timetables:', error);
        showError('Failed to generate timetables from templates');
    }
}

// ==================== BACKWARDS COMPATIBILITY ====================

window.loadTimetableGridPage = function(){ return SMS.loadTimetableGridPage(); };
window.loadClassTimetable = loadClassTimetable;
window.generateTimetablesFromTemplates = generateTimetablesFromTemplates;
window.showBulkAssignModal = showBulkAssignModal;
window.assignTeacherToSlot = assignTeacherToSlot;
window.removeAssignment = removeAssignment;

})(window);
