-- =============================================
-- School Configuration and Period Management
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- School Configuration Table
-- =============================================
CREATE TABLE school_config (
    config_id INT IDENTITY(1,1) PRIMARY KEY,
    config_key VARCHAR(100) UNIQUE NOT NULL,
    config_value VARCHAR(500) NOT NULL,
    description VARCHAR(500),
    updated_by INT,
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_SchoolConfig_UpdatedBy FOREIGN KEY (updated_by) REFERENCES users(user_id)
);
GO

-- =============================================
-- Period Templates Table (Define standard periods)
-- =============================================
CREATE TABLE period_templates (
    template_id INT IDENTITY(1,1) PRIMARY KEY,
    period_number INT NOT NULL,
    period_name VARCHAR(100) NOT NULL,  -- e.g., "Period 1", "Break", "Lunch"
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    is_teaching_period BIT DEFAULT 1,  -- 0 for breaks, lunch, etc.
    day_of_week INT NULL,  -- NULL means applies to all days, specific number for specific day
    academic_year INT NOT NULL,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE()
);
GO

CREATE INDEX IX_PeriodTemplates_Year ON period_templates(academic_year);
CREATE INDEX IX_PeriodTemplates_Active ON period_templates(is_active);
GO

-- =============================================
-- Class Timetable Table (Replaces teacher_periods with class-centric approach)
-- =============================================
CREATE TABLE class_timetable (
    timetable_id INT IDENTITY(1,1) PRIMARY KEY,
    class_id INT NOT NULL,
    subject_id INT,
    teacher_id INT,
    day_of_week INT NOT NULL,  -- 1=Monday, 2=Tuesday, ..., 5=Friday
    period_number INT NOT NULL,
    start_time TIME NOT NULL,
    end_time TIME NOT NULL,
    academic_year INT NOT NULL,
    is_active BIT DEFAULT 1,
    created_at DATETIME2 DEFAULT GETDATE(),
    updated_at DATETIME2 DEFAULT GETDATE(),
    CONSTRAINT FK_ClassTimetable_Class FOREIGN KEY (class_id) REFERENCES classes(class_id),
    CONSTRAINT FK_ClassTimetable_Subject FOREIGN KEY (subject_id) REFERENCES subjects(subject_id),
    CONSTRAINT FK_ClassTimetable_Teacher FOREIGN KEY (teacher_id) REFERENCES users(user_id),
    CONSTRAINT UQ_ClassTimetable UNIQUE (class_id, day_of_week, period_number, academic_year)
);
GO

CREATE INDEX IX_ClassTimetable_Class ON class_timetable(class_id);
CREATE INDEX IX_ClassTimetable_Teacher ON class_timetable(teacher_id);
CREATE INDEX IX_ClassTimetable_Day ON class_timetable(day_of_week);
GO

-- =============================================
-- Insert Default Configuration
-- =============================================
INSERT INTO school_config (config_key, config_value, description) VALUES
('school_start_time', '08:00:00', 'School start time'),
('school_end_time', '14:30:00', 'School end time'),
('periods_per_day', '8', 'Number of periods per day'),
('period_duration', '40', 'Default period duration in minutes'),
('break_duration', '15', 'Break duration in minutes'),
('lunch_duration', '30', 'Lunch duration in minutes'),
('school_name', 'Múinteoir School', 'School name for timetable display');
GO

-- =============================================
-- Insert Default Period Templates for current year
-- =============================================
DECLARE @CurrentYear INT = YEAR(GETDATE());

INSERT INTO period_templates (period_number, period_name, start_time, end_time, is_teaching_period, academic_year) VALUES
(1, 'Period 1', '08:00:00', '08:40:00', 1, @CurrentYear),
(2, 'Period 2', '08:40:00', '09:20:00', 1, @CurrentYear),
(3, 'Period 3', '09:20:00', '10:00:00', 1, @CurrentYear),
(4, 'Break', '10:00:00', '10:15:00', 0, @CurrentYear),
(5, 'Period 4', '10:15:00', '10:55:00', 1, @CurrentYear),
(6, 'Period 5', '10:55:00', '11:35:00', 1, @CurrentYear),
(7, 'Lunch', '11:35:00', '12:05:00', 0, @CurrentYear),
(8, 'Period 6', '12:05:00', '12:45:00', 1, @CurrentYear),
(9, 'Period 7', '12:45:00', '13:25:00', 1, @CurrentYear),
(10, 'Period 8', '13:25:00', '14:05:00', 1, @CurrentYear);
GO

-- =============================================
-- Stored Procedure: Get Teacher's Full Timetable
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetTeacherTimetable
    @TeacherId INT,
    @AcademicYear INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @AcademicYear IS NULL
        SET @AcademicYear = YEAR(GETDATE());
    
    SELECT 
        ct.timetable_id,
        ct.class_id,
        c.class_name,
        c.section,
        ct.subject_id,
        s.subject_name,
        ct.day_of_week,
        ct.period_number,
        CONVERT(VARCHAR(8), ct.start_time, 108) as start_time,
        CONVERT(VARCHAR(8), ct.end_time, 108) as end_time,
        ct.academic_year
    FROM class_timetable ct
    JOIN classes c ON ct.class_id = c.class_id
    LEFT JOIN subjects s ON ct.subject_id = s.subject_id
    WHERE ct.teacher_id = @TeacherId 
        AND ct.academic_year = @AcademicYear
        AND ct.is_active = 1
    ORDER BY ct.day_of_week, ct.period_number;
END
GO

-- =============================================
-- Stored Procedure: Get Class Timetable
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetClassTimetable
    @ClassId INT,
    @AcademicYear INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @AcademicYear IS NULL
        SET @AcademicYear = YEAR(GETDATE());
    
    SELECT 
        ct.timetable_id,
        ct.teacher_id,
        u.full_name as teacher_name,
        ct.subject_id,
        s.subject_name,
        ct.day_of_week,
        ct.period_number,
        pt.period_name,
        CONVERT(VARCHAR(8), ct.start_time, 108) as start_time,
        CONVERT(VARCHAR(8), ct.end_time, 108) as end_time,
        pt.is_teaching_period
    FROM class_timetable ct
    LEFT JOIN users u ON ct.teacher_id = u.user_id
    LEFT JOIN subjects s ON ct.subject_id = s.subject_id
    LEFT JOIN period_templates pt ON ct.period_number = pt.period_number 
        AND pt.academic_year = @AcademicYear
    WHERE ct.class_id = @ClassId 
        AND ct.academic_year = @AcademicYear
        AND ct.is_active = 1
    ORDER BY ct.day_of_week, ct.period_number;
END
GO

-- =============================================
-- Stored Procedure: Check Teacher Availability
-- =============================================
CREATE OR ALTER PROCEDURE sp_CheckTeacherAvailability
    @TeacherId INT,
    @DayOfWeek INT,
    @PeriodNumber INT,
    @AcademicYear INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @AcademicYear IS NULL
        SET @AcademicYear = YEAR(GETDATE());
    
    -- Returns 1 if teacher is available, 0 if busy
    IF EXISTS (
        SELECT 1 FROM class_timetable
        WHERE teacher_id = @TeacherId
            AND day_of_week = @DayOfWeek
            AND period_number = @PeriodNumber
            AND academic_year = @AcademicYear
            AND is_active = 1
    )
        SELECT 0 as is_available, 
               c.class_name + ' - ' + c.section as conflicting_class,
               s.subject_name as conflicting_subject
        FROM class_timetable ct
        JOIN classes c ON ct.class_id = c.class_id
        LEFT JOIN subjects s ON ct.subject_id = s.subject_id
        WHERE ct.teacher_id = @TeacherId
            AND ct.day_of_week = @DayOfWeek
            AND ct.period_number = @PeriodNumber
            AND ct.academic_year = @AcademicYear
            AND ct.is_active = 1;
    ELSE
        SELECT 1 as is_available, NULL as conflicting_class, NULL as conflicting_subject;
END
GO

PRINT 'School configuration and timetable schema created successfully!';
