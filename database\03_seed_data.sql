-- =============================================
-- School Management System - Seed Data
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Insert Roles
-- =============================================
SET IDENTITY_INSERT roles ON;
GO

INSERT INTO roles (role_id, role_name, role_name_urdu, description) VALUES
(1, 'Administrator', N'منتظم', 'Full system access'),
(2, 'Teacher', N'استاد', 'Attendance and exam management'),
(3, 'Accountant', N'اکاؤنٹنٹ', 'Fee management'),
(4, 'Receptionist', N'استقبالیہ', 'Student registration and discharge'),
(5, 'Parent', N'والدین', 'View-only access to student data');
GO

SET IDENTITY_INSERT roles OFF;
GO

-- =============================================
-- Insert Default Admin User
-- Password: Admin@123 (bcrypt hashed)
-- =============================================
SET IDENTITY_INSERT users ON;
GO

INSERT INTO users (user_id, username, password_hash, full_name, full_name_urdu, email, phone, role_id, is_active) VALUES
(1, 'admin', '$2b$12$LQv3c1yqBWVHxkd0LHAkCOYz6TtxMQJqhN8/LewY5GyYIr.8VfGSO', 'System Administrator', N'سسٹم منتظم', '<EMAIL>', '***********', 1, 1);
GO

SET IDENTITY_INSERT users OFF;
GO

-- =============================================
-- Insert Sample Classes
-- =============================================
INSERT INTO classes (class_name, class_name_urdu, section, academic_year, capacity) VALUES
('Nursery', N'نرسری', 'A', 2025, 30),
('Prep', N'پریپ', 'A', 2025, 30),
('Class 1', N'کلاس 1', 'A', 2025, 35),
('Class 1', N'کلاس 1', 'B', 2025, 35),
('Class 2', N'کلاس 2', 'A', 2025, 35),
('Class 3', N'کلاس 3', 'A', 2025, 40),
('Class 4', N'کلاس 4', 'A', 2025, 40),
('Class 5', N'کلاس 5', 'A', 2025, 40),
('Class 6', N'کلاس 6', 'A', 2025, 40),
('Class 7', N'کلاس 7', 'A', 2025, 40),
('Class 8', N'کلاس 8', 'A', 2025, 40),
('Class 9', N'کلاس 9', 'A', 2025, 45),
('Class 10', N'کلاس 10', 'A', 2025, 45);
GO

-- =============================================
-- Insert Subjects
-- =============================================
INSERT INTO subjects (subject_name, subject_name_urdu, subject_code, total_marks, passing_marks) VALUES
('English', N'انگلش', 'ENG', 100, 40),
('Urdu', N'اردو', 'URD', 100, 40),
('Mathematics', N'ریاضی', 'MATH', 100, 40),
('Science', N'سائنس', 'SCI', 100, 40),
('Social Studies', N'معاشرتی علوم', 'SST', 100, 40),
('Islamiyat', N'اسلامیات', 'ISL', 100, 40),
('Computer', N'کمپیوٹر', 'COMP', 100, 40),
('Physics', N'طبیعیات', 'PHY', 100, 40),
('Chemistry', N'کیمیا', 'CHEM', 100, 40),
('Biology', N'حیاتیات', 'BIO', 100, 40);
GO

-- =============================================
-- Insert Fee Structures (Sample for Class 1-A)
-- =============================================
DECLARE @class1a_id INT = (SELECT class_id FROM classes WHERE class_name = 'Class 1' AND section = 'A' AND academic_year = 2025);

INSERT INTO fee_structures (class_id, academic_year, fee_type, fee_type_urdu, amount, due_day, fine_per_day) VALUES
(@class1a_id, 2025, 'Admission Fee', N'داخلہ فیس', 5000.00, 1, 0),
(@class1a_id, 2025, 'Monthly Tuition', N'ماہانہ فیس', 3000.00, 10, 50.00),
(@class1a_id, 2025, 'Annual Charges', N'سالانہ چارجز', 2000.00, 1, 0),
(@class1a_id, 2025, 'Exam Fee', N'امتحان فیس', 1000.00, 1, 0);
GO

-- =============================================
-- Insert Holidays (Sample)
-- =============================================
INSERT INTO holidays (holiday_name, holiday_name_urdu, holiday_date, holiday_type) VALUES
('Kashmir Day', N'یوم کشمیر', '2025-02-05', 'Public Holiday'),
('Pakistan Day', N'یوم پاکستان', '2025-03-23', 'Public Holiday'),
('Labour Day', N'یوم مزدور', '2025-05-01', 'Public Holiday'),
('Independence Day', N'یوم آزادی', '2025-08-14', 'Public Holiday'),
('Iqbal Day', N'یوم اقبال', '2025-11-09', 'Public Holiday'),
('Quaid-e-Azam Day', N'یوم قائد اعظم', '2025-12-25', 'Public Holiday');
GO

-- =============================================
-- Insert Sample Exams
-- =============================================
INSERT INTO exams (exam_name, exam_name_urdu, exam_type, academic_year, start_date, end_date) VALUES
('First Term 2025', N'پہلا ٹرم 2025', 'First Term', 2025, '2025-04-01', '2025-04-15'),
('Second Term 2025', N'دوسرا ٹرم 2025', 'Second Term', 2025, '2025-08-01', '2025-08-15'),
('Final Term 2025', N'فائنل ٹرم 2025', 'Final Term', 2025, '2025-11-01', '2025-11-15');
GO

PRINT 'Seed data inserted successfully';
PRINT 'Default Admin Credentials:';
PRINT 'Username: admin';
PRINT 'Password: Admin@123';
GO

