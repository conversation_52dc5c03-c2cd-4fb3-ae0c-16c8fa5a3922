"""
Fee Collection Routes
Handles fee collection operations
"""
from fastapi import APIRouter, Depends, HTTPException, status
from models import FeeCollectionRequest, SuccessResponse
from auth import get_current_user, require_accountant
from services.fee_service import FeeService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/collect", response_model=SuccessResponse)
async def collect_fee(
    request: FeeCollectionRequest,
    current_user: dict = Depends(require_accountant)
):
    """
    Collect fee from student (Accountant/Admin only)
    
    Args:
        request: Fee collection request
        current_user: Current authenticated user
    
    Returns:
        Success response with receipt number
    """
    result = FeeService.collect_fee(request, current_user['user_id'])
    
    if result and result.get('Result') == 'Success':
        return SuccessResponse(
            message=result.get('Message', 'Fee collected successfully'),
            data={
                'receipt_number': result.get('ReceiptNumber'),
                'total_amount': result.get('TotalAmount')
            }
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result.get('Message', 'Failed to collect fee')
        )


@router.post("/collect-comprehensive", response_model=SuccessResponse)
async def collect_fee_comprehensive(
    request: FeeCollectionRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Comprehensive fee collection with detailed processing
    
    Args:
        request: Fee collection request
        current_user: Current authenticated user
    
    Returns:
        Success response with detailed collection information
    """
    try:
        result = FeeService.collect_fee_comprehensive(request, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return SuccessResponse(
                message=result.get('Message', 'Fee collected successfully'),
                data={
                    'receipt_number': result.get('ReceiptNumber'),
                    'total_amount': result.get('TotalAmount'),
                    'collection_details': result.get('CollectionDetails', {})
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to collect fee')
            )
    except Exception as e:
        logger.error(f"Error in comprehensive fee collection: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during fee collection"
        )
