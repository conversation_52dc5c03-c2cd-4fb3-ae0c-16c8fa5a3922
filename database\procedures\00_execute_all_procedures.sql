-- =============================================
-- Master Script to Execute All Stored Procedures
-- =============================================
-- This script executes all stored procedure creation scripts
-- in the correct order
-- =============================================

USE SchoolManagementDB;
GO

PRINT '========================================';
PRINT 'Starting Stored Procedures Creation';
PRINT '========================================';
PRINT '';

-- Execute existing stored procedure files
PRINT 'Executing User Management Procedures...';
:r sp_user_management.sql
PRINT '';

PRINT 'Executing Class Management Procedures...';
:r sp_class_management.sql
PRINT '';

PRINT 'Executing Student Management Procedures...';
:r sp_student_management.sql
PRINT '';

PRINT 'Executing Attendance Management Procedures...';
:r sp_attendance_management.sql
PRINT '';

PRINT 'Executing Fee Management Procedures...';
:r sp_fee_management.sql
PRINT '';

PRINT 'Executing Fee Additional Procedures...';
:r sp_fee_additional.sql
PRINT '';

PRINT 'Executing Exam Management Procedures...';
:r sp_exam_management.sql
PRINT '';

PRINT 'Executing Exam Additional Procedures...';
:r sp_exam_additional.sql
PRINT '';

-- Execute new stored procedure files
PRINT 'Executing Holiday Management Procedures...';
:r sp_holiday_management.sql
PRINT '';

PRINT 'Executing Staff Management Procedures...';
:r sp_staff_management.sql
PRINT '';

PRINT 'Executing Subject Management Procedures...';
:r sp_subject_management.sql
PRINT '';

PRINT '';
PRINT '========================================';
PRINT 'All Stored Procedures Created Successfully!';
PRINT '========================================';
PRINT '';

-- List all stored procedures
PRINT 'List of all stored procedures:';
SELECT 
    SCHEMA_NAME(schema_id) AS [Schema],
    name AS [Procedure Name],
    create_date AS [Created],
    modify_date AS [Last Modified]
FROM sys.procedures
WHERE name LIKE 'sp_%'
ORDER BY name;

GO

