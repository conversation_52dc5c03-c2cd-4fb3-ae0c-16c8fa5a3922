// Holidays Management Module
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    SMS.loadHolidaysPage = async function() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-calendar-times"></i> Holidays Management</h2>
                <button class="btn btn-primary" onclick="SMS.showAddHolidayModal()">
                    <i class="fas fa-plus"></i> Add New Holiday
                </button>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <select class="form-control" id="holidayYearFilter">
                                <option value="${new Date().getFullYear()}">${new Date().getFullYear()}</option>
                                <option value="${new Date().getFullYear() + 1}">${new Date().getFullYear() + 1}</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <button class="btn btn-primary" onclick="SMS.loadHolidays()">
                                <i class="fas fa-search"></i> Filter
                            </button>
                        </div>
                    </div>
                    <div id="holidaysTableContainer">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        await SMS.loadHolidays();
    };

    SMS.loadHolidays = async function() {
        try {
            const year = document.getElementById('holidayYearFilter')?.value || new Date().getFullYear();
            const response = await fetch(SMS.apiUrl(`/holidays?year=${year}`), {
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`
                }
            });

            if (response.ok) {
                const holidays = await response.json();
                SMS.displayHolidaysTable(holidays);
            } else {
                SMS.showError('Failed to load holidays');
            }
        } catch (error) {
            console.error('Error loading holidays:', error);
            SMS.showError('Error loading holidays');
        }
    };

    SMS.displayHolidaysTable = function(holidays) {
        const container = document.getElementById('holidaysTableContainer');

        if (holidays.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">No holidays found</p>';
            return;
        }

        let html = `
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Date</th>
                            <th>Holiday Name</th>
                            <th>Holiday Name (Urdu)</th>
                            <th>Type</th>
                            <th>Description</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        holidays.forEach(holiday => {
            html += `
                <tr>
                    <td>${new Date(holiday.holiday_date).toLocaleDateString()}</td>
                    <td>${holiday.holiday_name}</td>
                    <td>${holiday.holiday_name_urdu || 'N/A'}</td>
                    <td><span class="badge bg-info">${holiday.holiday_type || 'Public'}</span></td>
                    <td>${holiday.description || 'N/A'}</td>
                    <td>
                        <button class="btn btn-sm btn-warning" onclick="SMS.editHoliday(${holiday.holiday_id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="SMS.deleteHoliday(${holiday.holiday_id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;
    };

    SMS.showAddHolidayModal = async function() {
        Swal.fire({
            title: 'Add New Holiday',
            html: `
                <form id="addHolidayForm" class="text-start">
                    <div class="mb-3">
                        <label class="form-label">Holiday Name *</label>
                        <input type="text" class="form-control" id="holidayName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Holiday Name (Urdu)</label>
                        <input type="text" class="form-control" id="holidayNameUrdu" dir="rtl">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Date *</label>
                        <input type="date" class="form-control" id="holidayDate" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Type</label>
                        <select class="form-control" id="holidayType">
                            <option value="Public">Public Holiday</option>
                            <option value="School Event">School Event</option>
                            <option value="Exam">Exam Holiday</option>
                            <option value="Other">Other</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" id="holidayDescription" rows="2"></textarea>
                    </div>
                </form>
            `,
            showCancelButton: true,
            confirmButtonText: 'Add Holiday',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const form = document.getElementById('addHolidayForm');
                if (!form.checkValidity()) {
                    Swal.showValidationMessage('Please fill all required fields');
                    return false;
                }

                return {
                    holiday_name: document.getElementById('holidayName').value,
                    holiday_name_urdu: document.getElementById('holidayNameUrdu').value || null,
                    holiday_date: document.getElementById('holidayDate').value,
                    holiday_type: document.getElementById('holidayType').value,
                    description: document.getElementById('holidayDescription').value || null,
                    is_active: true
                };
            }
        }).then(async (result) => {
            if (result.isConfirmed) {
                await SMS.createHoliday(result.value);
            }
        });
    };

    SMS.createHoliday = async function(holidayData) {
        try {
            const response = await fetch(SMS.apiUrl('/holidays'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(holidayData)
            });

            if (response.ok) {
                SMS.showSuccess('Holiday added successfully!');
                SMS.loadHolidays();
            } else {
                const error = await response.json();
                SMS.showError(error.detail || 'Failed to add holiday');
            }
        } catch (error) {
            console.error('Error creating holiday:', error);
            SMS.showError('Error creating holiday');
        }
    };

    SMS.editHoliday = async function(holidayId) {
        try {
            const response = await fetch(SMS.apiUrl(`/holidays/${holidayId}`), {
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`
                }
            });

            if (!response.ok) {
                SMS.showError('Failed to load holiday data');
                return;
            }

            const holidayData = await response.json();

            const { value: formValues } = await Swal.fire({
                title: 'Edit Holiday',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Holiday Name</label>
                            <input type="text" id="editHolidayName" class="swal2-input" value="${holidayData.holiday_name}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Holiday Name (Urdu)</label>
                            <input type="text" id="editHolidayNameUrdu" class="swal2-input" value="${holidayData.holiday_name_urdu || ''}" dir="rtl">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Date</label>
                            <input type="date" id="editHolidayDate" class="swal2-input" value="${holidayData.holiday_date}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Type</label>
                            <select id="editHolidayType" class="swal2-select">
                                <option value="Public Holiday" ${holidayData.holiday_type === 'Public Holiday' ? 'selected' : ''}>Public Holiday</option>
                                <option value="School Event" ${holidayData.holiday_type === 'School Event' ? 'selected' : ''}>School Event</option>
                                <option value="Other" ${holidayData.holiday_type === 'Other' ? 'selected' : ''}>Other</option>
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Description</label>
                            <textarea id="editHolidayDescription" class="swal2-textarea">${holidayData.description || ''}</textarea>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <select id="editHolidayIsActive" class="swal2-select">
                                <option value="true" ${holidayData.is_active ? 'selected' : ''}>Active</option>
                                <option value="false" ${!holidayData.is_active ? 'selected' : ''}>Inactive</option>
                            </select>
                        </div>
                    </div>
                `,
                width: '500px',
                showCancelButton: true,
                confirmButtonText: 'Update Holiday',
                preConfirm: () => {
                    return {
                        holiday_name: document.getElementById('editHolidayName').value,
                        holiday_name_urdu: document.getElementById('editHolidayNameUrdu').value || null,
                        holiday_date: document.getElementById('editHolidayDate').value,
                        holiday_type: document.getElementById('editHolidayType').value,
                        description: document.getElementById('editHolidayDescription').value || null,
                        is_active: document.getElementById('editHolidayIsActive').value === 'true'
                    };
                }
            });

            if (formValues) {
                const updateResponse = await fetch(SMS.apiUrl(`/holidays/${holidayId}`), {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${SMS.authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formValues)
                });

                if (updateResponse.ok) {
                    SMS.showSuccess('Holiday updated successfully!');
                    SMS.loadHolidays();
                } else {
                    const error = await updateResponse.json();
                    SMS.showError(error.detail || 'Failed to update holiday');
                }
            }
        } catch (error) {
            console.error('Error editing holiday:', error);
            SMS.showError('Error editing holiday');
        }
    };

    SMS.deleteHoliday = async function(holidayId) {
        Swal.fire({
            title: 'Are you sure?',
            text: 'Do you want to delete this holiday?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it'
        }).then(async (result) => {
            if (result.isConfirmed) {
                try {
                    const response = await fetch(SMS.apiUrl(`/holidays/${holidayId}`), {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${SMS.authToken}`
                        }
                    });

                    if (response.ok) {
                        SMS.showSuccess('Holiday deleted successfully!');
                        SMS.loadHolidays();
                    } else {
                        SMS.showError('Failed to delete holiday');
                    }
                } catch (error) {
                    console.error('Error deleting holiday:', error);
                    SMS.showError('Error deleting holiday');
                }
            }
        });
    };

    // Backwards-compatible aliases
    window.loadHolidaysPage = function(){ return SMS.loadHolidaysPage(); };
    window.showAddHolidayModal = function(){ return SMS.showAddHolidayModal(); };
    window.editHoliday = function(id){ return SMS.editHoliday(id); };
    window.deleteHoliday = function(id){ return SMS.deleteHoliday(id); };

})(window);

