// Subjects Management Module
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    SMS.loadSubjectsPage = async function() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-book"></i> Subjects Management</h2>
                <button class="btn btn-primary" onclick="SMS.showAddSubjectModal()">
                    <i class="fas fa-plus"></i> Add New Subject
                </button>
            </div>

            <div class="card">
                <div class="card-body">
                    <div id="subjectsTableContainer">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        await SMS.loadSubjects();
    };

    SMS.loadSubjects = async function() {
        try {
            const response = await fetch(SMS.apiUrl('/subjects'), {
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`
                }
            });

            if (response.ok) {
                const subjects = await response.json();
                SMS.displaySubjectsTable(subjects);
            } else {
                SMS.showError('Failed to load subjects');
            }
        } catch (error) {
            console.error('Error loading subjects:', error);
            SMS.showError('Error loading subjects');
        }
    };

    SMS.displaySubjectsTable = function(subjects) {
        const container = document.getElementById('subjectsTableContainer');

        if (subjects.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">No subjects found</p>';
            return;
        }

        let html = `
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Subject Code</th>
                            <th>Subject Name</th>
                            <th>Subject Name (Urdu)</th>
                            <th>Total Marks</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        subjects.forEach(subject => {
            const statusBadge = subject.is_active
                ? '<span class="badge bg-success">Active</span>'
                : '<span class="badge bg-secondary">Inactive</span>';

            html += `
                <tr>
                    <td>${subject.subject_code}</td>
                    <td>${subject.subject_name}</td>
                    <td>${subject.subject_name_urdu || 'N/A'}</td>
                    <td>${subject.total_marks}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <button class="btn btn-sm btn-warning" onclick="SMS.editSubject(${subject.subject_id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="SMS.deleteSubject(${subject.subject_id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;
    };

    SMS.showAddSubjectModal = async function() {
        Swal.fire({
            title: 'Add New Subject',
            html: `
                <form id="addSubjectForm" class="text-start">
                    <div class="mb-3">
                        <label class="form-label">Subject Code *</label>
                        <input type="text" class="form-control" id="subjectCode" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Subject Name *</label>
                        <input type="text" class="form-control" id="subjectName" required>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Subject Name (Urdu)</label>
                        <input type="text" class="form-control" id="subjectNameUrdu" dir="rtl">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Total Marks *</label>
                        <input type="number" class="form-control" id="totalMarks" value="100" required>
                    </div>
                </form>
            `,
            showCancelButton: true,
            confirmButtonText: 'Add Subject',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const form = document.getElementById('addSubjectForm');
                if (!form.checkValidity()) {
                    Swal.showValidationMessage('Please fill all required fields');
                    return false;
                }

                return {
                    subject_code: document.getElementById('subjectCode').value,
                    subject_name: document.getElementById('subjectName').value,
                    subject_name_urdu: document.getElementById('subjectNameUrdu').value || null,
                    total_marks: parseInt(document.getElementById('totalMarks').value),
                    is_active: true
                };
            }
        }).then(async (result) => {
            if (result.isConfirmed) {
                await SMS.createSubject(result.value);
            }
        });
    };

    SMS.createSubject = async function(subjectData) {
        try {
            const response = await fetch(SMS.apiUrl('/subjects'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(subjectData)
            });

            if (response.ok) {
                SMS.showSuccess('Subject added successfully!');
                SMS.loadSubjects();
            } else {
                const error = await response.json();
                SMS.showError(error.detail || 'Failed to add subject');
            }
        } catch (error) {
            console.error('Error creating subject:', error);
            SMS.showError('Error creating subject');
        }
    };

    SMS.editSubject = async function(subjectId) {
        try {
            const response = await fetch(SMS.apiUrl(`/subjects/${subjectId}`), {
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`
                }
            });

            if (!response.ok) {
                SMS.showError('Failed to load subject data');
                return;
            }

            const subjectData = await response.json();

            const { value: formValues } = await Swal.fire({
                title: 'Edit Subject',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Subject Code</label>
                            <input type="text" id="editSubjectCode" class="swal2-input" value="${subjectData.subject_code}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Subject Name</label>
                            <input type="text" id="editSubjectName" class="swal2-input" value="${subjectData.subject_name}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Subject Name (Urdu)</label>
                            <input type="text" id="editSubjectNameUrdu" class="swal2-input" value="${subjectData.subject_name_urdu || ''}" dir="rtl">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Total Marks</label>
                            <input type="number" id="editTotalMarks" class="swal2-input" value="${subjectData.total_marks}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Status</label>
                            <select id="editSubjectIsActive" class="swal2-select">
                                <option value="true" ${subjectData.is_active ? 'selected' : ''}>Active</option>
                                <option value="false" ${!subjectData.is_active ? 'selected' : ''}>Inactive</option>
                            </select>
                        </div>
                    </div>
                `,
                width: '500px',
                showCancelButton: true,
                confirmButtonText: 'Update Subject',
                preConfirm: () => {
                    return {
                        subject_code: document.getElementById('editSubjectCode').value,
                        subject_name: document.getElementById('editSubjectName').value,
                        subject_name_urdu: document.getElementById('editSubjectNameUrdu').value || null,
                        total_marks: parseInt(document.getElementById('editTotalMarks').value),
                        is_active: document.getElementById('editSubjectIsActive').value === 'true'
                    };
                }
            });

            if (formValues) {
                const updateResponse = await fetch(SMS.apiUrl(`/subjects/${subjectId}`), {
                    method: 'PUT',
                    headers: {
                        'Authorization': `Bearer ${SMS.authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formValues)
                });

                if (updateResponse.ok) {
                    SMS.showSuccess('Subject updated successfully!');
                    SMS.loadSubjects();
                } else {
                    const error = await updateResponse.json();
                    SMS.showError(error.detail || 'Failed to update subject');
                }
            }
        } catch (error) {
            console.error('Error editing subject:', error);
            SMS.showError('Error editing subject');
        }
    };

    SMS.deleteSubject = async function(subjectId) {
        Swal.fire({
            title: 'Are you sure?',
            text: 'Do you want to delete this subject?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, delete it'
        }).then(async (result) => {
            if (result.isConfirmed) {
                try {
                    const response = await fetch(SMS.apiUrl(`/subjects/${subjectId}`), {
                        method: 'DELETE',
                        headers: {
                            'Authorization': `Bearer ${SMS.authToken}`
                        }
                    });

                    if (response.ok) {
                        SMS.showSuccess('Subject deleted successfully!');
                        SMS.loadSubjects();
                    } else {
                        SMS.showError('Failed to delete subject');
                    }
                } catch (error) {
                    console.error('Error deleting subject:', error);
                    SMS.showError('Error deleting subject');
                }
            }
        });
    };

    // Backwards-compatible aliases
    window.loadSubjectsPage = function(){ return SMS.loadSubjectsPage(); };
    window.showAddSubjectModal = function(){ return SMS.showAddSubjectModal(); };
    window.editSubject = function(id){ return SMS.editSubject(id); };
    window.deleteSubject = function(id){ return SMS.deleteSubject(id); };

})(window);

