-- Teacher Attendance Management Stored Procedures
USE SchoolManagementDB;
GO

-- =============================================
-- Mark Teacher Attendance
-- =============================================
IF OBJECT_ID('sp_Teacher_MarkAttendance', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_MarkAttendance;
GO
CREATE PROCEDURE sp_Teacher_MarkAttendance
    @teacher_id INT,
    @attendance_date DATE,
    @status VARCHAR(20),
    @remarks VARCHAR(500) = NULL,
    @marked_by INT
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Check if attendance already exists for this date
    IF EXISTS (SELECT 1 FROM teacher_attendance WHERE teacher_id = @teacher_id AND attendance_date = @attendance_date)
    BEGIN
        -- Update existing attendance
        UPDATE teacher_attendance 
        SET status = @status, remarks = @remarks, marked_by = @marked_by, marked_at = GETDATE()
        WHERE teacher_id = @teacher_id AND attendance_date = @attendance_date;
        
        SELECT 'Success' AS Result, 'Teacher attendance updated successfully' AS Message;
    END
    ELSE
    BEGIN
        -- Insert new attendance record
        INSERT INTO teacher_attendance (teacher_id, attendance_date, status, remarks, marked_by, marked_at)
        VALUES (@teacher_id, @attendance_date, @status, @remarks, @marked_by, GETDATE());
        
        SELECT 'Success' AS Result, 'Teacher attendance marked successfully' AS Message;
    END
END
GO

-- =============================================
-- Get Teacher Attendance
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAttendance', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAttendance;
GO
CREATE PROCEDURE sp_Teacher_GetAttendance
    @teacher_id INT = NULL,
    @start_date DATE = NULL,
    @end_date DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT ta.attendance_id, ta.teacher_id, u.full_name AS teacher_name,
           ta.attendance_date, ta.status, ta.remarks, ta.marked_at,
           mb.full_name AS marked_by_name
    FROM teacher_attendance ta
    INNER JOIN users u ON ta.teacher_id = u.user_id
    LEFT JOIN users mb ON ta.marked_by = mb.user_id
    WHERE (@teacher_id IS NULL OR ta.teacher_id = @teacher_id)
      AND (@start_date IS NULL OR ta.attendance_date >= @start_date)
      AND (@end_date IS NULL OR ta.attendance_date <= @end_date)
    ORDER BY ta.attendance_date DESC, u.full_name;
END
GO

-- =============================================
-- Get Teacher Attendance Summary
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAttendanceSummary', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAttendanceSummary;
GO
CREATE PROCEDURE sp_Teacher_GetAttendanceSummary
    @year INT,
    @month INT,
    @teacher_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT u.user_id AS teacher_id, u.full_name AS teacher_name,
           COUNT(CASE WHEN ta.status = 'Present' THEN 1 END) AS present_days,
           COUNT(CASE WHEN ta.status = 'Absent' THEN 1 END) AS absent_days,
           COUNT(CASE WHEN ta.status = 'Late' THEN 1 END) AS late_days,
           COUNT(CASE WHEN ta.status = 'Half Day' THEN 1 END) AS half_days,
           COUNT(*) AS total_marked_days
    FROM users u
    LEFT JOIN teacher_attendance ta ON u.user_id = ta.teacher_id 
        AND YEAR(ta.attendance_date) = @year 
        AND MONTH(ta.attendance_date) = @month
    WHERE u.role_id IN (SELECT role_id FROM roles WHERE role_name LIKE '%Teacher%' OR role_name = 'Staff')
      AND u.is_active = 1
      AND (@teacher_id IS NULL OR u.user_id = @teacher_id)
    GROUP BY u.user_id, u.full_name
    ORDER BY u.full_name;
END
GO

-- =============================================
-- Get Teacher Attendance Date Range
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAttendanceDateRange', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAttendanceDateRange;
GO
CREATE PROCEDURE sp_Teacher_GetAttendanceDateRange
    @start_date DATE,
    @end_date DATE,
    @teacher_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT ta.attendance_id, ta.teacher_id, u.full_name AS teacher_name,
           ta.attendance_date, ta.status, ta.remarks, ta.marked_at,
           mb.full_name AS marked_by_name
    FROM teacher_attendance ta
    INNER JOIN users u ON ta.teacher_id = u.user_id
    LEFT JOIN users mb ON ta.marked_by = mb.user_id
    WHERE ta.attendance_date BETWEEN @start_date AND @end_date
      AND (@teacher_id IS NULL OR ta.teacher_id = @teacher_id)
    ORDER BY ta.attendance_date DESC, u.full_name;
END
GO
