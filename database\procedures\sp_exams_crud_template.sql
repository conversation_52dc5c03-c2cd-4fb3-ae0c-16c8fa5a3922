/*
  Template: <PERSON><PERSON> CRUD stored procedures
  NOTE: This is a template / documentation file only. The real exam procs are present in sp_exam_management.sql and sp_exam_additional.sql.

  Example procedures referenced by backend code:
    - sp_CreateExam(@exam_name, @exam_type, @academic_year, ...)
    - sp_UpdateExam(@exam_id, ...)
    - sp_GetExams(@academic_year INT = NULL, @exam_type NVARCHAR(50) = NULL)
    - sp_RecordExamMarks(@exam_id, @student_id, @subject_id, @obtained_marks, @total_marks, @entered_by)
    - sp_EnterExamMarks(...)
    - sp_SetExamResultDraft(@exam_id, @student_id, @subject_id, @is_draft)
    - sp_GetStudentResults(@student_id, @exam_id = NULL)
    - sp_GetExamResults(@exam_id, @class_id = NULL)
    - sp_GetReportCard(@student_id, @exam_id)
    - sp_GetTopPerformers(@exam_id, @class_id = NULL, @limit INT = 10)

  Edit the actual CREATE PROCEDURE statements in sp_exam_management.sql / sp_exam_additional.sql when applying changes to the DB.
*/
