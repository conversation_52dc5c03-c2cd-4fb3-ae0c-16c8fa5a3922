/**
 * Router Module - Handles page navigation and routing
 */

function loadPage(page) {
    console.log('[ROUTER] Loading page:', page);

    // Scroll to top of content area
    const mainContent = document.getElementById('mainContent');
    if (mainContent) {
        mainContent.scrollTop = 0;
    }
    window.scrollTo(0, 0);

    const SMS = window.SMS || {};
    try {
        const name = 'load' + page.replace(/(^|[-_\s]+)(\w)/g, (_, __, ch) => ch.toUpperCase()) + 'Page';
        console.log('[ROUTER] Looking for function:', name);
        const fn = SMS[name] || window[name];
        if (typeof fn === 'function') {
            console.log('[ROUTER] Function found, executing...');
            return fn();
        } else {
            console.log('[ROUTER] Function not found:', name);
        }
    } catch (e) {
        console.error('[ROUTER] Error loading page:', e);
    }

    // fallback to dashboard
    console.log('[ROUTER] Falling back to dashboard');
    if (typeof window.SMS !== 'undefined' && typeof window.SMS.loadDashboardData === 'function') {
        return window.SMS.loadDashboardData();
    }
}

// Export to global scope
window.loadPage = loadPage;

// Export to SMS namespace if available
if (typeof window.SMS !== 'undefined') {
    window.SMS.loadPage = loadPage;
}
