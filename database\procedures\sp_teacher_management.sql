-- Teacher Management Stored Procedures
USE SchoolManagementDB;
GO

-- =============================================
-- Get All Teacher Schedules
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAllSchedules', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAllSchedules;
GO
CREATE PROCEDURE sp_Teacher_GetAllSchedules
    @academic_year INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    IF @academic_year IS NULL
        SELECT @academic_year = MAX(academic_year) FROM classes;
    
    SELECT DISTINCT u.user_id AS teacher_id, u.full_name AS teacher_name, s.designation
    FROM users u
    LEFT JOIN staff s ON u.user_id = s.user_id
    WHERE u.role_id IN (SELECT role_id FROM roles WHERE role_name LIKE '%Teacher%' OR role_name = 'Staff')
      AND u.is_active = 1
    ORDER BY u.full_name;
END
GO

-- =============================================
-- Get Teacher Schedule by ID
-- =============================================
IF OBJECT_ID('sp_Teacher_GetSchedule', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetSchedule;
GO
CREATE PROCEDURE sp_Teacher_GetSchedule
    @teacher_id INT,
    @academic_year INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    IF @academic_year IS NULL
        SELECT @academic_year = MAX(academic_year) FROM classes;
    
    SELECT u.user_id, u.full_name, s.designation
    FROM users u
    LEFT JOIN staff s ON u.user_id = s.user_id
    WHERE u.user_id = @teacher_id;
END
GO

-- =============================================
-- Get Teacher Periods
-- =============================================
IF OBJECT_ID('sp_Teacher_GetPeriods', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetPeriods;
GO
CREATE PROCEDURE sp_Teacher_GetPeriods
    @teacher_id INT,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT tp.period_id, tp.teacher_id, u.full_name as teacher_name,
           tp.class_id, c.class_name, c.section,
           tp.subject_id, s.subject_name, tp.day_of_week, tp.period_number,
           CONVERT(VARCHAR(8), tp.start_time, 108) as start_time,
           CONVERT(VARCHAR(8), tp.end_time, 108) as end_time,
           tp.academic_year, tp.is_active
    FROM teacher_periods tp
    JOIN users u ON tp.teacher_id = u.user_id
    JOIN classes c ON tp.class_id = c.class_id
    LEFT JOIN subjects s ON tp.subject_id = s.subject_id
    WHERE tp.teacher_id = @teacher_id AND tp.academic_year = @academic_year AND tp.is_active = 1
    ORDER BY tp.day_of_week, tp.period_number;
END
GO

-- =============================================
-- Check Teacher Period Conflict
-- =============================================
IF OBJECT_ID('sp_Teacher_CheckPeriodConflict', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_CheckPeriodConflict;
GO
CREATE PROCEDURE sp_Teacher_CheckPeriodConflict
    @teacher_id INT,
    @day_of_week INT,
    @academic_year INT,
    @start_time TIME,
    @end_time TIME
AS
BEGIN
    SET NOCOUNT ON;
    SELECT COUNT(*) as conflict_count FROM teacher_periods
    WHERE teacher_id = @teacher_id AND day_of_week = @day_of_week AND academic_year = @academic_year AND is_active = 1
    AND ((start_time <= @start_time AND end_time > @start_time)
         OR (start_time < @end_time AND end_time >= @end_time)
         OR (start_time >= @start_time AND end_time <= @end_time));
END
GO

-- =============================================
-- Create Teacher Period
-- =============================================
IF OBJECT_ID('sp_Teacher_CreatePeriod', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_CreatePeriod;
GO
CREATE PROCEDURE sp_Teacher_CreatePeriod
    @teacher_id INT,
    @class_id INT,
    @subject_id INT = NULL,
    @day_of_week INT,
    @period_number INT,
    @start_time TIME,
    @end_time TIME,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO teacher_periods (teacher_id, class_id, subject_id, day_of_week, period_number, start_time, end_time, academic_year)
    VALUES (@teacher_id, @class_id, @subject_id, @day_of_week, @period_number, @start_time, @end_time, @academic_year);
    SELECT SCOPE_IDENTITY() AS period_id;
END
GO

-- =============================================
-- Delete Teacher Period (Soft Delete)
-- =============================================
IF OBJECT_ID('sp_Teacher_DeletePeriod', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_DeletePeriod;
GO
CREATE PROCEDURE sp_Teacher_DeletePeriod
    @period_id INT
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE teacher_periods SET is_active = 0 WHERE period_id = @period_id;
    SELECT 'Success' AS Result, 'Period deleted' AS Message;
END
GO

-- =============================================
-- Mark Teacher Attendance
-- =============================================
IF OBJECT_ID('sp_Teacher_MarkAttendance', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_MarkAttendance;
GO
CREATE PROCEDURE sp_Teacher_MarkAttendance
    @teacher_id INT,
    @attendance_date DATE,
    @status NVARCHAR(50),
    @check_in_time TIME = NULL,
    @check_out_time TIME = NULL,
    @leave_type NVARCHAR(100) = NULL,
    @remarks NVARCHAR(500) = NULL,
    @marked_by INT
AS
BEGIN
    SET NOCOUNT ON;
    IF EXISTS (SELECT 1 FROM teacher_attendance WHERE teacher_id = @teacher_id AND attendance_date = @attendance_date)
    BEGIN
        UPDATE teacher_attendance
        SET status = @status, check_in_time = @check_in_time, check_out_time = @check_out_time,
            leave_type = @leave_type, remarks = @remarks, updated_at = GETDATE()
        WHERE teacher_id = @teacher_id AND attendance_date = @attendance_date;
    END
    ELSE
    BEGIN
        INSERT INTO teacher_attendance (teacher_id, attendance_date, status, check_in_time, check_out_time, leave_type, remarks, marked_by)
        VALUES (@teacher_id, @attendance_date, @status, @check_in_time, @check_out_time, @leave_type, @remarks, @marked_by);
    END
    SELECT 'Success' AS Result, 'Attendance marked' AS Message;
END
GO

-- =============================================
-- Get Teacher Attendance
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAttendance', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAttendance;
GO
CREATE PROCEDURE sp_Teacher_GetAttendance
    @teacher_id INT = NULL,
    @start_date DATE = NULL,
    @end_date DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SELECT ta.attendance_id, ta.teacher_id, u.full_name AS teacher_name,
           ta.attendance_date, ta.status, ta.check_in_time, ta.check_out_time,
           ta.leave_type, ta.remarks, ta.marked_by
    FROM teacher_attendance ta
    JOIN users u ON ta.teacher_id = u.user_id
    WHERE (@teacher_id IS NULL OR ta.teacher_id = @teacher_id)
      AND (@start_date IS NULL OR ta.attendance_date >= @start_date)
      AND (@end_date IS NULL OR ta.attendance_date <= @end_date)
    ORDER BY ta.attendance_date DESC;
END
GO

-- =============================================
-- Get Teacher Substitutions
-- =============================================
IF OBJECT_ID('sp_Teacher_GetSubstitutions', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetSubstitutions;
GO
CREATE PROCEDURE sp_Teacher_GetSubstitutions
    @teacher_id INT = NULL,
    @start_date DATE = NULL,
    @end_date DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SELECT ts.substitution_id, ts.original_teacher_id, u1.full_name AS original_teacher_name,
           ts.substitute_teacher_id, u2.full_name AS substitute_teacher_name,
           ts.class_id, c.class_name, c.section,
           ts.subject_id, s.subject_name,
           ts.substitution_date, ts.period_number, ts.reason, ts.status
    FROM teacher_substitutions ts
    JOIN users u1 ON ts.original_teacher_id = u1.user_id
    JOIN users u2 ON ts.substitute_teacher_id = u2.user_id
    JOIN classes c ON ts.class_id = c.class_id
    LEFT JOIN subjects s ON ts.subject_id = s.subject_id
    WHERE (@teacher_id IS NULL OR ts.original_teacher_id = @teacher_id OR ts.substitute_teacher_id = @teacher_id)
      AND (@start_date IS NULL OR ts.substitution_date >= @start_date)
      AND (@end_date IS NULL OR ts.substitution_date <= @end_date)
    ORDER BY ts.substitution_date DESC;
END
GO

-- =============================================
-- Create Teacher Substitution
-- =============================================
IF OBJECT_ID('sp_Teacher_CreateSubstitution', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_CreateSubstitution;
GO
CREATE PROCEDURE sp_Teacher_CreateSubstitution
    @original_teacher_id INT,
    @substitute_teacher_id INT,
    @class_id INT,
    @subject_id INT = NULL,
    @substitution_date DATE,
    @period_number INT,
    @reason NVARCHAR(500) = NULL,
    @created_by INT
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO teacher_substitutions (original_teacher_id, substitute_teacher_id, class_id, subject_id, substitution_date, period_number, reason, created_by)
    VALUES (@original_teacher_id, @substitute_teacher_id, @class_id, @subject_id, @substitution_date, @period_number, @reason, @created_by);
    SELECT SCOPE_IDENTITY() AS substitution_id;
END
GO

-- =============================================
-- Get Available Substitute Teachers
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAvailableSubstitutes', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAvailableSubstitutes;
GO
CREATE PROCEDURE sp_Teacher_GetAvailableSubstitutes
    @day_of_week INT,
    @period_number INT,
    @academic_year INT,
    @exclude_teacher_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SELECT u.user_id AS teacher_id, u.full_name AS teacher_name, s.designation
    FROM users u
    LEFT JOIN staff s ON u.user_id = s.user_id
    WHERE u.role_id IN (SELECT role_id FROM roles WHERE role_name LIKE '%Teacher%' OR role_name = 'Staff')
      AND u.is_active = 1
      AND (@exclude_teacher_id IS NULL OR u.user_id != @exclude_teacher_id)
      AND u.user_id NOT IN (
          SELECT teacher_id FROM teacher_periods
          WHERE day_of_week = @day_of_week AND period_number = @period_number AND academic_year = @academic_year AND is_active = 1
      )
    ORDER BY u.full_name;
END
GO

-- =============================================
-- Get Absent Teachers With Periods
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAbsentWithPeriods', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAbsentWithPeriods;
GO
CREATE PROCEDURE sp_Teacher_GetAbsentWithPeriods
    @attendance_date DATE
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @day_of_week INT = DATEPART(WEEKDAY, @attendance_date);

    SELECT
        ta.teacher_id,
        u.full_name as teacher_name,
        ta.attendance_date,
        ta.status
    FROM teacher_attendance ta
    JOIN users u ON ta.teacher_id = u.user_id
    WHERE ta.attendance_date = @attendance_date AND ta.status IN ('Absent', 'Leave');
END
GO

-- =============================================
-- Get Absent Teacher Periods for Substitution
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAbsentTeacherPeriods', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAbsentTeacherPeriods;
GO
CREATE PROCEDURE sp_Teacher_GetAbsentTeacherPeriods
    @teacher_id INT,
    @attendance_date DATE
AS
BEGIN
    SET NOCOUNT ON;
    DECLARE @day_of_week INT = DATEPART(WEEKDAY, @attendance_date);

    SELECT
        tp.period_id,
        tp.teacher_id,
        u.full_name as teacher_name,
        tp.class_id,
        c.class_name,
        c.section,
        tp.subject_id,
        s.subject_name,
        tp.day_of_week,
        tp.period_number,
        CONVERT(VARCHAR(8), tp.start_time, 108) as start_time,
        CONVERT(VARCHAR(8), tp.end_time, 108) as end_time,
        tp.academic_year,
        tp.is_active
    FROM teacher_periods tp
    JOIN users u ON tp.teacher_id = u.user_id
    JOIN classes c ON tp.class_id = c.class_id
    LEFT JOIN subjects s ON tp.subject_id = s.subject_id
    WHERE tp.teacher_id = @teacher_id AND tp.day_of_week = @day_of_week AND tp.is_active = 1
    AND NOT EXISTS (
        SELECT 1 FROM period_substitutions ps
        WHERE ps.original_period_id = tp.period_id
        AND ps.substitution_date = @attendance_date
    )
    ORDER BY tp.period_number;
END
GO

-- =============================================
-- Get Period Info for Substitution
-- =============================================
IF OBJECT_ID('sp_Teacher_GetPeriodInfo', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetPeriodInfo;
GO
CREATE PROCEDURE sp_Teacher_GetPeriodInfo
    @period_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT start_time, end_time, day_of_week
    FROM teacher_periods
    WHERE period_id = @period_id;
END
GO

-- =============================================
-- Check Substitute Teacher Conflict
-- =============================================
IF OBJECT_ID('sp_Teacher_CheckSubstituteConflict', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_CheckSubstituteConflict;
GO
CREATE PROCEDURE sp_Teacher_CheckSubstituteConflict
    @substitute_teacher_id INT,
    @day_of_week INT,
    @start_time TIME,
    @end_time TIME
AS
BEGIN
    SET NOCOUNT ON;
    SELECT COUNT(*) as count FROM teacher_periods tp
    WHERE tp.teacher_id = @substitute_teacher_id AND tp.day_of_week = @day_of_week
    AND tp.is_active = 1
    AND (
        (tp.start_time <= @start_time AND tp.end_time > @start_time)
        OR (tp.start_time < @end_time AND tp.end_time >= @end_time)
        OR (tp.start_time >= @start_time AND tp.end_time <= @end_time)
    );
END
GO

-- =============================================
-- Create Period Substitution
-- =============================================
IF OBJECT_ID('sp_Teacher_CreatePeriodSubstitution', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_CreatePeriodSubstitution;
GO
CREATE PROCEDURE sp_Teacher_CreatePeriodSubstitution
    @original_period_id INT,
    @substitute_teacher_id INT,
    @substitution_date DATE,
    @remarks NVARCHAR(500) = NULL,
    @assigned_by INT
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO period_substitutions (original_period_id, substitute_teacher_id, substitution_date, remarks, assigned_by)
    VALUES (@original_period_id, @substitute_teacher_id, @substitution_date, @remarks, @assigned_by);
    SELECT SCOPE_IDENTITY() AS substitution_id;
END
GO

-- =============================================
-- Get Period Substitutions
-- =============================================
IF OBJECT_ID('sp_Teacher_GetPeriodSubstitutions', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetPeriodSubstitutions;
GO
CREATE PROCEDURE sp_Teacher_GetPeriodSubstitutions
    @substitution_date DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        ps.substitution_id,
        ps.original_period_id,
        ps.substitute_teacher_id,
        u.full_name as substitute_teacher_name,
        ps.substitution_date,
        ps.remarks,
        ps.assigned_by
    FROM period_substitutions ps
    JOIN users u ON ps.substitute_teacher_id = u.user_id
    WHERE (@substitution_date IS NULL OR ps.substitution_date = @substitution_date)
    ORDER BY ps.substitution_date DESC;
END
GO

-- =============================================
-- Get Teacher Attendance Summary
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAttendanceSummary', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAttendanceSummary;
GO
CREATE PROCEDURE sp_Teacher_GetAttendanceSummary
    @year INT,
    @month INT,
    @teacher_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        ta.teacher_id,
        u.full_name as teacher_name,
        SUM(CASE WHEN ta.status = 'Present' THEN 1 ELSE 0 END) as total_present,
        SUM(CASE WHEN ta.status = 'Absent' THEN 1 ELSE 0 END) as total_absent,
        SUM(CASE WHEN ta.status = 'Leave' THEN 1 ELSE 0 END) as total_leave,
        SUM(CASE WHEN ta.status = 'Half-Day' THEN 1 ELSE 0 END) as total_half_day,
        SUM(CASE WHEN ta.status = 'Late' THEN 1 ELSE 0 END) as total_late,
        COUNT(*) as working_days
    FROM teacher_attendance ta
    JOIN users u ON ta.teacher_id = u.user_id
    WHERE YEAR(ta.attendance_date) = @year AND MONTH(ta.attendance_date) = @month
      AND (@teacher_id IS NULL OR ta.teacher_id = @teacher_id)
    GROUP BY ta.teacher_id, u.full_name
    ORDER BY u.full_name;
END
GO

-- =============================================
-- Get Teacher Attendance Date Range
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAttendanceDateRange', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAttendanceDateRange;
GO
CREATE PROCEDURE sp_Teacher_GetAttendanceDateRange
    @start_date DATE,
    @end_date DATE,
    @teacher_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        ta.teacher_attendance_id,
        ta.teacher_id,
        u.full_name as teacher_name,
        ta.attendance_date,
        ta.status,
        CONVERT(VARCHAR(8), ta.check_in_time, 108) as check_in_time,
        CONVERT(VARCHAR(8), ta.check_out_time, 108) as check_out_time,
        ta.leave_type,
        ta.remarks
    FROM teacher_attendance ta
    JOIN users u ON ta.teacher_id = u.user_id
    WHERE ta.attendance_date BETWEEN @start_date AND @end_date
      AND (@teacher_id IS NULL OR ta.teacher_id = @teacher_id)
    ORDER BY ta.attendance_date DESC, u.full_name;
END
GO

PRINT 'Teacher management procedures created successfully';
GO

