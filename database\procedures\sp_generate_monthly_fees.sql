-- =============================================
-- Procedure: sp_GenerateMonthlyFees
-- Description: Generate monthly fee records for students
-- =============================================

USE SchoolManagementDB;
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GenerateMonthlyFees]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GenerateMonthlyFees;
GO

CREATE PROCEDURE sp_GenerateMonthlyFees
    @class_id INT = NULL,  -- NULL means all classes
    @academic_year INT,
    @start_month INT = 1,  -- Default: January
    @end_month INT = 12    -- Default: December
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        BEGIN TRANSACTION;
        
        DECLARE @generated_count INT = 0;
        DE<PERSON><PERSON><PERSON> @skipped_count INT = 0;
        
        -- Generate monthly fees for all active students in the class(es)
        INSERT INTO student_monthly_fees (
            student_id,
            class_id,
            academic_year,
            fee_month,
            fee_year,
            base_fee,
            discount_amount,
            final_fee,
            paid_amount,
            outstanding_amount,
            status,
            due_date
        )
        SELECT 
            s.student_id,
            s.class_id,
            @academic_year,
            months.month_num,
            @academic_year,
            ISNULL(cfs.monthly_fee, 0) AS base_fee,
            ISNULL(sfd.discount_amount, 0) AS discount_amount,
            ISNULL(cfs.monthly_fee, 0) - ISNULL(sfd.discount_amount, 0) AS final_fee,
            0 AS paid_amount,
            ISNULL(cfs.monthly_fee, 0) - ISNULL(sfd.discount_amount, 0) AS outstanding_amount,
            'Pending' AS status,
            DATEFROMPARTS(@academic_year, months.month_num, 10) AS due_date  -- Due on 10th of each month
        FROM students s
        CROSS JOIN (
            SELECT 1 AS month_num UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL 
            SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL 
            SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL 
            SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12
        ) months
        LEFT JOIN class_fee_structure cfs ON s.class_id = cfs.class_id 
            AND cfs.academic_year = @academic_year 
            AND cfs.is_active = 1
        LEFT JOIN student_fee_discounts sfd ON s.student_id = sfd.student_id 
            AND sfd.academic_year = @academic_year 
            AND sfd.is_active = 1
        WHERE s.is_active = 1
            AND (@class_id IS NULL OR s.class_id = @class_id)
            AND months.month_num BETWEEN @start_month AND @end_month
            AND NOT EXISTS (
                -- Don't create duplicate records
                SELECT 1 FROM student_monthly_fees smf
                WHERE smf.student_id = s.student_id
                    AND smf.academic_year = @academic_year
                    AND smf.fee_month = months.month_num
            );
        
        SET @generated_count = @@ROWCOUNT;
        
        -- Count skipped records (already exist)
        SELECT @skipped_count = COUNT(*)
        FROM students s
        CROSS JOIN (
            SELECT 1 AS month_num UNION ALL SELECT 2 UNION ALL SELECT 3 UNION ALL 
            SELECT 4 UNION ALL SELECT 5 UNION ALL SELECT 6 UNION ALL 
            SELECT 7 UNION ALL SELECT 8 UNION ALL SELECT 9 UNION ALL 
            SELECT 10 UNION ALL SELECT 11 UNION ALL SELECT 12
        ) months
        WHERE s.is_active = 1
            AND (@class_id IS NULL OR s.class_id = @class_id)
            AND months.month_num BETWEEN @start_month AND @end_month
            AND EXISTS (
                SELECT 1 FROM student_monthly_fees smf
                WHERE smf.student_id = s.student_id
                    AND smf.academic_year = @academic_year
                    AND smf.fee_month = months.month_num
            );
        
        COMMIT TRANSACTION;
        
        SELECT 
            'Success' AS Result,
            CONCAT('Generated ', @generated_count, ' monthly fee records. Skipped ', @skipped_count, ' existing records.') AS Message,
            @generated_count AS generated_count,
            @skipped_count AS skipped_count;
            
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
            
        DECLARE @ErrorMessage NVARCHAR(4000) = ERROR_MESSAGE();
        SELECT 
            'Error' AS Result,
            @ErrorMessage AS Message;
    END CATCH
END
GO

PRINT 'Stored procedure sp_GenerateMonthlyFees created successfully!';
GO

