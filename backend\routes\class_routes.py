"""
Class Management Routes
"""
from fastapi import APIRouter, HTTPException, Depends, status
from typing import List, Optional
from pydantic import BaseModel
from datetime import datetime
from auth import get_current_user
from database import execute_query, execute_non_query, execute_scalar, execute_procedure, execute_procedure_single
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


# Pydantic models
class ClassBase(BaseModel):
    class_name: str
    class_name_urdu: Optional[str] = None
    section: str
    academic_year: int  # Changed from str to int to match database
    class_teacher_id: Optional[int] = None
    capacity: int = 30


class ClassCreate(ClassBase):
    pass


class ClassUpdate(BaseModel):
    class_name: Optional[str] = None
    class_name_urdu: Optional[str] = None
    section: Optional[str] = None
    academic_year: Optional[int] = None  # Changed from str to int
    class_teacher_id: Optional[int] = None
    capacity: Optional[int] = None
    is_active: Optional[bool] = None


class ClassResponse(ClassBase):
    class_id: int
    is_active: bool
    created_at: datetime
    updated_at: datetime
    student_count: Optional[int] = 0

    class Config:
        from_attributes = True


class StudentInClass(BaseModel):
    student_id: int
    admission_number: str
    full_name: str
    full_name_urdu: Optional[str] = None
    father_name: str
    date_of_birth: datetime
    gender: str
    status: str
    roll_number: Optional[str] = None


@router.get("/", response_model=List[ClassResponse])
async def get_all_classes(current_user: dict = Depends(get_current_user)):
    """Get all classes with student count and teacher name"""
    try:
        # Use stored procedure
        results = execute_procedure('sp_GetAllClasses')
        return results
    except Exception as e:
        logger.warning(f"Stored procedure failed, using fallback query: {e}")
        # Fallback to direct SQL query
        try:
            fallback_query = """
                SELECT
                    c.class_id, c.class_name, c.class_name_urdu, c.section,
                    c.academic_year, c.class_teacher_id, c.capacity, c.is_active,
                    c.created_at, c.updated_at,
                    ISNULL((SELECT COUNT(*) FROM students s WHERE s.class_id = c.class_id AND s.is_active = 1), 0) as student_count
                FROM classes c
                WHERE c.is_active = 1
                ORDER BY c.class_name, c.section
            """
            results = execute_query(fallback_query)
            return results
        except Exception as e2:
            logger.error(f"Fallback query also failed: {e2}")
            return []


@router.get("/{class_id}", response_model=ClassResponse)
async def get_class(class_id: int, current_user: dict = Depends(get_current_user)):
    """Get a specific class by ID with teacher name"""
    result = execute_procedure_single('sp_GetClass', {'class_id': class_id})
    if not result:
        raise HTTPException(status_code=404, detail="Class not found")
    return result


@router.get("/{class_id}/students", response_model=List[StudentInClass])
async def get_class_students(class_id: int, current_user: dict = Depends(get_current_user)):
    """Get all students in a specific class"""
    results = execute_procedure('sp_GetClassStudents', {'class_id': class_id})
    return results if results else []


@router.post("/", response_model=ClassResponse, status_code=status.HTTP_201_CREATED)
async def create_class(class_data: ClassCreate, current_user: dict = Depends(get_current_user)):
    """Create a new class"""
    # Check if user has permission (Admin or Owner)
    if current_user['role_name'] not in ['Administrator', 'Owner']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can create classes"
        )
    
    # Use stored procedure to create class
    params = {
        'class_name': class_data.class_name,
        'class_name_urdu': class_data.class_name_urdu,
        'section': class_data.section,
        'academic_year': class_data.academic_year,
        'class_teacher_id': class_data.class_teacher_id,
        'capacity': class_data.capacity
    }
    created = execute_procedure_single('sp_CreateClass', params)
    if not created or created.get('Result') == 'Error':
        msg = created.get('Message') if created else 'Failed to create class'
        raise HTTPException(status_code=400, detail=msg)
    return created


@router.put("/{class_id}", response_model=ClassResponse)
async def update_class(class_id: int, class_data: ClassUpdate, current_user: dict = Depends(get_current_user)):
    """Update a class"""
    # Check if user has permission
    if current_user['role_name'] not in ['Administrator', 'Owner']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can update classes"
        )
    
    # Use stored procedure to update class
    params = {
        'class_id': class_id,
        'class_name': class_data.class_name,
        'class_name_urdu': class_data.class_name_urdu,
        'section': class_data.section,
        'academic_year': class_data.academic_year,
        'class_teacher_id': class_data.class_teacher_id,
        'capacity': class_data.capacity,
        'is_active': 1 if class_data.is_active else (0 if class_data.is_active is not None else None)
    }
    updated = execute_procedure_single('sp_UpdateClass', params)
    if not updated or updated.get('Result') == 'Error':
        msg = updated.get('Message') if updated else 'Failed to update class'
        raise HTTPException(status_code=400, detail=msg)

    return updated


@router.delete("/{class_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_class(class_id: int, current_user: dict = Depends(get_current_user)):
    """Delete a class (soft delete)"""
    # Check if user has permission
    if current_user['role_name'] not in ['Administrator', 'Owner']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can delete classes"
        )
    
    # Use stored procedure for delete (soft delete)
    result = execute_procedure_single('sp_DeleteClass', {'class_id': class_id})
    if not result or result.get('Result') == 'Error':
        msg = result.get('Message') if result else 'Failed to delete class'
        raise HTTPException(status_code=400, detail=msg)
    return None

