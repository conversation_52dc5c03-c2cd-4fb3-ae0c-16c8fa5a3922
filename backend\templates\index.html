<!DOCTYPE html>
<html lang="en" dir="ltr">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>School Management System - نظام انتظام مدرسہ</title>
    
    <!-- Bootstrap CSS -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    
    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    
    <!-- Urdu Font -->
    <link href="https://fonts.googleapis.com/css2?family=Noto+Nastaliq+Urdu:wght@400;700&display=swap" rel="stylesheet">
    
    <!-- HTMX -->
    <script src="https://unpkg.com/htmx.org@1.9.10"></script>
    
    <style>
        :root {
            --primary-color: #4CAF50;
            --secondary-color: #2196F3;
            --danger-color: #f44336;
            --warning-color: #ff9800;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background-color: #f5f5f5;
        }
        
        .urdu {
            font-family: 'Noto Nastaliq Urdu', serif;
            direction: rtl;
            text-align: right;
        }
        
        .navbar {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        
        .navbar-brand {
            font-weight: bold;
            color: white !important;
        }
        
        .sidebar {
            min-height: calc(100vh - 56px);
            background-color: white;
            box-shadow: 2px 0 5px rgba(0,0,0,0.1);
            padding: 20px 0;
        }
        
        .sidebar .nav-link {
            color: #333;
            padding: 12px 20px;
            margin: 5px 10px;
            border-radius: 8px;
            transition: all 0.3s;
        }
        
        .sidebar .nav-link:hover,
        .sidebar .nav-link.active {
            background-color: var(--primary-color);
            color: white;
        }
        
        .sidebar .nav-link i {
            margin-right: 10px;
            width: 20px;
        }
        
        .card {
            border: none;
            border-radius: 12px;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            transition: transform 0.3s;
        }
        
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
        }
        
        .card-header {
            background: linear-gradient(135deg, var(--primary-color), var(--secondary-color));
            color: white;
            border-radius: 12px 12px 0 0 !important;
            font-weight: bold;
        }
        
        .stat-card {
            padding: 20px;
            text-align: center;
        }
        
        .stat-card .icon {
            font-size: 48px;
            margin-bottom: 10px;
        }
        
        .stat-card .number {
            font-size: 32px;
            font-weight: bold;
            margin: 10px 0;
        }
        
        .stat-card .label {
            color: #666;
            font-size: 14px;
        }
        
        .btn-primary {
            background-color: var(--primary-color);
            border-color: var(--primary-color);
        }
        
        .btn-primary:hover {
            background-color: #45a049;
            border-color: #45a049;
        }
        
        .login-container {
            max-width: 400px;
            margin: 100px auto;
        }
        
        .login-card {
            padding: 40px;
        }
        
        .bilingual-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .bilingual-header .english {
            text-align: left;
        }
        
        .bilingual-header .urdu {
            text-align: right;
        }
        
        @media (max-width: 768px) {
            .sidebar {
                min-height: auto;
            }
            
            .bilingual-header {
                flex-direction: column;
            }
        }
    </style>
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="/">
                <i class="fas fa-school"></i> School Management System
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav ms-auto">
                    <li class="nav-item">
                        <a class="nav-link text-white" href="#" id="languageToggle">
                            <i class="fas fa-language"></i> اردو / English
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="#" id="userProfile">
                            <i class="fas fa-user"></i> <span id="username">Guest</span>
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link text-white" href="#" id="logoutBtn">
                            <i class="fas fa-sign-out-alt"></i> Logout
                        </a>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <div class="container-fluid">
        <div class="row">
            <!-- Sidebar -->
            <div class="col-md-2 sidebar" id="sidebar">
                <nav class="nav flex-column">
                    <a class="nav-link active" href="#" data-page="dashboard">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                    <a class="nav-link" href="#" data-page="students">
                        <i class="fas fa-user-graduate"></i> Students
                    </a>
                    <a class="nav-link" href="#" data-page="attendance">
                        <i class="fas fa-clipboard-check"></i> Attendance
                    </a>
                    <a class="nav-link" href="#" data-page="fees">
                        <i class="fas fa-money-bill-wave"></i> Fees
                    </a>
                    <a class="nav-link" href="#" data-page="exams">
                        <i class="fas fa-file-alt"></i> Exams
                    </a>
                    <a class="nav-link" href="#" data-page="reports">
                        <i class="fas fa-chart-bar"></i> Reports
                    </a>
                    <a class="nav-link" href="#" data-page="users">
                        <i class="fas fa-users"></i> Users
                    </a>
                    <a class="nav-link" href="#" data-page="settings">
                        <i class="fas fa-cog"></i> Settings
                    </a>
                </nav>
            </div>

            <!-- Main Content -->
            <div class="col-md-10 p-4" id="mainContent">
                <!-- Login Form (shown initially) -->
                <div id="loginSection">
                    <div class="login-container">
                        <div class="card login-card">
                            <div class="text-center mb-4">
                                <i class="fas fa-school" style="font-size: 64px; color: var(--primary-color);"></i>
                                <h2 class="mt-3">School Management System</h2>
                                <p class="urdu" style="font-size: 20px;">نظام انتظام مدرسہ</p>
                            </div>
                            <form id="loginForm">
                                <div class="mb-3">
                                    <label for="username" class="form-label">Username</label>
                                    <input type="text" class="form-control" id="loginUsername" required>
                                </div>
                                <div class="mb-3">
                                    <label for="password" class="form-label">Password</label>
                                    <input type="password" class="form-control" id="loginPassword" required>
                                </div>
                                <button type="submit" class="btn btn-primary w-100">
                                    <i class="fas fa-sign-in-alt"></i> Login
                                </button>
                            </form>
                            <div class="mt-3 text-center">
                                <small class="text-muted">Default: admin / Admin@123</small>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Dashboard (shown after login) -->
                <div id="dashboardSection" style="display: none;">
                    <div class="bilingual-header mb-4">
                        <div class="english">
                            <h2>Dashboard</h2>
                        </div>
                        <div class="urdu">
                            <h2>ڈیش بورڈ</h2>
                        </div>
                    </div>

                    <!-- Statistics Cards -->
                    <div class="row">
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="icon" style="color: var(--primary-color);">
                                    <i class="fas fa-user-graduate"></i>
                                </div>
                                <div class="number" id="totalStudents">0</div>
                                <div class="label">Total Students</div>
                                <div class="label urdu">کل طلباء</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="icon" style="color: var(--secondary-color);">
                                    <i class="fas fa-chalkboard-teacher"></i>
                                </div>
                                <div class="number" id="totalTeachers">0</div>
                                <div class="label">Total Teachers</div>
                                <div class="label urdu">کل اساتذہ</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="icon" style="color: var(--warning-color);">
                                    <i class="fas fa-clipboard-check"></i>
                                </div>
                                <div class="number" id="todayAttendance">0%</div>
                                <div class="label">Today's Attendance</div>
                                <div class="label urdu">آج کی حاضری</div>
                            </div>
                        </div>
                        <div class="col-md-3">
                            <div class="card stat-card">
                                <div class="icon" style="color: var(--danger-color);">
                                    <i class="fas fa-money-bill-wave"></i>
                                </div>
                                <div class="number" id="feeDefaulters">0</div>
                                <div class="label">Fee Defaulters</div>
                                <div class="label urdu">فیس بقایا جات</div>
                            </div>
                        </div>
                    </div>

                    <!-- Quick Actions -->
                    <div class="row mt-4">
                        <div class="col-md-12">
                            <div class="card">
                                <div class="card-header">
                                    Quick Actions - فوری اقدامات
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-3 mb-2">
                                            <button class="btn btn-primary w-100">
                                                <i class="fas fa-user-plus"></i> Add Student
                                            </button>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <button class="btn btn-success w-100">
                                                <i class="fas fa-clipboard-check"></i> Mark Attendance
                                            </button>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <button class="btn btn-info w-100">
                                                <i class="fas fa-money-bill"></i> Collect Fee
                                            </button>
                                        </div>
                                        <div class="col-md-3 mb-2">
                                            <button class="btn btn-warning w-100">
                                                <i class="fas fa-file-pdf"></i> Generate Report
                                            </button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Custom JS -->
    <script src="/static/js/app.js"></script>
</body>
</html>

