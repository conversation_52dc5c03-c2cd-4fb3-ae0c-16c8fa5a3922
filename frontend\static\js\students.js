// Students module (clean module)
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    SMS.loadStudentsPage = async function() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-graduate"></i> Students Management</h2>
                <button class="btn btn-primary" onclick="SMS.showAddStudentModal()"><i class="fas fa-plus"></i> Add New Student</button>
            </div>
            <div id="studentsTableContainer"><div class="text-center"><div class="spinner-border" role="status"></div></div></div>
        `;
        await SMS.loadStudents();
    };

    SMS.loadStudents = async function() {
        SMS.showLoadingInline('studentsTableContainer', 'Loading students...');
        try {
            const response = await fetch(SMS.API_BASE + '/students/');
            if (!response.ok) throw new Error('Failed to load');
            const students = await response.json();
            SMS.displayStudentsTable(students);
        } catch (error) {
            console.error('Error loading students:', error);
            document.getElementById('studentsTableContainer').innerHTML = '<div class="alert alert-danger">Failed to load students</div>';
        }
    };

    SMS.displayStudentsTable = function(students) {
        const container = document.getElementById('studentsTableContainer');
        if (!students || students.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">No students found</p>';
            return;
        }
        let html = '<div class="table-responsive"><table class="table table-striped"><thead><tr><th>Admission #</th><th>Name</th><th>Class</th><th>Actions</th></tr></thead><tbody>';
        students.forEach(s => {
            html += `<tr><td>${s.admission_number}</td><td>${s.full_name}</td><td>${s.class_name || 'N/A'}</td><td>
                <button class="btn btn-sm btn-info" onclick="SMS.viewStudent(${s.student_id})">View</button>
                <button class="btn btn-sm btn-warning" onclick="SMS.editStudent(${s.student_id})">Edit</button>
                <button class="btn btn-sm btn-danger" onclick="SMS.deleteStudent(${s.student_id})">Delete</button>
            </td></tr>`;
        });
        html += '</tbody></table></div>';
        container.innerHTML = html;
    };

    SMS.showAddStudentModal = async function() {
        const response = await fetch(SMS.API_BASE + '/classes', { headers: { 'Authorization': `Bearer ${SMS.authToken}` } });
        const classes = response.ok ? await response.json() : [];
        const classOptions = classes.map(cls => `<option value="${cls.class_id}">${cls.class_name} - ${cls.section}</option>`).join('');

        const { isConfirmed, value } = await Swal.fire({
            title: 'Add New Student',
            html: `
                <form id="addStudentForm" class="text-start">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Admission Number *</label>
                            <input type="text" class="form-control" id="admissionNumber" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Full Name *</label>
                            <input type="text" class="form-control" id="fullName" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Full Name (Urdu)</label>
                            <input type="text" class="form-control" id="fullNameUrdu" dir="rtl">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Father Name *</label>
                            <input type="text" class="form-control" id="fatherName" required>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Date of Birth *</label>
                            <input type="date" class="form-control" id="dateOfBirth" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Gender *</label>
                            <select class="form-control" id="gender" required>
                                <option value="">Select Gender</option>
                                <option value="Male">Male</option>
                                <option value="Female">Female</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Class *</label>
                            <select class="form-control" id="classId" required>
                                <option value="">Select Class</option>
                                ${classOptions}
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Parent Phone *</label>
                            <input type="tel" class="form-control" id="parentPhone" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Address</label>
                        <textarea class="form-control" id="address" rows="2"></textarea>
                    </div>
                </form>
            `,
            width: '800px',
            showCancelButton: true,
            confirmButtonText: 'Add Student',
            preConfirm: () => {
                const form = document.getElementById('addStudentForm');
                if (!form.checkValidity()) {
                    Swal.showValidationMessage('Please fill all required fields');
                    return false;
                }
                return {
                    admission_number: document.getElementById('admissionNumber').value,
                    full_name: document.getElementById('fullName').value,
                    full_name_urdu: document.getElementById('fullNameUrdu').value || null,
                    father_name: document.getElementById('fatherName').value,
                    date_of_birth: document.getElementById('dateOfBirth').value,
                    gender: document.getElementById('gender').value,
                    class_id: parseInt(document.getElementById('classId').value),
                    parent_phone: document.getElementById('parentPhone').value,
                    address: document.getElementById('address').value || null
                };
            }
        });

        if (isConfirmed) await SMS.createStudent(value);
    };

    SMS.createStudent = async function(studentData) {
        try {
            const response = await fetch(SMS.API_BASE + '/students', {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(studentData)
            });

            if (response.ok) {
                SMS.showSuccess('Student added successfully!');
                SMS.loadStudents();
            } else {
                const error = await response.json();
                SMS.showError(error.detail || 'Failed to add student');
            }
        } catch (error) {
            console.error('Error creating student:', error);
            SMS.showError('Error creating student');
        }
    };

    SMS.viewStudent = async function(studentId) {
        try {
            const response = await fetch(`${SMS.API_BASE}/students/${studentId}`, { headers: { 'Authorization': `Bearer ${SMS.authToken}` } });
            if (response.ok) {
                const student = await response.json();
                Swal.fire({
                    title: 'Student Details',
                    html: `
                        <div class="text-start">
                            <p><strong>Admission Number:</strong> ${student.admission_number}</p>
                            <p><strong>Name:</strong> ${student.full_name}</p>
                            ${student.full_name_urdu ? `<p><strong>نام:</strong> ${student.full_name_urdu}</p>` : ''}
                            <p><strong>Father Name:</strong> ${student.father_name}</p>
                            <p><strong>Date of Birth:</strong> ${new Date(student.date_of_birth).toLocaleDateString()}</p>
                            <p><strong>Gender:</strong> ${student.gender}</p>
                            <p><strong>Class:</strong> ${student.class_name || 'N/A'} ${student.section || ''}</p>
                            <p><strong>CNIC:</strong> ${student.cnic || 'N/A'}</p>
                            <p><strong>B-Form:</strong> ${student.b_form || 'N/A'}</p>
                            <p><strong>Parent Phone:</strong> ${student.parent_phone || 'N/A'}</p>
                            <p><strong>Parent Email:</strong> ${student.parent_email || 'N/A'}</p>
                            <p><strong>Address:</strong> ${student.address || 'N/A'}</p>
                            <p><strong>Status:</strong> <span class="badge bg-${student.status === 'Active' ? 'success' : 'secondary'}">${student.status}</span></p>
                        </div>
                    `,
                    width: '600px'
                });
            }
        } catch (error) {
            console.error('Error viewing student:', error);
            SMS.showError('Error loading student details');
        }
    };

    SMS.editStudent = function(studentId) {
        SMS.showError('Edit functionality coming soon!');
    };

    SMS.deleteStudent = function(studentId) {
        Swal.fire({
            title: 'Are you sure?',
            text: 'Do you want to discharge this student?',
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#3085d6',
            confirmButtonText: 'Yes, discharge student'
        }).then(async (result) => {
            if (result.isConfirmed) {
                try {
                    const response = await fetch(`${SMS.API_BASE}/students/${studentId}`, {
                        method: 'DELETE',
                        headers: { 'Authorization': `Bearer ${SMS.authToken}` }
                    });

                    if (response.ok) {
                        SMS.showSuccess('Student discharged successfully!');
                        SMS.loadStudents();
                    } else {
                        SMS.showError('Failed to discharge student');
                    }
                } catch (error) {
                    console.error('Error deleting student:', error);
                    SMS.showError('Error discharging student');
                }
            }
        });
    };

    // Backwards-compatible aliases (some templates may call global functions)
    window.showAddStudentModal = function(){ return SMS.showAddStudentModal(); };
    window.createStudent = function(data){ return SMS.createStudent(data); };
    window.viewStudent = function(id){ return SMS.viewStudent(id); };
    window.editStudent = function(id){ return SMS.editStudent(id); };
    window.deleteStudent = function(id){ return SMS.deleteStudent(id); };

})(window);
