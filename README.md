# School Management System (SMS)

A comprehensive web-based School Management System built with FastAPI (Python) backend and vanilla JavaScript frontend.

## 🚀 Quick Start

### Start the Server
```bash
# Windows PowerShell
.\start_server.ps1

# Windows Command Prompt
start_server.bat
```

### Stop the Server
```bash
# Windows PowerShell
.\stop_server.ps1

# Windows Command Prompt
stop_server.bat
```

### Access the Application
- Open your browser and go to `http://127.0.0.1:8000`
- Default login: **admin** / **admin123**

## 📋 Features

- **Student Management**: Add, edit, delete, and manage student records
- **Teacher Management**: Manage teacher profiles and assignments
- **Class Management**: Create and manage classes and sections
- **Subject Management**: Handle subject creation and assignments
- **Attendance System**: Track student and teacher attendance
- **Fee Management**: Handle fee collection and tracking
- **Exam Management**: Manage exams and results
- **Timetable Management**: Create and manage class timetables
- **User Management**: Role-based access control
- **Dashboard**: Overview of key metrics and statistics

## 🛠 Technology Stack

- **Backend**: FastAP<PERSON> (Python)
- **Frontend**: Vanilla JavaScript, HTML5, CSS3
- **Database**: SQL Server
- **Authentication**: JWT tokens
- **UI Framework**: Bootstrap 5

## 📁 Project Structure

```
SMS/
├── backend/                 # Backend API server
│   ├── routes/             # API route handlers (modular)
│   ├── services/           # Business logic services
│   ├── models.py           # Data models
│   ├── database.py         # Database connection
│   ├── auth.py            # Authentication logic
│   ├── config.py          # Configuration settings
│   └── main.py            # Application entry point
├── frontend/               # Frontend web application
│   ├── static/            # Static assets
│   │   ├── js/           # JavaScript modules (modular)
│   │   ├── css/          # Stylesheets
│   │   └── images/       # Images and icons
│   └── templates/         # HTML templates
├── database/              # Database scripts
│   ├── procedures/        # Stored procedures (modular)
│   └── *.sql             # Database setup scripts
├── scripts/               # Utility scripts
├── venv/                  # Python virtual environment
├── requirements.txt       # Python dependencies
├── start_server.ps1       # Server start script (PowerShell)
├── start_server.bat       # Server start script (Batch)
├── stop_server.ps1        # Server stop script (PowerShell)
└── stop_server.bat        # Server stop script (Batch)
```

## 🔧 Manual Setup (if needed)

### Prerequisites
- Python 3.8 or higher
- SQL Server (Express or full version)
- Modern web browser

### Database Setup
1. Create a SQL Server database named `SchoolManagementDB`
2. Run the SQL scripts in the `database/` folder in order:
   - `01_create_database.sql`
   - `02_create_tables.sql`
   - `03_seed_data.sql`
   - `04_stored_procedures.sql`

### Configuration
- Update `backend/config.py` with your database connection details if needed

## 📚 API Documentation

The API documentation is available at `http://127.0.0.1:8000/docs` when the server is running.

## 🐛 Troubleshooting

### Common Issues

1. **Server won't start**
   - Run `stop_server.ps1` first to kill any existing processes
   - Check if Python is installed: `python --version`
   - Check if port 8000 is available

2. **Database connection errors**
   - Verify SQL Server is running
   - Check connection string in `backend/config.py`
   - Ensure database exists and tables are created

3. **Login issues**
   - Default credentials: admin / admin123
   - Check browser console for JavaScript errors
   - Verify API endpoints are responding

## 📝 License

This project is licensed under the MIT License.

