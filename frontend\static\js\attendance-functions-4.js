/**
 * Attendance Management Functions - Part 4
 * Contains reporting and display functions
 */

function displayAttendanceRecords(records) {
    const container = document.getElementById('attendanceRecordsContainer');

    if (records.length === 0) {
        container.innerHTML = '<p class="text-center text-muted">No attendance records found</p>';
        return;
    }

    let html = `
        <div class="table-responsive">
            <table class="table table-striped">
                <thead>
                    <tr>
                        <th>Class</th>
                        <th>Total Students</th>
                        <th>Present</th>
                        <th>Absent</th>
                        <th>Leave</th>
                        <th>Late</th>
                        <th>Attendance %</th>
                    </tr>
                </thead>
                <tbody>
    `;

    records.forEach(record => {
        html += `
            <tr>
                <td>${record.class_name} - ${record.section}</td>
                <td>${record.total_students}</td>
                <td><span class="badge bg-success">${record.present_count}</span></td>
                <td><span class="badge bg-danger">${record.absent_count}</span></td>
                <td><span class="badge bg-warning">${record.leave_count}</span></td>
                <td><span class="badge bg-info">${record.late_count}</span></td>
                <td><strong>${record.attendance_percentage}%</strong></td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
    `;

    container.innerHTML = html;
}

async function loadMonthlyReport() {
    const classId = document.getElementById('monthlyClassFilter').value;
    const month = document.getElementById('monthlyMonth').value;
    const year = document.getElementById('monthlyYear').value;

    if (!classId) {
        showError('Please select a class');
        return;
    }

    const container = document.getElementById('monthlyReportContainer');
    showLoadingInline('monthlyReportContainer', 'Loading monthly report...');

    try {
        const url = `${API_BASE}/attendance/class/${classId}/monthly-report?year=${year}&month=${month}`;

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const report = await response.json();
            displayMonthlyReport(report, month, year);
        } else {
            showError('Failed to load monthly report');
            container.innerHTML = '<p class="text-center text-danger">Failed to load monthly report</p>';
        }
    } catch (error) {
        console.error('Error loading monthly report:', error);
        showError('Error loading monthly report');
        container.innerHTML = '<p class="text-center text-danger">Error loading monthly report</p>';
    }
}

function displayMonthlyReport(report, month, year) {
    const container = document.getElementById('monthlyReportContainer');

    if (!report || report.length === 0) {
        container.innerHTML = '<p class="text-center text-muted">No attendance data found for this month</p>';
        return;
    }

    const monthNames = ['', 'January', 'February', 'March', 'April', 'May', 'June',
                        'July', 'August', 'September', 'October', 'November', 'December'];

    let html = `
        <div class="mb-3">
            <h5><i class="fas fa-calendar"></i> ${monthNames[month]} ${year} - Attendance Report</h5>
        </div>
        <div class="table-responsive">
            <table class="table table-striped table-bordered">
                <thead class="table-dark">
                    <tr>
                        <th>Admission #</th>
                        <th>Student Name</th>
                        <th>Total Days</th>
                        <th>Present</th>
                        <th>Absent</th>
                        <th>Leave</th>
                        <th>Late</th>
                        <th>Attendance %</th>
                        <th>Status</th>
                    </tr>
                </thead>
                <tbody>
    `;

    report.forEach(student => {
        const percentage = student.attendance_percentage || 0;
        let statusBadge = '';
        if (percentage >= 90) {
            statusBadge = '<span class="badge bg-success">Excellent</span>';
        } else if (percentage >= 75) {
            statusBadge = '<span class="badge bg-primary">Good</span>';
        } else if (percentage >= 60) {
            statusBadge = '<span class="badge bg-warning">Average</span>';
        } else {
            statusBadge = '<span class="badge bg-danger">Poor</span>';
        }

        html += `
            <tr>
                <td>${student.admission_number || 'N/A'}</td>
                <td>${student.full_name}</td>
                <td>${student.total_days}</td>
                <td><span class="badge bg-success">${student.present_count}</span></td>
                <td><span class="badge bg-danger">${student.absent_count}</span></td>
                <td><span class="badge bg-warning">${student.leave_count}</span></td>
                <td><span class="badge bg-info">${student.late_count}</span></td>
                <td><strong>${percentage.toFixed(1)}%</strong></td>
                <td>${statusBadge}</td>
            </tr>
        `;
    });

    html += `
                </tbody>
            </table>
        </div>
        <div class="mt-3">
            <button class="btn btn-success" onclick="exportMonthlyReport()">
                <i class="fas fa-file-excel"></i> Export to Excel
            </button>
            <button class="btn btn-primary" onclick="printMonthlyReport()">
                <i class="fas fa-print"></i> Print Report
            </button>
        </div>
    `;

    container.innerHTML = html;
}

function exportMonthlyReport() {
    showError('Export functionality coming soon!');
}

function printMonthlyReport() {
    window.print();
}

// Export functions to global scope
window.displayAttendanceRecords = displayAttendanceRecords;
window.loadMonthlyReport = loadMonthlyReport;
window.displayMonthlyReport = displayMonthlyReport;
window.exportMonthlyReport = exportMonthlyReport;
window.printMonthlyReport = printMonthlyReport;
