2025-12-05 14:33:44,100 - main - INFO - Starting School Management System v1.0.0
2025-12-05 14:33:44,229 - main - INFO - Database connection successful
2025-12-05 16:13:58,135 - main - INFO - [REQUEST] GET /api/health
2025-12-05 16:13:58,135 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:13:58,160 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:34,444 - main - INFO - [REQUEST] GET /api/students/
2025-12-05 16:21:34,444 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:34,445 - main - INFO - [RESPONSE] Status: 403
2025-12-05 16:21:46,178 - main - INFO - [REQUEST] GET /
2025-12-05 16:21:46,178 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:46,190 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:54,976 - main - INFO - [REQUEST] GET /
2025-12-05 16:21:54,976 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:54,977 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:55,143 - main - INFO - [REQUEST] GET /static/js/app.js
2025-12-05 16:21:55,154 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:55,159 - main - INFO - [REQUEST] GET /static/js/teacher_pages.js
2025-12-05 16:21:55,163 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:55,168 - main - INFO - [REQUEST] GET /static/js/timetable.js
2025-12-05 16:21:55,171 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:55,172 - main - INFO - [REQUEST] GET /static/js/timetable_grid.js
2025-12-05 16:21:55,176 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:55,276 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:55,277 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:55,279 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:55,280 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,266 - main - INFO - [REQUEST] GET /static/js/module-loader.js
2025-12-05 16:21:56,267 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,270 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,280 - main - INFO - [REQUEST] GET /static/js/core.module.js
2025-12-05 16:21:56,281 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,284 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,291 - main - INFO - [REQUEST] GET /static/js/api.js
2025-12-05 16:21:56,291 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,293 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,299 - main - INFO - [REQUEST] GET /static/js/auth.js
2025-12-05 16:21:56,299 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,301 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,308 - main - INFO - [REQUEST] GET /static/js/router.js
2025-12-05 16:21:56,308 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,310 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,314 - main - INFO - [REQUEST] GET /static/js/dashboard.js
2025-12-05 16:21:56,315 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,317 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,321 - main - INFO - [REQUEST] GET /static/js/users.js
2025-12-05 16:21:56,322 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,324 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,328 - main - INFO - [REQUEST] GET /static/js/classes.js
2025-12-05 16:21:56,329 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,331 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,335 - main - INFO - [REQUEST] GET /static/js/students.module.js
2025-12-05 16:21:56,335 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,338 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,343 - main - INFO - [REQUEST] GET /static/js/attendance.module.js
2025-12-05 16:21:56,344 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,345 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,349 - main - INFO - [REQUEST] GET /static/js/fees.module.js
2025-12-05 16:21:56,349 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,351 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,357 - main - INFO - [REQUEST] GET /static/js/exams.js
2025-12-05 16:21:56,357 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,358 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,364 - main - INFO - [REQUEST] GET /static/js/reports.js
2025-12-05 16:21:56,364 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,365 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,369 - main - INFO - [REQUEST] GET /static/js/subjects.js
2025-12-05 16:21:56,369 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,372 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,376 - main - INFO - [REQUEST] GET /static/js/staff.js
2025-12-05 16:21:56,376 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,379 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,387 - main - INFO - [REQUEST] GET /static/js/holidays.js
2025-12-05 16:21:56,387 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,390 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,395 - main - INFO - [REQUEST] GET /static/js/settings.js
2025-12-05 16:21:56,395 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,396 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,400 - main - INFO - [REQUEST] GET /static/js/student-modals.js
2025-12-05 16:21:56,401 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,402 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,408 - main - INFO - [REQUEST] GET /static/js/attendance-functions.js
2025-12-05 16:21:56,409 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,411 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,415 - main - INFO - [REQUEST] GET /static/js/attendance-functions-2.js
2025-12-05 16:21:56,415 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,417 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,421 - main - INFO - [REQUEST] GET /static/js/attendance-functions-3.js
2025-12-05 16:21:56,422 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,424 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,428 - main - INFO - [REQUEST] GET /static/js/attendance-functions-4.js
2025-12-05 16:21:56,428 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,430 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:21:56,434 - main - INFO - [REQUEST] GET /static/js/fee-functions.js
2025-12-05 16:21:56,435 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,436 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:21:56,440 - main - INFO - [REQUEST] GET /static/js/report-functions.js
2025-12-05 16:21:56,440 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,442 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:21:56,445 - main - INFO - [REQUEST] GET /static/js/subject-functions.js
2025-12-05 16:21:56,445 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,446 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:21:56,450 - main - INFO - [REQUEST] GET /static/js/staff-functions.js
2025-12-05 16:21:56,450 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,452 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:21:56,456 - main - INFO - [REQUEST] GET /static/js/holiday-functions.js
2025-12-05 16:21:56,457 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,460 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:21:56,464 - main - INFO - [REQUEST] GET /static/js/teacher-functions.js
2025-12-05 16:21:56,464 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,466 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:21:56,469 - main - INFO - [REQUEST] GET /static/js/monthly-overview.js
2025-12-05 16:21:56,469 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,472 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:21:56,476 - main - INFO - [REQUEST] GET /static/js/sms-namespace.js
2025-12-05 16:21:56,476 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:56,477 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:21:58,347 - main - INFO - [REQUEST] GET /favicon.ico
2025-12-05 16:21:58,348 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:21:58,348 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:11,844 - main - INFO - [REQUEST] POST /api/auth/login
2025-12-05 16:22:11,845 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:22:12,219 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:12,237 - main - INFO - [REQUEST] GET /api/auth/me
2025-12-05 16:22:12,238 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:12,247 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:12,256 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:12,263 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-05 16:22:12,263 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:12,264 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:12,281 - main - INFO - [REQUEST] GET /api/students
2025-12-05 16:22:12,281 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:12,286 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:22:12,287 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:12,289 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:12,310 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:12,360 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:12,361 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-05 16:22:12,362 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:12,362 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:22:12,363 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:12,375 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:12,379 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:12,380 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:22:18,463 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:22:18,464 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:18,467 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:18,470 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:20,096 - main - INFO - [REQUEST] GET /api/staff/
2025-12-05 16:22:20,097 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:20,101 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:20,117 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:21,353 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-05 16:22:21,354 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:21,356 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:21,365 - main - INFO - [REQUEST] GET /api/students
2025-12-05 16:22:21,366 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:21,368 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:21,373 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:22:21,374 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:21,376 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-05 16:22:21,377 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:21,378 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:22:21,379 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:21,383 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:21,393 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:21,395 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:21,396 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:21,398 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:22:23,559 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-05 16:22:23,559 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:23,560 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:23,564 - main - INFO - [REQUEST] GET /api/students
2025-12-05 16:22:23,564 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:23,565 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-05 16:22:23,566 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:23,566 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:22:23,566 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:23,567 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:22:23,567 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:23,570 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:23,574 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:23,576 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:23,577 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:23,579 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:22:23,579 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:24,928 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-05 16:22:24,929 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:24,931 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:24,940 - main - INFO - [REQUEST] GET /api/students
2025-12-05 16:22:24,941 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:24,944 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:22:24,944 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:24,949 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:24,956 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:22:24,956 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:24,957 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-05 16:22:24,958 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:24,959 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:24,964 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:24,967 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:24,970 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:22:24,973 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:25,748 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-05 16:22:25,748 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:25,749 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:25,756 - main - INFO - [REQUEST] GET /api/students
2025-12-05 16:22:25,756 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:25,757 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:22:25,757 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:25,760 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:25,763 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-05 16:22:25,763 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:25,763 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:22:25,764 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:25,764 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:25,768 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:25,769 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:25,771 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:25,773 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:22:26,511 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-05 16:22:26,512 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:26,513 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:26,520 - main - INFO - [REQUEST] GET /api/students
2025-12-05 16:22:26,520 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:26,523 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:22:26,523 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:26,524 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:22:26,524 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:26,527 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:26,531 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:26,534 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-05 16:22:26,534 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:26,535 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:26,537 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:22:26,538 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:26,540 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:27,303 - main - INFO - [REQUEST] GET /api/timetable/config/
2025-12-05 16:22:27,304 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:27,305 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:27,314 - main - INFO - [REQUEST] GET /api/timetable/periods/templates
2025-12-05 16:22:27,316 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:27,322 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:27,326 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:22:29,614 - main - INFO - [REQUEST] GET /api/classes/
2025-12-05 16:22:29,615 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:29,620 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:29,625 - database - ERROR - Database operation error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:22:29,625 - database - ERROR - Database connection error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:22:29,626 - routes.class_routes - WARNING - Stored procedure failed, using fallback query: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:22:29,645 - main - INFO - [REQUEST] GET /api/staff/
2025-12-05 16:22:29,646 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:29,650 - main - INFO - [REQUEST] GET /api/subjects/
2025-12-05 16:22:29,651 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:29,655 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:29,661 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:29,668 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:29,669 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:29,671 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:32,108 - main - INFO - [REQUEST] GET /api/staff/
2025-12-05 16:22:32,109 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:32,114 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:32,118 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:32,125 - main - INFO - [REQUEST] GET /api/teachers/schedules/
2025-12-05 16:22:32,126 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:32,130 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:34,326 - main - INFO - [REQUEST] GET /api/teachers/attendance/summary
2025-12-05 16:22:34,326 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:34,330 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:34,332 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:22:36,455 - main - INFO - [REQUEST] GET /api/classes/
2025-12-05 16:22:36,456 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:36,460 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:36,462 - database - ERROR - Database operation error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:22:36,462 - database - ERROR - Database connection error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:22:36,463 - routes.class_routes - WARNING - Stored procedure failed, using fallback query: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:22:36,465 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:44,675 - main - INFO - [REQUEST] GET /api/subjects/
2025-12-05 16:22:44,676 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:44,681 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:22:44,686 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:22:51,669 - main - INFO - [REQUEST] GET /api/students/
2025-12-05 16:22:51,670 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:22:51,672 - main - INFO - [RESPONSE] Status: 403
2025-12-05 16:22:56,282 - main - INFO - [REQUEST] GET /api/attendance/
2025-12-05 16:22:56,282 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:22:56,284 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:22:59,829 - main - INFO - [REQUEST] GET /api/holidays
2025-12-05 16:22:59,830 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:22:59,831 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:23:06,229 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-05 16:23:06,229 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:06,230 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:23:06,236 - main - INFO - [REQUEST] GET /api/students
2025-12-05 16:23:06,237 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:06,239 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:23:06,239 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:06,240 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:23:06,245 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:23:06,249 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:23:06,251 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-05 16:23:06,252 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:06,253 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:23:06,253 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:06,256 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:23:06,257 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:23:06,258 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:23:10,805 - main - INFO - [REQUEST] GET /api/exams
2025-12-05 16:23:10,805 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:10,806 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:23:17,346 - main - INFO - [REQUEST] GET /api/exams/
2025-12-05 16:23:17,346 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:17,351 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:23:17,356 - database - ERROR - Database operation error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetExams'. (2812) (SQLExecDirectW)")
2025-12-05 16:23:17,357 - database - ERROR - Database connection error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetExams'. (2812) (SQLExecDirectW)")
2025-12-05 16:23:17,357 - routes.exam_management_routes - ERROR - Error fetching exams: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetExams'. (2812) (SQLExecDirectW)")
2025-12-05 16:23:17,358 - main - INFO - [RESPONSE] Status: 500
2025-12-05 16:23:17,360 - main - INFO - [REQUEST] GET /api/subjects/
2025-12-05 16:23:17,360 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:17,361 - main - INFO - [REQUEST] GET /api/classes/
2025-12-05 16:23:17,361 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:17,363 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:23:17,394 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:23:17,404 - database - ERROR - Database operation error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:23:17,404 - database - ERROR - Database connection error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:23:17,408 - routes.class_routes - WARNING - Stored procedure failed, using fallback query: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:23:17,426 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:23:17,427 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:23:36,220 - main - INFO - [REQUEST] GET /api/exams
2025-12-05 16:23:36,221 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:36,221 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:23:43,701 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:23:43,701 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:43,705 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:23:43,706 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:23:46,011 - main - INFO - [REQUEST] GET /api/classes/
2025-12-05 16:23:46,012 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:23:46,017 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:23:46,022 - database - ERROR - Database operation error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:23:46,023 - database - ERROR - Database connection error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:23:46,024 - routes.class_routes - WARNING - Stored procedure failed, using fallback query: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-05 16:23:46,027 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:23:56,695 - main - INFO - [REQUEST] GET /api/reports/fees/defaulters-excel
2025-12-05 16:23:56,696 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:23:56,697 - main - INFO - [RESPONSE] Status: 403
2025-12-05 16:24:10,585 - main - INFO - [REQUEST] GET /api/attendance/
2025-12-05 16:24:10,585 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:24:10,586 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:24:22,079 - main - INFO - [REQUEST] GET /docs
2025-12-05 16:24:22,080 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:24:22,080 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:24:36,934 - main - INFO - [REQUEST] GET /api/docs
2025-12-05 16:24:36,934 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:24:36,935 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:24:38,243 - main - INFO - [REQUEST] GET /api/openapi.json
2025-12-05 16:24:38,243 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:24:38,593 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,416 - main - INFO - [REQUEST] GET /
2025-12-05 16:43:13,417 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,419 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,454 - main - INFO - [REQUEST] GET /static/js/app.js
2025-12-05 16:43:13,455 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,457 - main - INFO - [REQUEST] GET /static/js/teacher_pages.js
2025-12-05 16:43:13,460 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,467 - main - INFO - [REQUEST] GET /static/js/timetable.js
2025-12-05 16:43:13,468 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,469 - main - INFO - [REQUEST] GET /static/js/timetable_grid.js
2025-12-05 16:43:13,469 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,471 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,473 - main - INFO - [RESPONSE] Status: 304
2025-12-05 16:43:13,477 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,478 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,580 - main - INFO - [REQUEST] GET /static/js/module-loader.js
2025-12-05 16:43:13,581 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,583 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,662 - main - INFO - [REQUEST] GET /api/auth/me
2025-12-05 16:43:13,663 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:13,685 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:43:13,689 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,690 - main - INFO - [REQUEST] GET /static/js/router.js
2025-12-05 16:43:13,690 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,694 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,699 - main - INFO - [REQUEST] GET /static/js/dashboard.js
2025-12-05 16:43:13,700 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,701 - main - INFO - [RESPONSE] Status: 304
2025-12-05 16:43:13,708 - main - INFO - [REQUEST] GET /static/js/classes.js
2025-12-05 16:43:13,709 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,712 - main - INFO - [RESPONSE] Status: 304
2025-12-05 16:43:13,721 - main - INFO - [REQUEST] GET /static/js/fees.module.js
2025-12-05 16:43:13,721 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,723 - main - INFO - [RESPONSE] Status: 304
2025-12-05 16:43:13,729 - main - INFO - [REQUEST] GET /static/js/exams.js
2025-12-05 16:43:13,729 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,731 - main - INFO - [RESPONSE] Status: 304
2025-12-05 16:43:13,750 - main - INFO - [REQUEST] GET /static/js/student-modals.js
2025-12-05 16:43:13,751 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,752 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,757 - main - INFO - [REQUEST] GET /static/js/attendance-functions.js
2025-12-05 16:43:13,757 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,760 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,764 - main - INFO - [REQUEST] GET /static/js/attendance-functions-2.js
2025-12-05 16:43:13,764 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,766 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,770 - main - INFO - [REQUEST] GET /static/js/attendance-functions-3.js
2025-12-05 16:43:13,770 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,771 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,776 - main - INFO - [REQUEST] GET /static/js/attendance-functions-4.js
2025-12-05 16:43:13,776 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,778 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:13,782 - main - INFO - [REQUEST] GET /static/js/fee-functions.js
2025-12-05 16:43:13,782 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,784 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:13,787 - main - INFO - [REQUEST] GET /static/js/report-functions.js
2025-12-05 16:43:13,787 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,789 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:13,792 - main - INFO - [REQUEST] GET /static/js/subject-functions.js
2025-12-05 16:43:13,793 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,794 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:13,797 - main - INFO - [REQUEST] GET /static/js/staff-functions.js
2025-12-05 16:43:13,797 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,799 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:13,802 - main - INFO - [REQUEST] GET /static/js/holiday-functions.js
2025-12-05 16:43:13,802 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,804 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:13,807 - main - INFO - [REQUEST] GET /static/js/teacher-functions.js
2025-12-05 16:43:13,807 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,809 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:13,812 - main - INFO - [REQUEST] GET /static/js/monthly-overview.js
2025-12-05 16:43:13,812 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,814 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:13,816 - main - INFO - [REQUEST] GET /static/js/sms-namespace.js
2025-12-05 16:43:13,817 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:13,818 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:18,772 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-05 16:43:18,772 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:18,773 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:18,781 - main - INFO - [REQUEST] GET /api/students
2025-12-05 16:43:18,781 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:18,783 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:18,784 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:43:18,785 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:18,786 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-05 16:43:18,786 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:18,787 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:43:18,788 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:18,790 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:43:18,795 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:43:18,796 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:18,797 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:18,798 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:43:19,824 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-05 16:43:19,824 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:19,826 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:19,832 - main - INFO - [REQUEST] GET /api/students
2025-12-05 16:43:19,833 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:19,837 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:43:19,838 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:19,838 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-05 16:43:19,839 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:19,840 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:43:19,840 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:19,844 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:43:19,850 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:43:19,855 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:19,857 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:43:19,859 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:19,861 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:20,071 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-05 16:43:20,072 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:20,074 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:20,080 - main - INFO - [REQUEST] GET /api/students
2025-12-05 16:43:20,081 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:20,083 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:43:20,084 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:20,086 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:43:20,086 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:20,087 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-05 16:43:20,088 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:20,092 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:43:20,096 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:43:20,102 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:20,104 - main - INFO - [RESPONSE] Status: 422
2025-12-05 16:43:20,105 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:20,107 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:27,702 - main - INFO - [REQUEST] POST /api/auth/login
2025-12-05 16:43:27,702 - main - INFO - [HEADERS] Authorization: None...
2025-12-05 16:43:27,981 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:27,990 - main - INFO - [REQUEST] GET /api/auth/me
2025-12-05 16:43:27,990 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:27,996 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:43:28,000 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:28,005 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-05 16:43:28,005 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:28,007 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:28,018 - main - INFO - [REQUEST] GET /api/students
2025-12-05 16:43:28,019 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:28,022 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:28,029 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-05 16:43:28,030 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:28,030 - main - INFO - [REQUEST] GET /api/users/
2025-12-05 16:43:28,032 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:28,034 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-05 16:43:28,034 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-05 16:43:28,037 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:43:28,049 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-05 16:43:28,051 - main - INFO - [RESPONSE] Status: 404
2025-12-05 16:43:28,052 - main - INFO - [RESPONSE] Status: 200
2025-12-05 16:43:28,053 - main - INFO - [RESPONSE] Status: 422
2025-12-10 12:18:07,837 - main - INFO - Starting School Management System v1.0.0
2025-12-10 12:18:07,933 - main - INFO - Database connection successful
2025-12-10 12:33:51,948 - main - INFO - Starting School Management System v1.0.0
2025-12-10 12:33:52,058 - main - INFO - Database connection successful
