"""
Authentication Routes
"""
from fastapi import APIRouter, Depends, HTTPException, status
from models import LoginRequest, TokenResponse, UserCreate, UserResponse, SuccessResponse
from auth import authenticate_user, create_access_token, create_refresh_token, get_current_user, require_admin, create_user
from database import execute_query
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/login", response_model=TokenResponse)
async def login(request: LoginRequest):
    """
    User login endpoint
    
    Args:
        request: Login credentials
    
    Returns:
        Access and refresh tokens
    """
    user = authenticate_user(request.username, request.password)
    
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect username or password",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    # Create tokens
    token_data = {
        "user_id": user['user_id'],
        "username": user['username'],
        "role": user['role_name']
    }
    
    access_token = create_access_token(token_data)
    refresh_token = create_refresh_token(token_data)
    
    return TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        user_id=user['user_id'],
        username=user['username'],
        role=user['role_name']
    )


@router.get("/me", response_model=UserResponse)
async def get_current_user_info(current_user: dict = Depends(get_current_user)):
    """
    Get current user information

    Args:
        current_user: Current authenticated user

    Returns:
        User information
    """
    # Use stored procedure to get current user
    query = "EXEC sp_Auth_GetCurrentUser @user_id = ?"
    results = execute_query(query, (current_user['user_id'],))
    if not results:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="User not found"
        )
    return results[0]


@router.post("/register", response_model=SuccessResponse)
async def register_user(user_data: UserCreate, current_user: dict = Depends(require_admin)):
    """
    Register a new user (Admin only)
    
    Args:
        user_data: User registration data
        current_user: Current authenticated admin user
    
    Returns:
        Success response with new user ID
    """
    try:
        user_id = create_user(
            username=user_data.username,
            password=user_data.password,
            full_name=user_data.full_name,
            role_id=user_data.role_id,
            email=user_data.email,
            phone=user_data.phone
        )
        
        return SuccessResponse(
            message="User created successfully",
            data={"user_id": user_id}
        )
    except Exception as e:
        logger.error(f"Error creating user: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create user: {str(e)}"
        )


@router.get("/users", response_model=list)
async def get_all_users(current_user: dict = Depends(require_admin)):
    """
    Get all users (Admin only)
    
    Args:
        current_user: Current authenticated admin user
    
    Returns:
        List of all users
    """
    query = """
        SELECT u.user_id, u.username, u.full_name, u.full_name_urdu, u.email, u.phone,
               u.role_id, r.role_name, u.is_active, u.last_login, u.created_at
        FROM users u
        INNER JOIN roles r ON u.role_id = r.role_id
        ORDER BY u.created_at DESC
    """
    
    users = execute_query(query)
    return users


@router.get("/roles", response_model=list)
async def get_all_roles(current_user: dict = Depends(get_current_user)):
    """
    Get all roles

    Args:
        current_user: Current authenticated user

    Returns:
        List of all roles
    """
    query = "EXEC sp_Auth_GetAllRoles"
    roles = execute_query(query)
    return roles


@router.put("/users/{user_id}/toggle-status", response_model=SuccessResponse)
async def toggle_user_status(user_id: int, current_user: dict = Depends(require_admin)):
    """
    Toggle user active status (Admin only)
    
    Args:
        user_id: User ID to toggle
        current_user: Current authenticated admin user
    
    Returns:
        Success response
    """
    from database import execute_non_query
    
    query = """
        UPDATE users 
        SET is_active = CASE WHEN is_active = 1 THEN 0 ELSE 1 END,
            updated_at = GETDATE()
        WHERE user_id = ?
    """
    
    execute_non_query(query, (user_id,))
    
    return SuccessResponse(message="User status updated successfully")


@router.post("/change-password", response_model=SuccessResponse)
async def change_password(old_password: str, new_password: str, 
                         current_user: dict = Depends(get_current_user)):
    """
    Change user password
    
    Args:
        old_password: Current password
        new_password: New password
        current_user: Current authenticated user
    
    Returns:
        Success response
    """
    from auth import change_password as change_pwd
    
    success = change_pwd(current_user['user_id'], old_password, new_password)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid current password"
        )
    
    return SuccessResponse(message="Password changed successfully")

