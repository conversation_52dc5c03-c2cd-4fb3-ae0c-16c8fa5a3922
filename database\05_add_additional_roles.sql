-- Add additional roles for School Management System
USE SchoolManagementDB;
GO

-- Add new roles
INSERT INTO roles (role_name, role_name_urdu, description, is_active)
VALUES 
    ('Attendance Controller', N'حاضری کنٹرولر', 'Manages student attendance only', 1),
    ('Exam Controller', N'امتحان کنٹرولر', 'Manages exams and results only', 1),
    ('Guest', N'مہمان', 'Read-only access to limited data', 1),
    ('Fee Accountant', N'فیس اکاؤنٹنٹ', 'Manages fee collection and accounts', 1),
    ('Owner', N'مالک', 'Full system access with administrative rights', 1);
GO

-- Display all roles
SELECT role_id, role_name, role_name_urdu, description, is_active
FROM roles
ORDER BY role_id;
GO

