-- =============================================
-- Migration: Add Exam-Subject Mapping and Class-Subject Mapping
-- Description: Create tables to map exams to subjects and classes to subjects
-- =============================================

USE SchoolManagementDB;
GO

-- Create exam_subjects table (which subjects are included in which exam)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[exam_subjects]') AND type in (N'U'))
BEGIN
    CREATE TABLE exam_subjects (
        exam_subject_id INT IDENTITY(1,1) PRIMARY KEY,
        exam_id INT NOT NULL,
        subject_id INT NOT NULL,
        class_id INT NULL,  -- NULL means all classes, otherwise specific class
        max_marks DECIMAL(5,2) NOT NULL DEFAULT 100,
        passing_marks DECIMAL(5,2) NOT NULL DEFAULT 40,
        theory_max DECIMAL(5,2) NULL,
        practical_max DECIMAL(5,2) NULL,
        oral_max DECIMAL(5,2) NULL,
        assignment_max DECIMAL(5,2) NULL,
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (exam_id) REFERENCES exams(exam_id),
        FOREIGN KEY (subject_id) REFERENCES subjects(subject_id),
        FOREIGN KEY (class_id) REFERENCES classes(class_id)
    );
    PRINT 'Table exam_subjects created successfully';
END
ELSE
BEGIN
    PRINT 'Table exam_subjects already exists';
END
GO

-- Create class_subjects table (which subjects are taught in which class)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[class_subjects]') AND type in (N'U'))
BEGIN
    CREATE TABLE class_subjects (
        class_subject_id INT IDENTITY(1,1) PRIMARY KEY,
        class_id INT NOT NULL,
        subject_id INT NOT NULL,
        teacher_id INT NULL,  -- Staff member teaching this subject
        is_compulsory BIT DEFAULT 1,  -- Compulsory or optional subject
        is_active BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (class_id) REFERENCES classes(class_id),
        FOREIGN KEY (subject_id) REFERENCES subjects(subject_id),
        FOREIGN KEY (teacher_id) REFERENCES staff(staff_id)
    );
    PRINT 'Table class_subjects created successfully';
END
ELSE
BEGIN
    PRINT 'Table class_subjects already exists';
END
GO

-- Create student_exam_summary table (overall exam performance for each student)
IF NOT EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[student_exam_summary]') AND type in (N'U'))
BEGIN
    CREATE TABLE student_exam_summary (
        summary_id INT IDENTITY(1,1) PRIMARY KEY,
        exam_id INT NOT NULL,
        student_id INT NOT NULL,
        class_id INT NOT NULL,
        total_obtained_marks DECIMAL(7,2) DEFAULT 0,
        total_max_marks DECIMAL(7,2) DEFAULT 0,
        percentage DECIMAL(5,2) DEFAULT 0,
        grade NVARCHAR(5) NULL,
        class_rank INT NULL,
        section_rank INT NULL,
        overall_rank INT NULL,
        result_status NVARCHAR(20) DEFAULT 'Pending',  -- Pending, Pass, Fail, Absent
        remarks NVARCHAR(500) NULL,
        is_published BIT DEFAULT 0,  -- Whether result is published to student/parent
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        FOREIGN KEY (exam_id) REFERENCES exams(exam_id),
        FOREIGN KEY (student_id) REFERENCES students(student_id),
        FOREIGN KEY (class_id) REFERENCES classes(class_id)
    );
    PRINT 'Table student_exam_summary created successfully';
END
ELSE
BEGIN
    PRINT 'Table student_exam_summary already exists';
END
GO

-- Add index for faster queries
IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_exam_subjects_exam_id' AND object_id = OBJECT_ID('exam_subjects'))
BEGIN
    CREATE INDEX IX_exam_subjects_exam_id ON exam_subjects(exam_id);
    PRINT 'Index IX_exam_subjects_exam_id created';
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_class_subjects_class_id' AND object_id = OBJECT_ID('class_subjects'))
BEGIN
    CREATE INDEX IX_class_subjects_class_id ON class_subjects(class_id);
    PRINT 'Index IX_class_subjects_class_id created';
END
GO

IF NOT EXISTS (SELECT * FROM sys.indexes WHERE name = 'IX_student_exam_summary_exam_student' AND object_id = OBJECT_ID('student_exam_summary'))
BEGIN
    CREATE INDEX IX_student_exam_summary_exam_student ON student_exam_summary(exam_id, student_id);
    PRINT 'Index IX_student_exam_summary_exam_student created';
END
GO

PRINT 'Migration completed successfully!';
GO

