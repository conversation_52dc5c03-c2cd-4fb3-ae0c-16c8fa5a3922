"""
Exam Marks Routes
Handles marks recording and retrieval
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional
from models import ExamResultCreate, ExamMarksEntry, BulkMarksEntry, SuccessResponse
from auth import require_teacher, get_current_user
from services.exam_service import ExamService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/results", response_model=SuccessResponse)
async def record_marks(
    result: ExamResultCreate,
    current_user: dict = Depends(require_teacher)
):
    """
    Record exam marks for a student (Teacher/Admin only)
    
    Args:
        result: Exam result data
        current_user: Current authenticated user
    
    Returns:
        Success response
    """
    try:
        result_data = ExamService.record_marks(result, current_user['user_id'])
        
        if result_data and result_data.get('Result') == 'Success':
            return SuccessResponse(
                message=result_data.get('Message', 'Marks recorded successfully')
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result_data.get('Message', 'Failed to record marks')
            )
    except Exception as e:
        logger.error(f"Error recording marks: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during marks recording"
        )


@router.post("/marks/enter", response_model=SuccessResponse)
async def enter_exam_marks(
    marks_entry: ExamMarksEntry,
    current_user: dict = Depends(get_current_user)
):
    """
    Enter exam marks for a student
    
    Args:
        marks_entry: Marks entry data
        current_user: Current authenticated user
    
    Returns:
        Success response
    """
    try:
        result = ExamService.enter_exam_marks(marks_entry, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return SuccessResponse(
                message=result.get('Message', 'Marks entered successfully')
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to enter marks')
            )
    except Exception as e:
        logger.error(f"Error entering marks: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during marks entry"
        )


@router.post("/marks/bulk", response_model=SuccessResponse)
async def bulk_marks_entry(
    bulk_data: BulkMarksEntry,
    current_user: dict = Depends(get_current_user)
):
    """
    Bulk entry of exam marks
    
    Args:
        bulk_data: Bulk marks data
        current_user: Current authenticated user
    
    Returns:
        Success response with processing summary
    """
    try:
        result = ExamService.bulk_marks_entry(bulk_data, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return SuccessResponse(
                message=result.get('Message', 'Bulk marks entered successfully'),
                data={
                    'processed_count': result.get('ProcessedCount', 0),
                    'failed_count': result.get('FailedCount', 0)
                }
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to process bulk marks')
            )
    except Exception as e:
        logger.error(f"Error in bulk marks entry: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during bulk marks entry"
        )
