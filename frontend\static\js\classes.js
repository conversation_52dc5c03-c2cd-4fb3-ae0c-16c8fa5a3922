// Classes module
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    SMS.loadClassesPage = async function() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-school"></i> Class Management</h2>
                <button class="btn btn-primary" onclick="showAddClassModal()"><i class="fas fa-plus"></i> Add New Class</button>
            </div>
            <div id="classesContainer"><div class="text-center"><div class="spinner-border" role="status"></div></div></div>
        `;
        await SMS.loadClasses();
    };

    SMS.loadClasses = async function() {
        SMS.showLoadingInline('classesContainer', 'Loading classes...');
        try {
            const results = await fetch(SMS.apiUrl('/classes'), {
                headers: { 'Authorization': `Bearer ${SMS.authToken}` }
            });
            if (!results.ok) throw new Error('Failed to load');
            const classes = await results.json();
            SMS.displayClasses(classes);
        } catch (error) {
            console.error('Error loading classes:', error);
            document.getElementById('classesContainer').innerHTML = '<div class="alert alert-danger">Failed to load classes</div>';
        }
    };

    SMS.displayClasses = function(classes) {
        const container = document.getElementById('classesContainer');
        if (!classes || classes.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">No classes found</p>';
            return;
        }
        container.innerHTML = `<div class="row">${classes.map(cls => `
            <div class="col-md-6 col-lg-4 mb-4">
                <div class="card shadow-sm h-100">
                    <div class="card-header bg-primary text-white">
                        <div class="d-flex justify-content-between align-items-center">
                            <h5 class="mb-0"><i class="fas fa-graduation-cap"></i> ${cls.class_name} - ${cls.section}</h5>
                        </div>
                    </div>
                    <div class="card-body">
                        <p><small class="text-muted">Teacher: ${cls.teacher_name || 'N/A'}</small></p>
                        <p><small class="text-muted">Students: ${cls.student_count || 0}</small></p>
                    </div>
                    <div class="card-footer bg-light">
                        <div class="d-flex gap-2">
                            <button class="btn btn-sm btn-info" onclick="viewClassDetails(${cls.class_id})">View</button>
                            <button class="btn btn-sm btn-warning" onclick="editClass(${cls.class_id})">Edit</button>
                            <button class="btn btn-sm btn-danger" onclick="deleteClass(${cls.class_id})">Delete</button>
                        </div>
                    </div>
                </div>
            </div>
        `).join('')}</div>`;
    };

})(window);
