"""
Fee Account Routes
Handles student fee accounts, transactions, and ledgers
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional
from auth import get_current_user
from services.fee_service import FeeService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/account/{student_id}", response_model=dict)
async def get_fee_account(
    student_id: int,
    academic_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get fee account details for a student
    
    Args:
        student_id: Student ID
        academic_year: Academic year
        current_user: Current authenticated user
    
    Returns:
        Fee account details
    """
    try:
        result = FeeService.get_fee_account(student_id, academic_year)
        
        if result:
            return result
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Fee account not found for the specified student"
            )
    except Exception as e:
        logger.error(f"Error fetching fee account: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching fee account"
        )


@router.get("/transactions/{student_id}", response_model=list)
async def get_fee_transactions(
    student_id: int,
    academic_year: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get fee transactions for a student
    
    Args:
        student_id: Student ID
        academic_year: Optional academic year filter
        current_user: Current authenticated user
    
    Returns:
        List of fee transactions
    """
    try:
        result = FeeService.get_fee_transactions(student_id, academic_year)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching fee transactions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching fee transactions"
        )


@router.get("/ledger/{student_id}", response_model=list)
async def get_student_ledger(
    student_id: int,
    academic_year: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get detailed ledger for a student
    
    Args:
        student_id: Student ID
        academic_year: Optional academic year filter
        current_user: Current authenticated user
    
    Returns:
        Student ledger entries
    """
    try:
        result = FeeService.get_student_ledger(student_id, academic_year)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching student ledger: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching student ledger"
        )


@router.get("/outstanding/{student_id}", response_model=dict)
async def get_student_outstanding(
    student_id: int,
    academic_year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get outstanding fee amount for a student
    
    Args:
        student_id: Student ID
        academic_year: Academic year
        current_user: Current authenticated user
    
    Returns:
        Outstanding fee details
    """
    try:
        result = FeeService.get_student_outstanding(student_id, academic_year)
        
        if result:
            return result
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Outstanding fee information not found for the specified student"
            )
    except Exception as e:
        logger.error(f"Error fetching outstanding fees: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching outstanding fees"
        )
