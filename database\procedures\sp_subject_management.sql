-- =============================================
-- Subject Management Stored Procedures
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Procedure: sp_Subject_Create
-- Description: Create a new subject
-- =============================================
IF OBJECT_ID('sp_Subject_Create', 'P') IS NOT NULL
    DROP PROCEDURE sp_Subject_Create;
GO

CREATE PROCEDURE sp_Subject_Create
    @subject_name NVARCHAR(100),
    @subject_name_urdu NVARCHAR(100) = NULL,
    @subject_code NVARCHAR(20),
    @total_marks INT = 100,
    @is_active BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if subject code already exists
        IF EXISTS (SELECT 1 FROM subjects WHERE subject_code = @subject_code)
        BEGIN
            THROW 50001, 'Subject code already exists', 1;
        END
        
        INSERT INTO subjects (
            subject_name,
            subject_name_urdu,
            subject_code,
            total_marks,
            is_active
        )
        VALUES (
            @subject_name,
            @subject_name_urdu,
            @subject_code,
            @total_marks,
            @is_active
        );
        
        SELECT SCOPE_IDENTITY() AS subject_id;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- Procedure: sp_Subject_GetAll
-- Description: Get all subjects with optional filter
-- =============================================
IF OBJECT_ID('sp_Subject_GetAll', 'P') IS NOT NULL
    DROP PROCEDURE sp_Subject_GetAll;
GO

CREATE PROCEDURE sp_Subject_GetAll
    @is_active BIT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        subject_id,
        subject_name,
        subject_name_urdu,
        subject_code,
        total_marks,
        is_active,
        created_at,
        updated_at
    FROM subjects
    WHERE (@is_active IS NULL OR is_active = @is_active)
    ORDER BY subject_name;
END;
GO

-- =============================================
-- Procedure: sp_Subject_GetById
-- Description: Get subject by ID
-- =============================================
IF OBJECT_ID('sp_Subject_GetById', 'P') IS NOT NULL
    DROP PROCEDURE sp_Subject_GetById;
GO

CREATE PROCEDURE sp_Subject_GetById
    @subject_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        subject_id,
        subject_name,
        subject_name_urdu,
        subject_code,
        total_marks,
        is_active,
        created_at,
        updated_at
    FROM subjects
    WHERE subject_id = @subject_id;
END;
GO

-- =============================================
-- Procedure: sp_Subject_Update
-- Description: Update subject
-- =============================================
IF OBJECT_ID('sp_Subject_Update', 'P') IS NOT NULL
    DROP PROCEDURE sp_Subject_Update;
GO

CREATE PROCEDURE sp_Subject_Update
    @subject_id INT,
    @subject_name NVARCHAR(100),
    @subject_name_urdu NVARCHAR(100) = NULL,
    @subject_code NVARCHAR(20),
    @total_marks INT,
    @is_active BIT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if subject code exists for another subject
        IF EXISTS (
            SELECT 1 FROM subjects 
            WHERE subject_code = @subject_code 
                AND subject_id != @subject_id
        )
        BEGIN
            THROW 50001, 'Subject code already exists for another subject', 1;
        END
        
        UPDATE subjects
        SET 
            subject_name = @subject_name,
            subject_name_urdu = @subject_name_urdu,
            subject_code = @subject_code,
            total_marks = @total_marks,
            is_active = @is_active,
            updated_at = GETDATE()
        WHERE subject_id = @subject_id;
        
        SELECT @@ROWCOUNT AS rows_affected;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- Procedure: sp_Subject_Delete
-- Description: Delete subject
-- =============================================
IF OBJECT_ID('sp_Subject_Delete', 'P') IS NOT NULL
    DROP PROCEDURE sp_Subject_Delete;
GO

CREATE PROCEDURE sp_Subject_Delete
    @subject_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if subject is assigned to any class
        IF EXISTS (SELECT 1 FROM class_subjects WHERE subject_id = @subject_id)
        BEGIN
            THROW 50002, 'Cannot delete subject that is assigned to classes', 1;
        END
        
        DELETE FROM subjects
        WHERE subject_id = @subject_id;
        
        SELECT @@ROWCOUNT AS rows_affected;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- Procedure: sp_Subject_AssignToClass
-- Description: Assign subject to a class
-- =============================================
IF OBJECT_ID('sp_Subject_AssignToClass', 'P') IS NOT NULL
    DROP PROCEDURE sp_Subject_AssignToClass;
GO

CREATE PROCEDURE sp_Subject_AssignToClass
    @class_id INT,
    @subject_id INT,
    @teacher_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if already assigned
        IF EXISTS (
            SELECT 1 FROM class_subjects 
            WHERE class_id = @class_id 
                AND subject_id = @subject_id
        )
        BEGIN
            THROW 50003, 'Subject already assigned to this class', 1;
        END
        
        INSERT INTO class_subjects (
            class_id,
            subject_id,
            teacher_id,
            is_active
        )
        VALUES (
            @class_id,
            @subject_id,
            @teacher_id,
            1
        );
        
        SELECT SCOPE_IDENTITY() AS class_subject_id;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- Procedure: sp_Subject_GetByClass
-- Description: Get all subjects for a class
-- =============================================
IF OBJECT_ID('sp_Subject_GetByClass', 'P') IS NOT NULL
    DROP PROCEDURE sp_Subject_GetByClass;
GO

CREATE PROCEDURE sp_Subject_GetByClass
    @class_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        s.subject_id,
        s.subject_name,
        s.subject_name_urdu,
        s.subject_code,
        s.total_marks,
        s.is_active,
        cs.teacher_id,
        u.full_name AS teacher_name
    FROM class_subjects cs
    INNER JOIN subjects s ON cs.subject_id = s.subject_id
    LEFT JOIN users u ON cs.teacher_id = u.user_id
    WHERE cs.class_id = @class_id 
        AND cs.is_active = 1
    ORDER BY s.subject_name;
END;
GO

-- =============================================
-- Procedure: sp_Subject_UnassignFromClass
-- Description: Unassign subject from a class
-- =============================================
IF OBJECT_ID('sp_Subject_UnassignFromClass', 'P') IS NOT NULL
    DROP PROCEDURE sp_Subject_UnassignFromClass;
GO

CREATE PROCEDURE sp_Subject_UnassignFromClass
    @class_id INT,
    @subject_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DELETE FROM class_subjects
        WHERE class_id = @class_id 
            AND subject_id = @subject_id;
        
        SELECT @@ROWCOUNT AS rows_affected;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

PRINT 'Subject management stored procedures created successfully!';

