"""
Teacher Routes - Main Router
Combines all teacher-related route modules
"""
from fastapi import APIRouter
from .teacher_schedule_routes import router as schedule_router
from .teacher_attendance_routes import router as attendance_router
from .teacher_substitution_routes import router as substitution_router

# Create main router
router = APIRouter()

# Include all sub-routers
router.include_router(schedule_router, tags=["Teacher Schedules"])
router.include_router(attendance_router, tags=["Teacher Attendance"])
router.include_router(substitution_router, tags=["Teacher Substitutions"])

# Note: All teacher functionality has been modularized:
# - teacher_schedule_routes.py: Teacher schedule viewing and period management
# - teacher_attendance_routes.py: Attendance marking, tracking, and reporting
# - teacher_substitution_routes.py: Substitute teacher management
