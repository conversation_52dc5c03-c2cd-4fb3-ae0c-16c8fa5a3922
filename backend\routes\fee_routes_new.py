"""
Fee Routes - Main Router
Combines all fee-related route modules
"""
from fastapi import APIRouter
from .fee_collection_routes import router as collection_router
from .fee_calculation_routes import router as calculation_router
from .fee_structure_routes import router as structure_router
from .fee_account_routes import router as account_router

# Create main router
router = APIRouter()

# Include all sub-routers
router.include_router(collection_router, tags=["Fee Collection"])
router.include_router(calculation_router, tags=["Fee Calculations"])
router.include_router(structure_router, tags=["Fee Structures"])
router.include_router(account_router, tags=["Fee Accounts"])

# TODO: Add remaining routes from original fee_routes.py:
# - Fee Reports & Analytics routes
# - Class Fee Management routes  
# - Student Discount routes
# - Utility routes (generate-monthly-fees, students-for-collection)
#
# These will be added in separate route files to keep modules focused and manageable.
