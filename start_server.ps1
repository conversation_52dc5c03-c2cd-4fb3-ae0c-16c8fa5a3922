#!/usr/bin/env pwsh
# School Management System - Server Start Script
# This script starts the SMS backend server

Write-Host "=== School Management System - Server Start ===" -ForegroundColor Green
Write-Host "Starting SMS Backend Server..." -ForegroundColor Yellow

# Check if Python is installed
try {
    $pythonVersion = python --version 2>&1
    Write-Host "Python found: $pythonVersion" -ForegroundColor Green
} catch {
    Write-Host "ERROR: Python is not installed or not in PATH" -ForegroundColor Red
    Write-Host "Please install Python 3.8+ and try again" -ForegroundColor Red
    exit 1
}

# Check if virtual environment exists
if (-not (Test-Path "venv")) {
    Write-Host "Virtual environment not found. Creating..." -ForegroundColor Yellow
    python -m venv venv
    Write-Host "Virtual environment created." -ForegroundColor Green
}

# Activate virtual environment
Write-Host "Activating virtual environment..." -ForegroundColor Yellow
if ($IsWindows -or $env:OS -eq "Windows_NT") {
    & "venv\Scripts\Activate.ps1"
} else {
    & "venv/bin/activate"
}

# Install requirements if needed
if (Test-Path "requirements.txt") {
    Write-Host "Installing/updating requirements..." -ForegroundColor Yellow
    pip install -r requirements.txt --quiet
}

# Kill any existing server processes
Write-Host "Stopping any existing server processes..." -ForegroundColor Yellow
Get-Process -Name "python" -ErrorAction SilentlyContinue | Where-Object { $_.CommandLine -like "*main.py*" } | Stop-Process -Force -ErrorAction SilentlyContinue

# Change to backend directory
Set-Location backend

# Start the server
Write-Host "Starting SMS Backend Server on http://127.0.0.1:8000" -ForegroundColor Green
Write-Host "Press Ctrl+C to stop the server" -ForegroundColor Yellow
Write-Host "========================================" -ForegroundColor Green

try {
    python main.py
} catch {
    Write-Host "ERROR: Failed to start server" -ForegroundColor Red
    Write-Host "Error: $_" -ForegroundColor Red
    exit 1
}
