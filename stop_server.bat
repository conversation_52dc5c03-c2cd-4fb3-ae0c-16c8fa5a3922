@echo off
REM School Management System - Server Stop Script (Windows Batch)
REM This script stops all SMS backend server processes

echo === School Management System - Server Stop ===
echo Stopping SMS Backend Server...

REM Kill all Python processes (be careful - this kills ALL python processes)
echo Stopping Python processes...
taskkill /f /im python.exe >nul 2>&1

REM Kill processes using port 8000
echo Stopping processes using port 8000...
for /f "tokens=5" %%a in ('netstat -ano ^| findstr :8000') do (
    taskkill /f /pid %%a >nul 2>&1
)

echo SMS Backend Server stopped
echo ========================================
pause
