#!/usr/bin/env python3
"""
SMS File Mapping Generator
Creates an Excel sheet mapping frontend, backend, and database files for the School Management System
"""

import pandas as pd
from openpyxl import Workbook
from openpyxl.styles import Font, PatternFill, Alignment
from openpyxl.utils.dataframe import dataframe_to_rows
import os

def create_file_mapping():
    """Create comprehensive file mapping for SMS system"""
    
    # Define the module structure and file relationships
    modules_data = [
        {
            'Module': 'Authentication/Login',
            'Frontend_JS': 'auth.js',
            'Backend_Route': 'auth_routes.py',
            'Stored_Procedures': 'sp_auth_management.sql',
            'HTML_Template': 'index.html (login section)',
            'CSS_Classes': '.login-form, .auth-container',
            'API_Endpoints': '/auth/login, /auth/logout, /auth/verify',
            'Description': 'User authentication, login/logout functionality'
        },
        {
            'Module': 'Dashboard',
            'Frontend_JS': 'dashboard.js',
            'Backend_Route': 'dashboard_routes.py',
            'Stored_Procedures': 'sp_dashboard_stats.sql, sp_GetDashboardStats',
            'HTML_Template': 'index.html (dashboard section)',
            'CSS_Classes': '.dashboard-card, .stats-widget',
            'API_Endpoints': '/dashboard/stats',
            'Description': 'Main dashboard with statistics and overview'
        },
        {
            'Module': 'Students Management',
            'Frontend_JS': 'students.module.js, student-modals.js',
            'Backend_Route': 'student_routes.py',
            'Stored_Procedures': 'sp_student_management.sql, sp_CreateStudent, sp_GetStudents',
            'HTML_Template': 'index.html (students section)',
            'CSS_Classes': '.student-form, .student-list',
            'API_Endpoints': '/students, /students/{id}, /students/profile/{id}',
            'Description': 'Student CRUD operations, profiles, enrollment'
        },
        {
            'Module': 'Users Management',
            'Frontend_JS': 'users.js, users.module.js',
            'Backend_Route': 'user_routes.py',
            'Stored_Procedures': 'sp_user_management.sql, sp_users.sql',
            'HTML_Template': 'index.html (users section)',
            'CSS_Classes': '.user-form, .user-list',
            'API_Endpoints': '/users, /users/{id}, /users/profile',
            'Description': 'User account management, roles, permissions'
        },
        {
            'Module': 'Teachers Management',
            'Frontend_JS': 'teacher_pages.js',
            'Backend_Route': 'teacher_routes.py, teacher_routes_new.py',
            'Stored_Procedures': 'sp_teacher_management.sql',
            'HTML_Template': 'index.html (teachers section)',
            'CSS_Classes': '.teacher-form, .teacher-profile',
            'API_Endpoints': '/teachers, /teachers/{id}, /teachers/profile/{id}',
            'Description': 'Teacher profiles, qualifications, assignments'
        },
        {
            'Module': 'Staff Management',
            'Frontend_JS': 'staff.js',
            'Backend_Route': 'staff_routes.py',
            'Stored_Procedures': 'sp_staff_management.sql',
            'HTML_Template': 'index.html (staff section)',
            'CSS_Classes': '.staff-form, .staff-list',
            'API_Endpoints': '/staff, /staff/{id}',
            'Description': 'Non-teaching staff management'
        },
        {
            'Module': 'Classes Management',
            'Frontend_JS': 'classes.js',
            'Backend_Route': 'class_routes.py',
            'Stored_Procedures': 'sp_class_management.sql',
            'HTML_Template': 'index.html (classes section)',
            'CSS_Classes': '.class-form, .class-list',
            'API_Endpoints': '/classes, /classes/{id}',
            'Description': 'Class creation, sections, capacity management'
        },
        {
            'Module': 'Subjects Management',
            'Frontend_JS': 'subjects.js',
            'Backend_Route': 'subject_routes.py',
            'Stored_Procedures': 'sp_subject_management.sql',
            'HTML_Template': 'index.html (subjects section)',
            'CSS_Classes': '.subject-form, .subject-list',
            'API_Endpoints': '/subjects, /subjects/{id}',
            'Description': 'Subject definitions, curriculum management'
        },
        {
            'Module': 'Attendance Management',
            'Frontend_JS': 'attendance.module.js, attendance-functions.js, attendance-functions-2.js, attendance-functions-3.js, attendance-functions-4.js',
            'Backend_Route': 'attendance_routes.py',
            'Stored_Procedures': 'sp_attendance_management.sql',
            'HTML_Template': 'index.html (attendance section)',
            'CSS_Classes': '.attendance-grid, .attendance-form',
            'API_Endpoints': '/attendance/mark, /attendance/class/{id}, /attendance/student/{id}',
            'Description': 'Daily attendance marking, reports, statistics'
        },
        {
            'Module': 'Teacher Attendance',
            'Frontend_JS': 'teacher_pages.js',
            'Backend_Route': 'teacher_attendance_routes.py',
            'Stored_Procedures': 'sp_teacher_attendance_management.sql',
            'HTML_Template': 'index.html (teacher attendance section)',
            'CSS_Classes': '.teacher-attendance-form',
            'API_Endpoints': '/teachers/attendance/mark, /teachers/attendance/{id}',
            'Description': 'Teacher attendance tracking and management'
        },
        {
            'Module': 'Fee Management',
            'Frontend_JS': 'fees.module.js',
            'Backend_Route': 'fee_routes.py, fee_routes_new.py, fee_collection_routes.py, fee_calculation_routes.py, fee_structure_routes.py, fee_account_routes.py',
            'Stored_Procedures': 'sp_fee_management.sql, sp_fee_additional.sql, sp_generate_monthly_fees.sql',
            'HTML_Template': 'index.html (fees section)',
            'CSS_Classes': '.fee-form, .fee-collection',
            'API_Endpoints': '/fees/collect, /fees/structure, /fees/account/{id}',
            'Description': 'Fee collection, structure, accounts, defaulters'
        },
        {
            'Module': 'Exam Management',
            'Frontend_JS': 'exams.js, exam-crud-functions.js, exam-page-functions.js',
            'Backend_Route': 'exam_routes.py, exam_routes_new.py, exam_management_routes.py, exam_marks_routes.py',
            'Stored_Procedures': 'sp_exam_management.sql, sp_exam_additional.sql',
            'HTML_Template': 'index.html (exams section)',
            'CSS_Classes': '.exam-form, .marks-entry',
            'API_Endpoints': '/exams, /exams/{id}/marks, /exams/results',
            'Description': 'Exam creation, marks entry, result generation'
        },
        {
            'Module': 'Timetable Management',
            'Frontend_JS': 'timetable.js, timetable_grid.js',
            'Backend_Route': 'timetable_routes.py, timetable_routes_new.py, timetable_management_routes.py, timetable_config_routes.py, timetable_views_routes.py',
            'Stored_Procedures': 'sp_timetable.sql, sp_timetable_management.sql',
            'HTML_Template': 'index.html (timetable section)',
            'CSS_Classes': '.timetable-grid, .schedule-form',
            'API_Endpoints': '/timetable, /timetable/class/{id}, /timetable/teacher/{id}',
            'Description': 'Class timetables, teacher schedules, period management'
        },
        {
            'Module': 'Teacher Schedule',
            'Frontend_JS': 'teacher_pages.js',
            'Backend_Route': 'teacher_schedule_routes.py',
            'Stored_Procedures': 'sp_teacher_schedule_management.sql',
            'HTML_Template': 'index.html (teacher schedule section)',
            'CSS_Classes': '.teacher-schedule',
            'API_Endpoints': '/teachers/schedule, /teachers/{id}/schedule',
            'Description': 'Individual teacher schedule management'
        },
        {
            'Module': 'Teacher Substitution',
            'Frontend_JS': 'teacher_pages.js',
            'Backend_Route': 'teacher_substitution_routes.py',
            'Stored_Procedures': 'sp_teacher_substitution_management.sql',
            'HTML_Template': 'index.html (substitution section)',
            'CSS_Classes': '.substitution-form',
            'API_Endpoints': '/teachers/substitution, /teachers/substitute/{id}',
            'Description': 'Teacher substitution and replacement management'
        },
        {
            'Module': 'Holidays Management',
            'Frontend_JS': 'holidays.js',
            'Backend_Route': 'holiday_routes.py',
            'Stored_Procedures': 'sp_holiday_management.sql',
            'HTML_Template': 'index.html (holidays section)',
            'CSS_Classes': '.holiday-form, .calendar-view',
            'API_Endpoints': '/holidays, /holidays/{id}',
            'Description': 'School holidays, calendar management'
        },
        {
            'Module': 'Reports',
            'Frontend_JS': 'reports.js',
            'Backend_Route': 'report_routes.py',
            'Stored_Procedures': 'Multiple SPs for different reports',
            'HTML_Template': 'index.html (reports section)',
            'CSS_Classes': '.report-form, .report-viewer',
            'API_Endpoints': '/reports/attendance, /reports/fees, /reports/academic',
            'Description': 'Various reports generation and viewing'
        },
        {
            'Module': 'Settings',
            'Frontend_JS': 'settings.js',
            'Backend_Route': 'settings_routes.py',
            'Stored_Procedures': 'sp_school_settings.sql',
            'HTML_Template': 'index.html (settings section)',
            'CSS_Classes': '.settings-form, .config-panel',
            'API_Endpoints': '/settings, /settings/school, /settings/system',
            'Description': 'System configuration, school settings'
        },
        {
            'Module': 'Core/Common',
            'Frontend_JS': 'core.js, core.module.js, api.js, router.js, module-loader.js',
            'Backend_Route': 'main.py, config.py, database.py, auth.py, models.py',
            'Stored_Procedures': 'Common utility procedures',
            'HTML_Template': 'index.html (base structure)',
            'CSS_Classes': '.common-styles, .layout-classes',
            'API_Endpoints': 'Base API configuration',
            'Description': 'Core functionality, routing, authentication, database connection'
        }
    ]

    return modules_data

def create_detailed_file_mapping():
    """Create detailed file-to-file mapping"""
    
    detailed_mapping = [
        # Dashboard relationships
        {'Source_File': 'frontend/static/js/dashboard.js', 'Target_File': 'backend/routes/dashboard_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadDashboardStats() -> /dashboard/stats'},
        {'Source_File': 'backend/routes/dashboard_routes.py', 'Target_File': 'database/procedures/sp_dashboard_stats.sql', 'Relationship': 'SP Call', 'Function/Endpoint': 'get_dashboard_stats() -> sp_GetDashboardStats'},
        
        # Students relationships
        {'Source_File': 'frontend/static/js/students.module.js', 'Target_File': 'backend/routes/student_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadStudents() -> /students'},
        {'Source_File': 'backend/routes/student_routes.py', 'Target_File': 'database/procedures/sp_student_management.sql', 'Relationship': 'SP Call', 'Function/Endpoint': 'get_students() -> sp_GetStudents'},
        {'Source_File': 'frontend/static/js/student-modals.js', 'Target_File': 'backend/routes/student_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'createStudent() -> POST /students'},
        
        # Attendance relationships
        {'Source_File': 'frontend/static/js/attendance.module.js', 'Target_File': 'backend/routes/attendance_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'markAttendance() -> /attendance/mark'},
        {'Source_File': 'backend/routes/attendance_routes.py', 'Target_File': 'database/procedures/sp_attendance_management.sql', 'Relationship': 'SP Call', 'Function/Endpoint': 'mark_attendance() -> sp_MarkAttendance'},
        
        # Cross-module dependencies
        {'Source_File': 'frontend/static/js/dashboard.js', 'Target_File': 'backend/routes/student_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadTotalStudents() -> /students?status=Active'},
        {'Source_File': 'frontend/static/js/dashboard.js', 'Target_File': 'backend/routes/user_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadTotalTeachers() -> /users'},
        {'Source_File': 'frontend/static/js/dashboard.js', 'Target_File': 'backend/routes/attendance_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadTodayAttendance() -> /attendance/daily-summary'},
        {'Source_File': 'frontend/static/js/dashboard.js', 'Target_File': 'backend/routes/fee_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadFeeDefaulters() -> /fees/defaulters'},

        # Users/Teachers relationships
        {'Source_File': 'frontend/static/js/users.module.js', 'Target_File': 'backend/routes/user_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadUsers() -> /users'},
        {'Source_File': 'backend/routes/user_routes.py', 'Target_File': 'database/procedures/sp_user_management.sql', 'Relationship': 'SP Call', 'Function/Endpoint': 'get_users() -> sp_GetUsers'},
        {'Source_File': 'frontend/static/js/teacher_pages.js', 'Target_File': 'backend/routes/teacher_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadTeachers() -> /teachers'},

        # Fee Management relationships
        {'Source_File': 'frontend/static/js/fees.module.js', 'Target_File': 'backend/routes/fee_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'collectFee() -> /fees/collect'},
        {'Source_File': 'backend/routes/fee_routes.py', 'Target_File': 'database/procedures/sp_fee_management.sql', 'Relationship': 'SP Call', 'Function/Endpoint': 'collect_fee() -> sp_CollectFee'},
        {'Source_File': 'backend/routes/fee_collection_routes.py', 'Target_File': 'database/procedures/sp_fee_management.sql', 'Relationship': 'SP Call', 'Function/Endpoint': 'Various fee collection SPs'},

        # Exam relationships
        {'Source_File': 'frontend/static/js/exams.js', 'Target_File': 'backend/routes/exam_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadExams() -> /exams'},
        {'Source_File': 'backend/routes/exam_routes.py', 'Target_File': 'database/procedures/sp_exam_management.sql', 'Relationship': 'SP Call', 'Function/Endpoint': 'get_exams() -> sp_GetExams'},
        {'Source_File': 'frontend/static/js/exam-crud-functions.js', 'Target_File': 'backend/routes/exam_management_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'CRUD operations'},

        # Timetable relationships
        {'Source_File': 'frontend/static/js/timetable.js', 'Target_File': 'backend/routes/timetable_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadTimetable() -> /timetable'},
        {'Source_File': 'frontend/static/js/timetable_grid.js', 'Target_File': 'backend/routes/timetable_management_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'Grid operations'},
        {'Source_File': 'backend/routes/timetable_routes.py', 'Target_File': 'database/procedures/sp_timetable_management.sql', 'Relationship': 'SP Call', 'Function/Endpoint': 'Timetable SPs'},

        # Classes and Subjects
        {'Source_File': 'frontend/static/js/classes.js', 'Target_File': 'backend/routes/class_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadClasses() -> /classes'},
        {'Source_File': 'frontend/static/js/subjects.js', 'Target_File': 'backend/routes/subject_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadSubjects() -> /subjects'},
        {'Source_File': 'backend/routes/class_routes.py', 'Target_File': 'database/procedures/sp_class_management.sql', 'Relationship': 'SP Call', 'Function/Endpoint': 'Class management SPs'},

        # Reports
        {'Source_File': 'frontend/static/js/reports.js', 'Target_File': 'backend/routes/report_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'generateReport() -> /reports/*'},

        # Settings
        {'Source_File': 'frontend/static/js/settings.js', 'Target_File': 'backend/routes/settings_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadSettings() -> /settings'},
        {'Source_File': 'backend/routes/settings_routes.py', 'Target_File': 'database/procedures/sp_school_settings.sql', 'Relationship': 'SP Call', 'Function/Endpoint': 'Settings SPs'},

        # Holidays
        {'Source_File': 'frontend/static/js/holidays.js', 'Target_File': 'backend/routes/holiday_routes.py', 'Relationship': 'API Call', 'Function/Endpoint': 'loadHolidays() -> /holidays'},
        {'Source_File': 'backend/routes/holiday_routes.py', 'Target_File': 'database/procedures/sp_holiday_management.sql', 'Relationship': 'SP Call', 'Function/Endpoint': 'Holiday SPs'},
    ]

    return detailed_mapping

def create_file_inventory():
    """Create complete file inventory"""

    file_inventory = [
        # Frontend JavaScript Files
        {'File_Path': 'frontend/static/js/core.js', 'Type': 'Frontend JS', 'Module': 'Core', 'Purpose': 'Core utilities and common functions', 'Dependencies': 'None'},
        {'File_Path': 'frontend/static/js/core.module.js', 'Type': 'Frontend JS', 'Module': 'Core', 'Purpose': 'Core module with SMS namespace', 'Dependencies': 'None'},
        {'File_Path': 'frontend/static/js/api.js', 'Type': 'Frontend JS', 'Module': 'Core', 'Purpose': 'API utility functions', 'Dependencies': 'core.js'},
        {'File_Path': 'frontend/static/js/auth.js', 'Type': 'Frontend JS', 'Module': 'Authentication', 'Purpose': 'Login/logout functionality', 'Dependencies': 'api.js'},
        {'File_Path': 'frontend/static/js/router.js', 'Type': 'Frontend JS', 'Module': 'Core', 'Purpose': 'Client-side routing', 'Dependencies': 'core.js'},
        {'File_Path': 'frontend/static/js/dashboard.js', 'Type': 'Frontend JS', 'Module': 'Dashboard', 'Purpose': 'Dashboard data loading and display', 'Dependencies': 'api.js, auth.js'},
        {'File_Path': 'frontend/static/js/users.js', 'Type': 'Frontend JS', 'Module': 'Users', 'Purpose': 'User management (legacy)', 'Dependencies': 'api.js'},
        {'File_Path': 'frontend/static/js/users.module.js', 'Type': 'Frontend JS', 'Module': 'Users', 'Purpose': 'User management (modular)', 'Dependencies': 'core.module.js'},
        {'File_Path': 'frontend/static/js/students.module.js', 'Type': 'Frontend JS', 'Module': 'Students', 'Purpose': 'Student management', 'Dependencies': 'core.module.js'},
        {'File_Path': 'frontend/static/js/student-modals.js', 'Type': 'Frontend JS', 'Module': 'Students', 'Purpose': 'Student modal dialogs', 'Dependencies': 'students.module.js'},
        {'File_Path': 'frontend/static/js/attendance.module.js', 'Type': 'Frontend JS', 'Module': 'Attendance', 'Purpose': 'Attendance management', 'Dependencies': 'core.module.js'},
        {'File_Path': 'frontend/static/js/attendance-functions.js', 'Type': 'Frontend JS', 'Module': 'Attendance', 'Purpose': 'Attendance utility functions', 'Dependencies': 'attendance.module.js'},
        {'File_Path': 'frontend/static/js/fees.module.js', 'Type': 'Frontend JS', 'Module': 'Fees', 'Purpose': 'Fee management', 'Dependencies': 'core.module.js'},
        {'File_Path': 'frontend/static/js/exams.js', 'Type': 'Frontend JS', 'Module': 'Exams', 'Purpose': 'Exam management', 'Dependencies': 'api.js'},
        {'File_Path': 'frontend/static/js/timetable.js', 'Type': 'Frontend JS', 'Module': 'Timetable', 'Purpose': 'Timetable management', 'Dependencies': 'api.js'},
        {'File_Path': 'frontend/static/js/classes.js', 'Type': 'Frontend JS', 'Module': 'Classes', 'Purpose': 'Class management', 'Dependencies': 'api.js'},
        {'File_Path': 'frontend/static/js/subjects.js', 'Type': 'Frontend JS', 'Module': 'Subjects', 'Purpose': 'Subject management', 'Dependencies': 'api.js'},
        {'File_Path': 'frontend/static/js/staff.js', 'Type': 'Frontend JS', 'Module': 'Staff', 'Purpose': 'Staff management', 'Dependencies': 'api.js'},
        {'File_Path': 'frontend/static/js/teacher_pages.js', 'Type': 'Frontend JS', 'Module': 'Teachers', 'Purpose': 'Teacher management pages', 'Dependencies': 'api.js'},
        {'File_Path': 'frontend/static/js/reports.js', 'Type': 'Frontend JS', 'Module': 'Reports', 'Purpose': 'Report generation', 'Dependencies': 'api.js'},
        {'File_Path': 'frontend/static/js/holidays.js', 'Type': 'Frontend JS', 'Module': 'Holidays', 'Purpose': 'Holiday management', 'Dependencies': 'api.js'},
        {'File_Path': 'frontend/static/js/settings.js', 'Type': 'Frontend JS', 'Module': 'Settings', 'Purpose': 'System settings', 'Dependencies': 'api.js'},

        # Backend Python Files
        {'File_Path': 'backend/main.py', 'Type': 'Backend Python', 'Module': 'Core', 'Purpose': 'Main FastAPI application', 'Dependencies': 'All route modules'},
        {'File_Path': 'backend/config.py', 'Type': 'Backend Python', 'Module': 'Core', 'Purpose': 'Configuration settings', 'Dependencies': 'None'},
        {'File_Path': 'backend/database.py', 'Type': 'Backend Python', 'Module': 'Core', 'Purpose': 'Database connection and utilities', 'Dependencies': 'config.py'},
        {'File_Path': 'backend/auth.py', 'Type': 'Backend Python', 'Module': 'Core', 'Purpose': 'Authentication middleware', 'Dependencies': 'database.py'},
        {'File_Path': 'backend/models.py', 'Type': 'Backend Python', 'Module': 'Core', 'Purpose': 'Pydantic models', 'Dependencies': 'None'},
        {'File_Path': 'backend/routes/auth_routes.py', 'Type': 'Backend Python', 'Module': 'Authentication', 'Purpose': 'Authentication endpoints', 'Dependencies': 'auth.py, database.py'},
        {'File_Path': 'backend/routes/dashboard_routes.py', 'Type': 'Backend Python', 'Module': 'Dashboard', 'Purpose': 'Dashboard API endpoints', 'Dependencies': 'database.py'},
        {'File_Path': 'backend/routes/user_routes.py', 'Type': 'Backend Python', 'Module': 'Users', 'Purpose': 'User management endpoints', 'Dependencies': 'database.py, auth.py'},
        {'File_Path': 'backend/routes/student_routes.py', 'Type': 'Backend Python', 'Module': 'Students', 'Purpose': 'Student management endpoints', 'Dependencies': 'database.py, models.py'},
        {'File_Path': 'backend/routes/attendance_routes.py', 'Type': 'Backend Python', 'Module': 'Attendance', 'Purpose': 'Attendance endpoints', 'Dependencies': 'database.py, services'},
        {'File_Path': 'backend/routes/fee_routes.py', 'Type': 'Backend Python', 'Module': 'Fees', 'Purpose': 'Fee management endpoints', 'Dependencies': 'database.py, services'},
        {'File_Path': 'backend/routes/exam_routes.py', 'Type': 'Backend Python', 'Module': 'Exams', 'Purpose': 'Exam management endpoints', 'Dependencies': 'database.py'},
        {'File_Path': 'backend/routes/teacher_routes.py', 'Type': 'Backend Python', 'Module': 'Teachers', 'Purpose': 'Teacher management endpoints', 'Dependencies': 'database.py'},
        {'File_Path': 'backend/routes/class_routes.py', 'Type': 'Backend Python', 'Module': 'Classes', 'Purpose': 'Class management endpoints', 'Dependencies': 'database.py'},
        {'File_Path': 'backend/routes/timetable_routes.py', 'Type': 'Backend Python', 'Module': 'Timetable', 'Purpose': 'Timetable endpoints', 'Dependencies': 'database.py'},

        # Database Stored Procedures
        {'File_Path': 'database/procedures/sp_auth_management.sql', 'Type': 'Database SP', 'Module': 'Authentication', 'Purpose': 'Authentication procedures', 'Dependencies': 'users, roles tables'},
        {'File_Path': 'database/procedures/sp_dashboard_stats.sql', 'Type': 'Database SP', 'Module': 'Dashboard', 'Purpose': 'Dashboard statistics', 'Dependencies': 'Multiple tables'},
        {'File_Path': 'database/procedures/sp_user_management.sql', 'Type': 'Database SP', 'Module': 'Users', 'Purpose': 'User CRUD operations', 'Dependencies': 'users, roles tables'},
        {'File_Path': 'database/procedures/sp_student_management.sql', 'Type': 'Database SP', 'Module': 'Students', 'Purpose': 'Student CRUD operations', 'Dependencies': 'students, classes tables'},
        {'File_Path': 'database/procedures/sp_attendance_management.sql', 'Type': 'Database SP', 'Module': 'Attendance', 'Purpose': 'Attendance operations', 'Dependencies': 'attendance, students tables'},
        {'File_Path': 'database/procedures/sp_fee_management.sql', 'Type': 'Database SP', 'Module': 'Fees', 'Purpose': 'Fee operations', 'Dependencies': 'fee_accounts, fee_collections tables'},
        {'File_Path': 'database/procedures/sp_exam_management.sql', 'Type': 'Database SP', 'Module': 'Exams', 'Purpose': 'Exam operations', 'Dependencies': 'exams, exam_results tables'},
        {'File_Path': 'database/procedures/sp_teacher_management.sql', 'Type': 'Database SP', 'Module': 'Teachers', 'Purpose': 'Teacher operations', 'Dependencies': 'users, teacher_profiles tables'},
        {'File_Path': 'database/procedures/sp_class_management.sql', 'Type': 'Database SP', 'Module': 'Classes', 'Purpose': 'Class operations', 'Dependencies': 'classes table'},
        {'File_Path': 'database/procedures/sp_timetable_management.sql', 'Type': 'Database SP', 'Module': 'Timetable', 'Purpose': 'Timetable operations', 'Dependencies': 'timetables, periods tables'},

        # Templates and Static Files
        {'File_Path': 'frontend/templates/index.html', 'Type': 'HTML Template', 'Module': 'Core', 'Purpose': 'Main application template', 'Dependencies': 'All CSS/JS files'},
        {'File_Path': 'frontend/static/css/style.css', 'Type': 'CSS', 'Module': 'Core', 'Purpose': 'Main stylesheet', 'Dependencies': 'None'},
    ]

    return file_inventory

def create_change_guide():
    """Create a guide for making changes to different modules"""

    change_guide = [
        {
            'Change_Type': 'Add New Student Field',
            'Frontend_Changes': 'students.module.js, student-modals.js (add form field)',
            'Backend_Changes': 'student_routes.py (update model), models.py (add field)',
            'Database_Changes': 'sp_student_management.sql (update procedures), ALTER TABLE students',
            'Testing_Files': 'Test student creation/update forms',
            'Notes': 'Remember to update both create and update procedures'
        },
        {
            'Change_Type': 'Modify Dashboard Stats',
            'Frontend_Changes': 'dashboard.js (update loadDashboardStats function)',
            'Backend_Changes': 'dashboard_routes.py (modify get_dashboard_stats)',
            'Database_Changes': 'sp_dashboard_stats.sql (update sp_GetDashboardStats)',
            'Testing_Files': 'Test dashboard loading',
            'Notes': 'Dashboard pulls data from multiple modules'
        },
        {
            'Change_Type': 'Add New User Role',
            'Frontend_Changes': 'users.module.js, auth.js (update role checks)',
            'Backend_Changes': 'user_routes.py, auth.py (update permissions)',
            'Database_Changes': 'INSERT INTO roles table, update sp_user_management.sql',
            'Testing_Files': 'Test login and permissions for new role',
            'Notes': 'Update all permission checks across modules'
        },
        {
            'Change_Type': 'Modify Attendance Marking',
            'Frontend_Changes': 'attendance.module.js, attendance-functions.js',
            'Backend_Changes': 'attendance_routes.py (update mark_attendance)',
            'Database_Changes': 'sp_attendance_management.sql (update procedures)',
            'Testing_Files': 'Test attendance marking for different scenarios',
            'Notes': 'Consider impact on reports and statistics'
        },
        {
            'Change_Type': 'Add New Fee Type',
            'Frontend_Changes': 'fees.module.js (update fee forms)',
            'Backend_Changes': 'fee_routes.py, fee_structure_routes.py',
            'Database_Changes': 'sp_fee_management.sql, fee_structure table',
            'Testing_Files': 'Test fee calculation and collection',
            'Notes': 'Update fee generation and calculation logic'
        },
        {
            'Change_Type': 'Modify Exam Results',
            'Frontend_Changes': 'exams.js, exam-crud-functions.js',
            'Backend_Changes': 'exam_routes.py, exam_marks_routes.py',
            'Database_Changes': 'sp_exam_management.sql (update result procedures)',
            'Testing_Files': 'Test marks entry and result generation',
            'Notes': 'Consider impact on report generation'
        },
        {
            'Change_Type': 'Update Timetable Logic',
            'Frontend_Changes': 'timetable.js, timetable_grid.js',
            'Backend_Changes': 'timetable_routes.py, timetable_management_routes.py',
            'Database_Changes': 'sp_timetable_management.sql',
            'Testing_Files': 'Test timetable creation and conflicts',
            'Notes': 'Check teacher schedule conflicts'
        },
        {
            'Change_Type': 'Add New Report Type',
            'Frontend_Changes': 'reports.js (add report option)',
            'Backend_Changes': 'report_routes.py (add new endpoint)',
            'Database_Changes': 'Create new stored procedure for report',
            'Testing_Files': 'Test report generation and export',
            'Notes': 'Consider data sources and performance'
        },
        {
            'Change_Type': 'Modify Login Process',
            'Frontend_Changes': 'auth.js (update login function)',
            'Backend_Changes': 'auth_routes.py (modify authentication)',
            'Database_Changes': 'sp_auth_management.sql (update login procedure)',
            'Testing_Files': 'Test login with different user types',
            'Notes': 'Test session management and security'
        },
        {
            'Change_Type': 'Update School Settings',
            'Frontend_Changes': 'settings.js (add/modify setting fields)',
            'Backend_Changes': 'settings_routes.py (update endpoints)',
            'Database_Changes': 'sp_school_settings.sql, school_settings table',
            'Testing_Files': 'Test settings save and load',
            'Notes': 'Consider impact on other modules using settings'
        }
    ]

    return change_guide

if __name__ == "__main__":
    # Create workbook
    wb = Workbook()

    # Remove default sheet
    wb.remove(wb.active)

    # Create Module Overview sheet
    ws1 = wb.create_sheet("Module Overview")
    modules_data = create_file_mapping()
    df1 = pd.DataFrame(modules_data)

    for r in dataframe_to_rows(df1, index=False, header=True):
        ws1.append(r)

    # Style the header
    for cell in ws1[1]:
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.font = Font(color="FFFFFF", bold=True)

    # Auto-adjust column widths
    for column in ws1.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws1.column_dimensions[column_letter].width = adjusted_width

    # Create File Relationships sheet
    ws2 = wb.create_sheet("File Relationships")
    detailed_data = create_detailed_file_mapping()
    df2 = pd.DataFrame(detailed_data)

    for r in dataframe_to_rows(df2, index=False, header=True):
        ws2.append(r)

    # Style the header
    for cell in ws2[1]:
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.font = Font(color="FFFFFF", bold=True)

    # Auto-adjust column widths
    for column in ws2.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 60)
        ws2.column_dimensions[column_letter].width = adjusted_width

    # Create File Inventory sheet
    ws3 = wb.create_sheet("File Inventory")
    inventory_data = create_file_inventory()
    df3 = pd.DataFrame(inventory_data)

    for r in dataframe_to_rows(df3, index=False, header=True):
        ws3.append(r)

    # Style the header
    for cell in ws3[1]:
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.font = Font(color="FFFFFF", bold=True)

    # Auto-adjust column widths
    for column in ws3.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 50)
        ws3.column_dimensions[column_letter].width = adjusted_width

    # Create Change Guide sheet
    ws4 = wb.create_sheet("Change Guide")
    change_data = create_change_guide()
    df4 = pd.DataFrame(change_data)

    for r in dataframe_to_rows(df4, index=False, header=True):
        ws4.append(r)

    # Style the header
    for cell in ws4[1]:
        cell.font = Font(bold=True)
        cell.fill = PatternFill(start_color="366092", end_color="366092", fill_type="solid")
        cell.font = Font(color="FFFFFF", bold=True)

    # Auto-adjust column widths
    for column in ws4.columns:
        max_length = 0
        column_letter = column[0].column_letter
        for cell in column:
            try:
                if len(str(cell.value)) > max_length:
                    max_length = len(str(cell.value))
            except:
                pass
        adjusted_width = min(max_length + 2, 60)
        ws4.column_dimensions[column_letter].width = adjusted_width

    # Save the workbook
    wb.save("SMS_File_Mapping.xlsx")

    print("Excel file mapping created successfully!")
    print("File saved as: SMS_File_Mapping.xlsx")
    print("\nSheets created:")
    print("1. Module Overview - High-level module mapping")
    print("2. File Relationships - Detailed file-to-file relationships")
    print("3. File Inventory - Complete file inventory with dependencies")
    print("4. Change Guide - Quick reference for making changes to different modules")

