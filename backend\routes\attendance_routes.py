"""
Attendance Routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional
from datetime import date
from models import (
    AttendanceMarkRequest, AttendanceBulkMarkRequest, AttendanceEditRequest,
    SuccessResponse
)
from auth import get_current_user, require_teacher
from services.attendance_service import AttendanceService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/mark", response_model=SuccessResponse)
async def mark_attendance(
    request: AttendanceMarkRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Mark attendance for a single student (Teacher/Admin only)

    Args:
        request: Attendance mark request
        current_user: Current authenticated user

    Returns:
        Success response
    """
    result = AttendanceService.mark_attendance(request, current_user['user_id'])

    if result and result.get('Result') == 'Success':
        return SuccessResponse(message=result.get('Message', 'Attendance marked successfully'))
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result.get('Message', 'Failed to mark attendance')
        )


@router.post("/mark-bulk", response_model=SuccessResponse)
async def mark_bulk_attendance(
    request: AttendanceBulkMarkRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Mark attendance for multiple students (Teacher/Admin only)
    
    Args:
        request: Bulk attendance mark request
        current_user: Current authenticated user
    
    Returns:
        Success response with statistics
    """
    result = AttendanceService.mark_bulk_attendance(
        request.class_id,
        request.attendance_date,
        request.attendance_records,
        current_user['user_id']
    )
    
    return SuccessResponse(
        message=f"Marked attendance for {result['success_count']} out of {result['total_count']} students",
        data=result
    )


@router.get("/daily-summary", response_model=list)
async def get_daily_summary(
    attendance_date: date = Query(...),
    class_id: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get daily attendance summary
    
    Args:
        attendance_date: Date to get summary for
        class_id: Optional class ID filter
        current_user: Current authenticated user
    
    Returns:
        List of attendance summaries
    """
    summary = AttendanceService.get_daily_summary(attendance_date, class_id)
    return summary


@router.get("/student/{student_id}", response_model=list)
async def get_student_attendance(
    student_id: int,
    start_date: date = Query(...),
    end_date: date = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get attendance records for a student
    
    Args:
        student_id: Student ID
        start_date: Start date
        end_date: End date
        current_user: Current authenticated user
    
    Returns:
        List of attendance records
    """
    attendance = AttendanceService.get_student_attendance(student_id, start_date, end_date)
    return attendance


@router.get("/class/{class_id}", response_model=list)
async def get_class_attendance(
    class_id: int,
    attendance_date: date = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get attendance for all students in a class on a specific date
    
    Args:
        class_id: Class ID
        attendance_date: Date
        current_user: Current authenticated user
    
    Returns:
        List of attendance records
    """
    attendance = AttendanceService.get_class_attendance(class_id, attendance_date)
    return attendance


@router.get("/student/{student_id}/percentage", response_model=dict)
async def get_attendance_percentage(
    student_id: int,
    start_date: date = Query(...),
    end_date: date = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get attendance percentage for a student
    
    Args:
        student_id: Student ID
        start_date: Start date
        end_date: End date
        current_user: Current authenticated user
    
    Returns:
        Attendance statistics
    """
    stats = AttendanceService.get_attendance_percentage(student_id, start_date, end_date)
    return stats


@router.get("/class/{class_id}/monthly-report", response_model=list)
async def get_monthly_report(
    class_id: int,
    year: int = Query(...),
    month: int = Query(..., ge=1, le=12),
    current_user: dict = Depends(get_current_user)
):
    """
    Get monthly attendance report for a class
    
    Args:
        class_id: Class ID
        year: Year
        month: Month (1-12)
        current_user: Current authenticated user
    
    Returns:
        List of student attendance statistics
    """
    report = AttendanceService.get_monthly_report(class_id, year, month)
    return report


@router.get("/absentees/{class_id}", response_model=list)
async def get_absentees(
    class_id: int,
    attendance_date: date = Query(...),
    current_user: dict = Depends(require_teacher)
):
    """
    Get list of absent students for notification (Teacher/Admin only)
    
    Args:
        class_id: Class ID
        attendance_date: Date
        current_user: Current authenticated user
    
    Returns:
        List of absent students
    """
    absentees = AttendanceService.get_absentees(class_id, attendance_date)
    return absentees


@router.get("/check-holiday", response_model=dict)
async def check_holiday(
    check_date: date = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Check if a date is a holiday

    Args:
        check_date: Date to check
        current_user: Current authenticated user

    Returns:
        Holiday status
    """
    is_holiday = AttendanceService.check_holiday(check_date)

    return {
        "date": check_date,
        "is_holiday": is_holiday
    }


@router.get("/class/{class_id}/students-for-marking", response_model=list)
async def get_students_for_marking(
    class_id: int,
    attendance_date: date = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all students in a class with their existing attendance for bulk marking

    Args:
        class_id: Class ID
        attendance_date: Date for attendance
        current_user: Current authenticated user

    Returns:
        List of students with attendance status
    """
    try:
        students = AttendanceService.get_class_students_for_attendance(class_id, attendance_date)
        return students if students else []
    except Exception as e:
        logger.error(f"Error getting students for attendance marking: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load students: {str(e)}"
        )


@router.put("/edit", response_model=SuccessResponse)
async def edit_attendance(
    request: AttendanceEditRequest,
    current_user: dict = Depends(get_current_user)
):
    """
    Edit existing attendance record with audit trail

    Args:
        request: Edit request
        current_user: Current authenticated user

    Returns:
        Success response
    """
    result = AttendanceService.edit_attendance(request, current_user['user_id'])

    if result.get('success'):
        return SuccessResponse(message=result['message'])
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result['message']
        )


@router.get("/student/{student_id}/report", response_model=dict)
async def get_student_report(
    student_id: int,
    start_date: date = Query(...),
    end_date: date = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get comprehensive attendance report for a student

    Args:
        student_id: Student ID
        start_date: Start date
        end_date: End date
        current_user: Current authenticated user

    Returns:
        Detailed attendance report
    """
    report = AttendanceService.get_student_attendance_report(student_id, start_date, end_date)

    if report:
        return report
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No attendance data found for this student"
        )


@router.get("/class/{class_id}/summary", response_model=dict)
async def get_class_summary(
    class_id: int,
    month: int = Query(..., ge=1, le=12),
    year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get class-wise attendance summary for a month

    Args:
        class_id: Class ID
        month: Month (1-12)
        year: Year
        current_user: Current authenticated user

    Returns:
        Class attendance summary
    """
    summary = AttendanceService.get_class_attendance_summary(class_id, month, year)

    if summary:
        return summary
    else:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No attendance data found for this class"
        )


@router.get("/student/{student_id}/calendar", response_model=list)
async def get_student_calendar(
    student_id: int,
    month: int = Query(..., ge=1, le=12),
    year: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get student's attendance calendar for a month

    Args:
        student_id: Student ID
        month: Month (1-12)
        year: Year
        current_user: Current authenticated user

    Returns:
        List of daily attendance records
    """
    calendar = AttendanceService.get_student_monthly_calendar(student_id, month, year)
    return calendar


@router.post("/mark-all-present", response_model=SuccessResponse)
async def mark_all_present(
    class_id: int = Query(...),
    attendance_date: date = Query(...),
    period: str = Query("Morning"),
    current_user: dict = Depends(require_teacher)
):
    """
    Mark all students in a class as present

    Args:
        class_id: Class ID
        attendance_date: Date
        period: Period (Morning, Afternoon, etc.)
        current_user: Current authenticated user

    Returns:
        Success response
    """
    result = AttendanceService.mark_all_present(class_id, attendance_date, current_user['user_id'], period)

    if result.get('success'):
        return SuccessResponse(message=result['message'], data={'count': result['count']})
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result['message']
        )

