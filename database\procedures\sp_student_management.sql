/* Stored procedures for student management */
SET ANSI_NULLS ON
SET QUOTED_IDENTIFIER ON
GO

-- Create student
IF OBJECT_ID('sp_CreateStudent', 'P') IS NOT NULL
    DROP PROCEDURE sp_CreateStudent
GO
CREATE PROCEDURE sp_CreateStudent
    @admission_number NVARCHAR(50),
    @full_name NVARCHAR(255),
    @full_name_urdu NVARCHAR(255) = NULL,
    @father_name NVARCHAR(255) = NULL,
    @father_name_urdu NVARCHAR(255) = NULL,
    @date_of_birth DATE = NULL,
    @gender NVARCHAR(10) = NULL,
    @cnic NVARCHAR(50) = NULL,
    @b_form NVARCHAR(50) = NULL,
    @class_id INT = NULL,
    @parent_phone NVARCHAR(50) = NULL,
    @parent_email NVARCHAR(255) = NULL,
    @address NVARCHAR(500) = NULL,
    @address_urdu NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO students (admission_number, full_name, full_name_urdu, father_name, father_name_urdu,
                         date_of_birth, gender, cnic, b_form, class_id, parent_phone, parent_email,
                         address, address_urdu, admission_date, is_active, created_at, updated_at)
    VALUES (@admission_number, @full_name, @full_name_urdu, @father_name, @father_name_urdu,
            @date_of_birth, @gender,
            CASE WHEN @cnic IS NOT NULL THEN CONVERT(varbinary(256), @cnic) ELSE NULL END,
            @b_form, @class_id,
            CASE WHEN @parent_phone IS NOT NULL THEN CONVERT(varbinary(256), @parent_phone) ELSE NULL END,
            @parent_email, @address, @address_urdu, CAST(GETDATE() AS DATE), 1, GETDATE(), GETDATE());

    DECLARE @new_id INT = SCOPE_IDENTITY();
    SELECT student_id = @new_id;
END
GO

-- Get students with optional filters
IF OBJECT_ID('sp_GetStudents', 'P') IS NOT NULL
    DROP PROCEDURE sp_GetStudents
GO
CREATE PROCEDURE sp_GetStudents
    @class_id INT = NULL,
    @status NVARCHAR(50) = NULL,
    @search NVARCHAR(255) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SELECT s.*, c.class_name, c.section, c.academic_year
    FROM students s
    LEFT JOIN classes c ON s.class_id = c.class_id
    WHERE (@class_id IS NULL OR s.class_id = @class_id)
      AND (@status IS NULL OR s.status = @status)
      AND (@search IS NULL OR (s.full_name LIKE '%' + @search + '%' OR s.admission_number LIKE '%' + @search + '%' OR c.class_name LIKE '%' + @search + '%'))
    ORDER BY s.full_name;
END
GO

-- Get student by id
IF OBJECT_ID('sp_GetStudentById', 'P') IS NOT NULL
    DROP PROCEDURE sp_GetStudentById
GO
CREATE PROCEDURE sp_GetStudentById
    @student_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT s.*, c.class_name, c.section, c.academic_year
    FROM students s
    LEFT JOIN classes c ON s.class_id = c.class_id
    WHERE s.student_id = @student_id;
END
GO

-- Update student (partial)
IF OBJECT_ID('sp_UpdateStudent', 'P') IS NOT NULL
    DROP PROCEDURE sp_UpdateStudent
GO
CREATE PROCEDURE sp_UpdateStudent
    @student_id INT,
    @full_name NVARCHAR(255) = NULL,
    @full_name_urdu NVARCHAR(255) = NULL,
    @father_name NVARCHAR(255) = NULL,
    @class_id INT = NULL,
    @parent_phone NVARCHAR(50) = NULL,
    @parent_email NVARCHAR(255) = NULL,
    @address NVARCHAR(500) = NULL,
    @status NVARCHAR(50) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE students
    SET full_name = COALESCE(@full_name, full_name),
        full_name_urdu = COALESCE(@full_name_urdu, full_name_urdu),
        father_name = COALESCE(@father_name, father_name),
        class_id = COALESCE(@class_id, class_id),
        parent_phone = COALESCE(CASE WHEN @parent_phone IS NOT NULL THEN CONVERT(varbinary(256), @parent_phone) ELSE parent_phone END, parent_phone),
        parent_email = COALESCE(@parent_email, parent_email),
        address = COALESCE(@address, address),
        status = COALESCE(@status, status),
        updated_at = GETDATE()
    WHERE student_id = @student_id;
END
GO

-- Discharge student (soft delete)
IF OBJECT_ID('sp_DischargeStudent', 'P') IS NOT NULL
    DROP PROCEDURE sp_DischargeStudent
GO
CREATE PROCEDURE sp_DischargeStudent
    @student_id INT
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE students
    SET status = 'Discharged', discharge_date = CAST(GETDATE() AS DATE), is_active = 0, updated_at = GETDATE()
    WHERE student_id = @student_id;
END
GO

-- Get students in a class
IF OBJECT_ID('sp_GetClassStudents', 'P') IS NOT NULL
    DROP PROCEDURE sp_GetClassStudents
GO
CREATE PROCEDURE sp_GetClassStudents
    @class_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT s.student_id, s.admission_number, s.full_name, s.full_name_urdu,
           s.father_name, s.date_of_birth, s.gender, s.status
    FROM students s
    WHERE s.class_id = @class_id AND s.is_active = 1
    ORDER BY s.full_name;
END
GO
