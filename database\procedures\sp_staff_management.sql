-- =============================================
-- Staff Management Stored Procedures
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Procedure: sp_Staff_Create
-- Description: Create a new staff member
-- =============================================
IF OBJECT_ID('sp_Staff_Create', 'P') IS NOT NULL
    DROP PROCEDURE sp_Staff_Create;
GO

CREATE PROCEDURE sp_Staff_Create
    @user_id INT,
    @employee_code NVARCHAR(50),
    @designation NVARCHAR(100),
    @designation_urdu NVARCHAR(100) = NULL,
    @department NVARCHAR(100) = NULL,
    @joining_date DATE,
    @salary DECIMAL(10,2) = NULL,
    @is_active BIT = 1
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if employee code already exists
        IF EXISTS (SELECT 1 FROM staff WHERE employee_code = @employee_code)
        BEGIN
            THROW 50001, 'Employee code already exists', 1;
        END
        
        INSERT INTO staff (
            user_id,
            employee_code,
            designation,
            designation_urdu,
            department,
            joining_date,
            salary,
            is_active
        )
        VALUES (
            @user_id,
            @employee_code,
            @designation,
            @designation_urdu,
            @department,
            @joining_date,
            @salary,
            @is_active
        );
        
        SELECT SCOPE_IDENTITY() AS staff_id;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- Procedure: sp_Staff_GetAll
-- Description: Get all staff members with user details
-- =============================================
IF OBJECT_ID('sp_Staff_GetAll', 'P') IS NOT NULL
    DROP PROCEDURE sp_Staff_GetAll;
GO

CREATE PROCEDURE sp_Staff_GetAll
    @is_active BIT = NULL,
    @department NVARCHAR(100) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        s.staff_id,
        s.user_id,
        s.employee_code,
        s.designation,
        s.designation_urdu,
        s.department,
        s.joining_date,
        s.salary,
        s.is_active,
        s.created_at,
        s.updated_at,
        u.full_name,
        u.email,
        u.phone
    FROM staff s
    INNER JOIN users u ON s.user_id = u.user_id
    WHERE 
        (@is_active IS NULL OR s.is_active = @is_active)
        AND (@department IS NULL OR s.department = @department)
    ORDER BY s.joining_date DESC;
END;
GO

-- =============================================
-- Procedure: sp_Staff_GetById
-- Description: Get staff by ID with user details
-- =============================================
IF OBJECT_ID('sp_Staff_GetById', 'P') IS NOT NULL
    DROP PROCEDURE sp_Staff_GetById;
GO

CREATE PROCEDURE sp_Staff_GetById
    @staff_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        s.staff_id,
        s.user_id,
        s.employee_code,
        s.designation,
        s.designation_urdu,
        s.department,
        s.joining_date,
        s.salary,
        s.is_active,
        s.created_at,
        s.updated_at,
        u.full_name,
        u.email,
        u.phone,
        u.username
    FROM staff s
    INNER JOIN users u ON s.user_id = u.user_id
    WHERE s.staff_id = @staff_id;
END;
GO

-- =============================================
-- Procedure: sp_Staff_Update
-- Description: Update staff member
-- =============================================
IF OBJECT_ID('sp_Staff_Update', 'P') IS NOT NULL
    DROP PROCEDURE sp_Staff_Update;
GO

CREATE PROCEDURE sp_Staff_Update
    @staff_id INT,
    @user_id INT,
    @employee_code NVARCHAR(50),
    @designation NVARCHAR(100),
    @designation_urdu NVARCHAR(100) = NULL,
    @department NVARCHAR(100) = NULL,
    @joining_date DATE,
    @salary DECIMAL(10,2) = NULL,
    @is_active BIT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        -- Check if employee code exists for another staff member
        IF EXISTS (
            SELECT 1 FROM staff 
            WHERE employee_code = @employee_code 
                AND staff_id != @staff_id
        )
        BEGIN
            THROW 50001, 'Employee code already exists for another staff member', 1;
        END
        
        UPDATE staff
        SET 
            user_id = @user_id,
            employee_code = @employee_code,
            designation = @designation,
            designation_urdu = @designation_urdu,
            department = @department,
            joining_date = @joining_date,
            salary = @salary,
            is_active = @is_active,
            updated_at = GETDATE()
        WHERE staff_id = @staff_id;
        
        SELECT @@ROWCOUNT AS rows_affected;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- Procedure: sp_Staff_Delete
-- Description: Delete staff member
-- =============================================
IF OBJECT_ID('sp_Staff_Delete', 'P') IS NOT NULL
    DROP PROCEDURE sp_Staff_Delete;
GO

CREATE PROCEDURE sp_Staff_Delete
    @staff_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    BEGIN TRY
        DELETE FROM staff
        WHERE staff_id = @staff_id;
        
        SELECT @@ROWCOUNT AS rows_affected;
    END TRY
    BEGIN CATCH
        THROW;
    END CATCH
END;
GO

-- =============================================
-- Procedure: sp_Staff_GetDepartments
-- Description: Get list of all departments
-- =============================================
IF OBJECT_ID('sp_Staff_GetDepartments', 'P') IS NOT NULL
    DROP PROCEDURE sp_Staff_GetDepartments;
GO

CREATE PROCEDURE sp_Staff_GetDepartments
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT DISTINCT department
    FROM staff
    WHERE department IS NOT NULL
    ORDER BY department;
END;
GO

-- =============================================
-- Procedure: sp_Staff_GetByUserId
-- Description: Get staff by user ID
-- =============================================
IF OBJECT_ID('sp_Staff_GetByUserId', 'P') IS NOT NULL
    DROP PROCEDURE sp_Staff_GetByUserId;
GO

CREATE PROCEDURE sp_Staff_GetByUserId
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        s.staff_id,
        s.user_id,
        s.employee_code,
        s.designation,
        s.designation_urdu,
        s.department,
        s.joining_date,
        s.salary,
        s.is_active,
        s.created_at,
        s.updated_at
    FROM staff s
    WHERE s.user_id = @user_id;
END;
GO

PRINT 'Staff management stored procedures created successfully!';

