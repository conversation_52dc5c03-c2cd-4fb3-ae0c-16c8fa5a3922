// Fees module (minimal stub)
(function () {
    const SMS = window.SMS = window.SMS || {};

    async function loadFeesPage() {
        const contentArea = document.getElementById('contentArea');
        if (!contentArea) return;

        contentArea.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-money-bill-wave"></i> Fee Management</h2>
            </div>
            <div class="row">
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-file-invoice-dollar"></i> Fee Structures</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-primary w-100 mb-2" onclick="loadPage('fee-structures')">
                                <i class="fas fa-list"></i> View Fee Structures
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-cash-register"></i> Fee Collection</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-success w-100 mb-2" onclick="loadPage('fee-collection')">
                                <i class="fas fa-money-check"></i> Collect Fees
                            </button>
                        </div>
                    </div>
                </div>
                <div class="col-md-4 mb-3">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5><i class="fas fa-exclamation-triangle"></i> Defaulters</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-warning w-100 mb-2" onclick="loadPage('fee-defaulters')">
                                <i class="fas fa-user-times"></i> View Defaulters
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    }

    async function loadFeesOverview() {
        // This function is no longer needed for the main page
        // It was causing the error by calling non-existent /api/fees endpoint
        return [];
    }

    SMS.loadFeesPage = loadFeesPage;
    SMS.loadFeesOverview = loadFeesOverview;

    window.loadFeesPage = loadFeesPage;
    window.loadFeesOverview = loadFeesOverview;
})();
