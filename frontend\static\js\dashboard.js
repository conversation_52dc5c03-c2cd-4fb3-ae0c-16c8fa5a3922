// Dashboard related functions
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    // Note: showDashboard is defined in auth.js
    // This module only handles loading dashboard data

    // Main dashboard page loader (called by navigation)
    SMS.loadDashboardPage = async function() {
        console.log('[DASHBOARD] Loading dashboard page...');
        await SMS.loadDashboardData();
    };

    SMS.loadDashboardData = async function() {
        console.log('[DASHBOARD] Loading dashboard data...');
        const contentArea = document.getElementById('contentArea');
        SMS.showLoading('Loading dashboard...');

        contentArea.innerHTML = `
            <div class="bilingual-header mb-4">
                <div class="english"><h2>Dashboard</h2></div>
                <div class="urdu"><h2>ڈیش بورڈ</h2></div>
            </div>
            <div class="row">
                <div class="col-md-3"><div class="card stat-card"><div class="icon" style="color: var(--primary-color);"><i class="fas fa-user-graduate"></i></div><div class="number" id="totalStudents">0</div><div class="label">Total Students</div></div></div>
                <div class="col-md-3"><div class="card stat-card"><div class="icon" style="color: var(--secondary-color);"><i class="fas fa-chalkboard-teacher"></i></div><div class="number" id="totalTeachers">0</div><div class="label">Total Teachers</div></div></div>
                <div class="col-md-3"><div class="card stat-card"><div class="icon" style="color: var(--warning-color);"><i class="fas fa-clipboard-check"></i></div><div class="number" id="todayAttendance">0%</div><div class="label">Today's Attendance</div></div></div>
                <div class="col-md-3"><div class="card stat-card"><div class="icon" style="color: var(--danger-color);"><i class="fas fa-money-bill-wave"></i></div><div class="number" id="feeDefaulters">0</div><div class="label">Fee Defaulters</div></div></div>
            </div>
        `;

        try {
            // Use the new dashboard stats API endpoint
            await SMS.loadDashboardStats();
        } catch (e) {
            console.error('Dashboard load error:', e);
            // Fallback to individual API calls
            await Promise.all([
                SMS.loadTotalStudents(),
                SMS.loadTotalTeachers(),
                SMS.loadTodayAttendance(),
                SMS.loadFeeDefaulters()
            ]);
        }
    };

    SMS.loadDashboardStats = async function() {
        try {
            const response = await fetch(SMS.apiUrl('/dashboard/stats'), {
                headers: { 'Authorization': `Bearer ${SMS.authToken}` }
            });
            if (response.ok) {
                const stats = await response.json();
                console.log('[DASHBOARD] Loaded stats:', stats);

                // Update all dashboard elements
                document.getElementById('totalStudents').textContent = stats.total_students || 0;
                document.getElementById('totalTeachers').textContent = stats.total_teachers || 0;
                document.getElementById('todayAttendance').textContent = (stats.today_attendance_percentage || 0).toFixed(1) + '%';
                document.getElementById('feeDefaulters').textContent = stats.fee_defaulters || 0;
            } else {
                console.error('Failed to load dashboard stats:', response.status);
                throw new Error('Failed to load dashboard stats');
            }
        } catch (error) {
            console.error('Error loading dashboard stats:', error);
            throw error; // Re-throw to trigger fallback
        }
    };

    SMS.loadTotalStudents = async function() {
        try {
            const response = await fetch(SMS.apiUrl('/students?status=Active'), { headers: { 'Authorization': `Bearer ${SMS.authToken}` } });
            if (response.ok) {
                const students = await response.json();
                document.getElementById('totalStudents').textContent = students.length;
            }
        } catch (error) { console.error('Error loading students:', error); }
    };

    SMS.loadTotalTeachers = async function() {
        try {
            const response = await fetch(SMS.apiUrl('/users'), { headers: { 'Authorization': `Bearer ${SMS.authToken}` } });
            if (response.ok) {
                const users = await response.json();
                const teachers = users.filter(u => u.role_name === 'Teacher');
                document.getElementById('totalTeachers').textContent = teachers.length;
            }
        } catch (error) { console.error('Error loading teachers:', error); }
    };

    SMS.loadTodayAttendance = async function() {
        try {
            const today = new Date().toISOString().split('T')[0];
            const response = await fetch(SMS.apiUrl(`/attendance/daily-summary?attendance_date=${today}`), { headers: { 'Authorization': `Bearer ${SMS.authToken}` } });
            if (response.ok) {
                const summary = await response.json();
                if (summary.length > 0) {
                    const totalPercentage = summary.reduce((sum, s) => sum + parseFloat(s.attendance_percentage || 0), 0);
                    const avgPercentage = (totalPercentage / summary.length).toFixed(1);
                    document.getElementById('todayAttendance').textContent = avgPercentage + '%';
                }
            }
        } catch (error) { console.error('Error loading attendance:', error); }
    };

    SMS.loadFeeDefaulters = async function() {
        try {
            const currentYear = new Date().getFullYear();
            const response = await fetch(SMS.apiUrl(`/fees/defaulters?academic_year=${currentYear}&min_arrears=1000`), { headers: { 'Authorization': `Bearer ${SMS.authToken}` } });
            if (response.ok) {
                const defaulters = await response.json();
                document.getElementById('feeDefaulters').textContent = defaulters.length;
            }
        } catch (error) { console.error('Error loading defaulters:', error); }
    };

})(window);
