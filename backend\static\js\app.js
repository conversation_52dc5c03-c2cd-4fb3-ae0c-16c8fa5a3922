// School Management System - Frontend JavaScript

// Global state
let authToken = null;
let currentUser = null;

// API Base URL
const API_BASE = '/api';

// Initialize app
document.addEventListener('DOMContentLoaded', function() {
    // Check if user is already logged in
    authToken = localStorage.getItem('authToken');
    if (authToken) {
        loadUserProfile();
    }
    
    // Setup event listeners
    setupEventListeners();
});

// Setup event listeners
function setupEventListeners() {
    // Login form
    const loginForm = document.getElementById('loginForm');
    if (loginForm) {
        loginForm.addEventListener('submit', handleLogin);
    }
    
    // Logout button
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', handleLogout);
    }
    
    // Sidebar navigation
    const navLinks = document.querySelectorAll('.sidebar .nav-link');
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            navLinks.forEach(l => l.classList.remove('active'));
            this.classList.add('active');
            const page = this.getAttribute('data-page');
            loadPage(page);
        });
    });
}

// Handle login
async function handleLogin(e) {
    e.preventDefault();
    
    const username = document.getElementById('loginUsername').value;
    const password = document.getElementById('loginPassword').value;
    
    try {
        const response = await fetch(`${API_BASE}/auth/login`, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({ username, password })
        });
        
        if (response.ok) {
            const data = await response.json();
            authToken = data.access_token;
            localStorage.setItem('authToken', authToken);
            
            currentUser = {
                user_id: data.user_id,
                username: data.username,
                role: data.role
            };
            
            showDashboard();
            loadDashboardData();
        } else {
            const error = await response.json();
            alert('Login failed: ' + (error.detail || 'Invalid credentials'));
        }
    } catch (error) {
        console.error('Login error:', error);
        alert('Login failed. Please try again.');
    }
}

// Handle logout
function handleLogout(e) {
    e.preventDefault();
    
    authToken = null;
    currentUser = null;
    localStorage.removeItem('authToken');
    
    document.getElementById('loginSection').style.display = 'block';
    document.getElementById('dashboardSection').style.display = 'none';
    document.getElementById('sidebar').style.display = 'none';
}

// Load user profile
async function loadUserProfile() {
    try {
        const response = await fetch(`${API_BASE}/auth/me`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            currentUser = await response.json();
            document.getElementById('username').textContent = currentUser.username;
            showDashboard();
            loadDashboardData();
        } else {
            handleLogout({ preventDefault: () => {} });
        }
    } catch (error) {
        console.error('Profile load error:', error);
        handleLogout({ preventDefault: () => {} });
    }
}

// Show dashboard
function showDashboard() {
    document.getElementById('loginSection').style.display = 'none';
    document.getElementById('dashboardSection').style.display = 'block';
    document.getElementById('sidebar').style.display = 'block';
}

// Load dashboard data
async function loadDashboardData() {
    try {
        // Load statistics
        await Promise.all([
            loadTotalStudents(),
            loadTotalTeachers(),
            loadTodayAttendance(),
            loadFeeDefaulters()
        ]);
    } catch (error) {
        console.error('Dashboard load error:', error);
    }
}

// Load total students
async function loadTotalStudents() {
    try {
        const response = await fetch(`${API_BASE}/students?status=Active`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            const students = await response.json();
            document.getElementById('totalStudents').textContent = students.length;
        }
    } catch (error) {
        console.error('Error loading students:', error);
    }
}

// Load total teachers
async function loadTotalTeachers() {
    try {
        const response = await fetch(`${API_BASE}/auth/users`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            const users = await response.json();
            const teachers = users.filter(u => u.role_name === 'Teacher');
            document.getElementById('totalTeachers').textContent = teachers.length;
        }
    } catch (error) {
        console.error('Error loading teachers:', error);
    }
}

// Load today's attendance
async function loadTodayAttendance() {
    try {
        const today = new Date().toISOString().split('T')[0];
        const response = await fetch(`${API_BASE}/attendance/daily-summary?attendance_date=${today}`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            const summary = await response.json();
            if (summary.length > 0) {
                const totalPercentage = summary.reduce((sum, s) => sum + parseFloat(s.attendance_percentage || 0), 0);
                const avgPercentage = (totalPercentage / summary.length).toFixed(1);
                document.getElementById('todayAttendance').textContent = avgPercentage + '%';
            }
        }
    } catch (error) {
        console.error('Error loading attendance:', error);
    }
}

// Load fee defaulters
async function loadFeeDefaulters() {
    try {
        const currentYear = new Date().getFullYear();
        const response = await fetch(`${API_BASE}/fees/defaulters?academic_year=${currentYear}&min_arrears=1000`, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });
        
        if (response.ok) {
            const defaulters = await response.json();
            document.getElementById('feeDefaulters').textContent = defaulters.length;
        }
    } catch (error) {
        console.error('Error loading defaulters:', error);
    }
}

// Load page content
function loadPage(page) {
    // This would load different page content
    // For now, just show an alert
    alert(`Loading ${page} page...`);
}

// Utility function for API calls
async function apiCall(endpoint, method = 'GET', body = null) {
    const options = {
        method,
        headers: {
            'Authorization': `Bearer ${authToken}`,
            'Content-Type': 'application/json'
        }
    };
    
    if (body) {
        options.body = JSON.stringify(body);
    }
    
    const response = await fetch(`${API_BASE}${endpoint}`, options);
    
    if (!response.ok) {
        const error = await response.json();
        throw new Error(error.detail || 'API call failed');
    }
    
    return response.json();
}

// Format date for display
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Format currency
function formatCurrency(amount) {
    return 'Rs. ' + parseFloat(amount).toLocaleString('en-US', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
    });
}

