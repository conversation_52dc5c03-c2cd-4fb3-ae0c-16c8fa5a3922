"""
Fee Service - Business logic for fee management
"""
from typing import List, Dict, Optional
from datetime import date
from decimal import Decimal
from database import execute_procedure, execute_procedure_single, execute_query, execute_non_query
from models import (FeeCollectionRequest, FeeStructureCreate,
                    ClassFeeStructureCreate, StudentDiscountCreate)
import logging

logger = logging.getLogger(__name__)


class FeeService:
    """Service class for fee operations"""
    
    @staticmethod
    def collect_fee(request: FeeCollectionRequest, collected_by: int) -> Dict:
        """
        Collect fee from student
        
        Args:
            request: Fee collection request
            collected_by: User ID who is collecting fee
        
        Returns:
            Result dictionary with receipt number
        """
        params = {
            'student_id': request.student_id,
            'fee_account_id': request.fee_account_id,
            'fee_type': request.fee_type,
            'fee_month': request.fee_month,
            'amount': float(request.amount),
            'fine_amount': float(request.fine_amount),
            'payment_method': request.payment_method,
            'collected_by': collected_by,
            'remarks': request.remarks
        }
        
        result = execute_procedure_single('sp_FeeCollection', params)
        return result
    
    @staticmethod
    def calculate_fine(student_id: int, fee_month: int, academic_year: int) -> Dict:
        """
        Calculate fine for late payment
        
        Args:
            student_id: Student ID
            fee_month: Fee month (1-12)
            academic_year: Academic year
        
        Returns:
            Dictionary with calculated fine
        """
        params = {
            'student_id': student_id,
            'fee_month': fee_month,
            'academic_year': academic_year
        }
        
        result = execute_procedure_single('sp_FeeFineLogic', params)
        return result
    
    @staticmethod
    def calculate_arrears(academic_year: int, current_month: int) -> Dict:
        """
        Calculate arrears for all students
        
        Args:
            academic_year: Academic year
            current_month: Current month (1-12)
        
        Returns:
            Result dictionary
        """
        params = {
            'academic_year': academic_year,
            'current_month': current_month
        }
        
        result = execute_procedure_single('sp_FeeArrearsCalculation', params)
        return result
    
    @staticmethod
    def create_fee_structure(structure: FeeStructureCreate) -> int:
        """
        Create fee structure for a class
        
        Args:
            structure: Fee structure data
        
        Returns:
            New fee structure ID
        """
        params = {
            'class_id': structure.class_id,
            'academic_year': structure.academic_year,
            'fee_type': structure.fee_type,
            'fee_type_urdu': structure.fee_type_urdu,
            'amount': float(structure.amount),
            'due_day': structure.due_day,
            'fine_per_day': float(structure.fine_per_day)
        }

        res = execute_procedure_single('sp_CreateFeeStructure', params)
        return int(res.get('fee_structure_id')) if res and res.get('fee_structure_id') else None
    
    @staticmethod
    def get_fee_account(student_id: int, academic_year: int) -> Optional[Dict]:
        """
        Get fee account for a student
        
        Args:
            student_id: Student ID
            academic_year: Academic year
        
        Returns:
            Fee account dictionary or None
        """
        res = execute_procedure_single('sp_GetFeeAccount', {'student_id': student_id, 'academic_year': academic_year})
        return res
    
    @staticmethod
    def create_fee_account(student_id: int, academic_year: int) -> int:
        """
        Create fee account for a student
        
        Args:
            student_id: Student ID
            academic_year: Academic year
        
        Returns:
            New fee account ID
        """
        res = execute_procedure_single('sp_CreateFeeAccount', {'student_id': student_id, 'academic_year': academic_year})
        return int(res.get('fee_account_id')) if res and res.get('fee_account_id') else None
    
    @staticmethod
    def get_fee_transactions(student_id: int, academic_year: Optional[int] = None) -> List[Dict]:
        """
        Get fee transaction history for a student
        
        Args:
            student_id: Student ID
            academic_year: Optional academic year filter
        
        Returns:
            List of fee transactions
        """
        params = {'student_id': student_id, 'academic_year': academic_year}
        if academic_year:
            return execute_procedure('sp_GetFeeTransactions', params)
        return execute_procedure('sp_GetFeeTransactions', params)
    
    @staticmethod
    def get_defaulters(academic_year: int, min_arrears: Decimal = Decimal('1000')) -> List[Dict]:
        """
        Get list of fee defaulters
        
        Args:
            academic_year: Academic year
            min_arrears: Minimum arrears amount
        
        Returns:
            List of defaulter students
        """
        return execute_procedure('sp_GetDefaulters', {'academic_year': academic_year, 'min_arrears': float(min_arrears)})
    
    @staticmethod
    def get_daily_collection(collection_date: date) -> Dict:
        """
        Get daily fee collection summary
        
        Args:
            collection_date: Date
        
        Returns:
            Collection summary dictionary
        """
        return execute_procedure_single('sp_GetDailyCollection', {'collection_date': collection_date})
    
    @staticmethod
    def get_monthly_collection(year: int, month: int) -> Dict:
        """
        Get monthly fee collection summary
        
        Args:
            year: Year
            month: Month (1-12)
        
        Returns:
            Collection summary dictionary
        """
        return execute_procedure_single('sp_GetMonthlyCollection', {'year': year, 'month': month})
    
    @staticmethod
    def get_fee_structures(class_id: Optional[int] = None, academic_year: Optional[int] = None) -> List[Dict]:
        """
        Get fee structures
        
        Args:
            class_id: Optional class ID filter
            academic_year: Optional academic year filter
        
        Returns:
            List of fee structures
        """
        return execute_procedure('sp_GetFeeStructures', {'class_id': class_id, 'academic_year': academic_year})

    @staticmethod
    def set_class_fee_structure(fee_structure: ClassFeeStructureCreate) -> Dict:
        """
        Set or update fee structure for a class

        Args:
            fee_structure: Class fee structure data

        Returns:
            Result dictionary
        """
        params = {
            'class_id': fee_structure.class_id,
            'academic_year': fee_structure.academic_year,
            'monthly_fee': float(fee_structure.monthly_fee),
            'admission_fee': float(fee_structure.admission_fee),
            'annual_fee': float(fee_structure.annual_fee),
            'exam_fee': float(fee_structure.exam_fee),
            'transport_fee': float(fee_structure.transport_fee),
            'other_fee': float(fee_structure.other_fee)
        }

        try:
            result = execute_procedure_single('sp_SetClassFeeStructure', params)
            if result:
                # Return formatted result
                return {
                    'message': result.get('message', 'Fee structure set successfully'),
                    'class_id': fee_structure.class_id,
                    'academic_year': fee_structure.academic_year
                }
            else:
                return {
                    'message': 'Fee structure set successfully',
                    'class_id': fee_structure.class_id,
                    'academic_year': fee_structure.academic_year
                }
        except Exception as e:
            logger.error(f"Error setting class fee structure: {e}")
            raise

    @staticmethod
    def get_class_fee_structure(class_id: int, academic_year: int) -> Dict:
        """
        Get fee structure for a class

        Args:
            class_id: Class ID
            academic_year: Academic year

        Returns:
            Fee structure details
        """
        return execute_procedure_single('sp_GetClassFeeStructure', {'class_id': class_id, 'academic_year': academic_year})

    @staticmethod
    def get_all_class_fee_structures(academic_year: int) -> List[Dict]:
        """
        Get fee structures for all classes

        Args:
            academic_year: Academic year

        Returns:
            List of fee structures with class details
        """
        query = """
            SELECT
                cfs.fee_structure_id,
                cfs.class_id,
                c.class_name,
                c.section,
                cfs.academic_year,
                cfs.monthly_fee,
                cfs.admission_fee,
                cfs.annual_fee,
                cfs.exam_fee,
                cfs.transport_fee,
                cfs.other_fee,
                cfs.is_active
            FROM class_fee_structure cfs
            INNER JOIN classes c ON cfs.class_id = c.class_id
            WHERE cfs.academic_year = ? AND cfs.is_active = 1
            ORDER BY c.class_name, c.section
        """

        return execute_query(query, (academic_year,))

    @staticmethod
    def set_student_discount(discount: StudentDiscountCreate, created_by: int) -> Dict:
        """
        Set discount for a student

        Args:
            discount: Student discount data
            created_by: User ID who is creating discount

        Returns:
            Result dictionary
        """
        params = {
            'student_id': discount.student_id,
            'academic_year': discount.academic_year,
            'discount_type': discount.discount_type,
            'discount_percentage': float(discount.discount_percentage),
            'discount_amount': float(discount.discount_amount),
            'apply_to': discount.apply_to,
            'remarks': discount.remarks,
            'created_by': created_by
        }

        result = execute_procedure_single('sp_SetStudentDiscount', params)
        return result

    @staticmethod
    def get_student_discount(student_id: int, academic_year: int) -> Dict:
        """
        Get discount for a student

        Args:
            student_id: Student ID
            academic_year: Academic year

        Returns:
            Discount details
        """
        query = """
            SELECT
                discount_id,
                student_id,
                academic_year,
                discount_type,
                discount_percentage,
                discount_amount,
                apply_to,
                remarks,
                is_active
            FROM student_fee_discounts
            WHERE student_id = ? AND academic_year = ? AND is_active = 1
        """

        results = execute_query(query, (student_id, academic_year))
        return results[0] if results else None

    @staticmethod
    def get_all_student_discounts(academic_year: int) -> List[Dict]:
        """
        Get all student discounts for an academic year

        Args:
            academic_year: Academic year

        Returns:
            List of student discounts with student details
        """
        query = """
            SELECT
                sfd.discount_id,
                sfd.student_id,
                s.full_name AS student_name,
                s.admission_number,
                c.class_name,
                c.section,
                sfd.academic_year,
                sfd.discount_type,
                sfd.discount_percentage,
                sfd.discount_amount,
                sfd.apply_to,
                sfd.remarks,
                sfd.is_active
            FROM student_fee_discounts sfd
            INNER JOIN students s ON sfd.student_id = s.student_id
            INNER JOIN classes c ON s.class_id = c.class_id
            WHERE sfd.academic_year = ? AND sfd.is_active = 1
            ORDER BY c.class_name, c.section, s.full_name
        """

        return execute_query(query, (academic_year,))

    @staticmethod
    def collect_fee_comprehensive(request: FeeCollectionRequest, collected_by: int) -> Dict:
        """
        Collect fee with comprehensive ledger management

        Args:
            request: Fee collection request
            collected_by: User ID who is collecting fee

        Returns:
            Result dictionary with balance and advance amount
        """
        params = {
            'student_id': request.student_id,
            'academic_year': request.academic_year,
            'amount': float(request.amount),
            'payment_method': request.payment_method,
            'reference_number': request.reference_number,
            'description': request.description,
            'collected_by': collected_by
        }

        result = execute_procedure_single('sp_CollectFee', params)
        return result

    @staticmethod
    def get_student_ledger(student_id: int, academic_year: Optional[int] = None) -> List[Dict]:
        """
        Get complete fee ledger for a student

        Args:
            student_id: Student ID
            academic_year: Optional academic year filter

        Returns:
            List of ledger entries
        """
        params = {
            'student_id': student_id,
            'academic_year': academic_year
        }

        return execute_procedure('sp_GetStudentFeeLedger', params)

    @staticmethod
    def get_student_outstanding(student_id: int, academic_year: int) -> Dict:
        """
        Get total outstanding amount for a student

        Args:
            student_id: Student ID
            academic_year: Academic year

        Returns:
            Outstanding summary
        """
        params = {
            'student_id': student_id,
            'academic_year': academic_year
        }

        result = execute_procedure_single('sp_GetStudentOutstanding', params)
        return result

    @staticmethod
    def get_students_for_collection(class_id: int, academic_year: int) -> List[Dict]:
        """
        Get all students in a class with their fee status

        Args:
            class_id: Class ID
            academic_year: Academic year

        Returns:
            List of students with fee status
        """
        return execute_procedure('sp_GetStudentsForCollection', {'class_id': class_id, 'academic_year': academic_year})

    @staticmethod
    def get_class_monthly_fee_overview(class_id: int, academic_year: int) -> Dict:
        """
        Get month-by-month fee status for all students in a class

        Args:
            class_id: Class ID
            academic_year: Academic year

        Returns:
            Dictionary with students and their monthly fee status
        """
        # Get all students in the class
        students_query = """
            SELECT
                s.student_id,
                s.admission_number,
                s.full_name,
                ISNULL(
                    (SELECT TOP 1 balance
                     FROM fee_ledger
                     WHERE student_id = s.student_id
                     ORDER BY ledger_id DESC),
                    0
                ) AS current_balance,
                (SELECT discount_percentage
                 FROM student_fee_discounts
                 WHERE student_id = s.student_id
                 AND academic_year = ?
                 AND is_active = 1) AS discount_percentage
            FROM students s
            WHERE s.class_id = ? AND s.is_active = 1
            ORDER BY s.admission_number, s.full_name
        """

        students = execute_query(students_query, (academic_year, class_id))

        # Get monthly fee status for all students
        monthly_fees_query = """
            SELECT
                student_id,
                fee_month,
                fee_year,
                base_fee,
                discount_amount,
                final_fee,
                paid_amount,
                outstanding_amount,
                status,
                paid_date
            FROM student_monthly_fees
            WHERE student_id IN (
                SELECT student_id FROM students WHERE class_id = ? AND is_active = 1
            )
            AND academic_year = ?
            ORDER BY student_id, fee_year, fee_month
        """

        monthly_fees = execute_query(monthly_fees_query, (class_id, academic_year))

        # Organize monthly fees by student
        student_fees_map = {}
        for fee in monthly_fees:
            student_id = fee['student_id']
            if student_id not in student_fees_map:
                student_fees_map[student_id] = []
            student_fees_map[student_id].append(fee)

        # Combine students with their monthly fees
        result = []
        for student in students:
            student_id = student['student_id']
            student_data = {
                'student_id': student_id,
                'admission_number': student['admission_number'],
                'full_name': student['full_name'],
                'current_balance': float(student['current_balance']) if student['current_balance'] else 0,
                'discount_percentage': float(student['discount_percentage']) if student['discount_percentage'] else 0,
                'monthly_fees': student_fees_map.get(student_id, [])
            }
            result.append(student_data)

        return {
            'class_id': class_id,
            'academic_year': academic_year,
            'students': result
        }

    @staticmethod
    def generate_monthly_fees(class_id: Optional[int], academic_year: int, start_month: int = 1, end_month: int = 12) -> Dict:
        """
        Generate monthly fee records for students

        Args:
            class_id: Class ID (None for all classes)
            academic_year: Academic year
            start_month: Starting month (1-12)
            end_month: Ending month (1-12)

        Returns:
            Result dictionary with generation statistics
        """
        params = {
            'class_id': class_id,
            'academic_year': academic_year,
            'start_month': start_month,
            'end_month': end_month
        }

        result = execute_procedure_single('sp_GenerateMonthlyFees', params)
        return result

