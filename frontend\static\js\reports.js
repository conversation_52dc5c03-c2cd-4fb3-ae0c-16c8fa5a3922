// Reports & Analytics Module
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    SMS.loadReportsPage = async function() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="mb-4">
                <h2><i class="fas fa-file-pdf"></i> Reports & Analytics</h2>
            </div>

            <div class="row">
                <!-- Attendance Reports -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-primary text-white">
                            <h5><i class="fas fa-clipboard-check"></i> Attendance Reports</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-primary w-100 mb-2" onclick="SMS.generateDailyAttendanceReport()">
                                <i class="fas fa-calendar-day"></i> Daily Attendance Report
                            </button>
                            <button class="btn btn-outline-primary w-100 mb-2" onclick="SMS.generateMonthlyAttendanceReport()">
                                <i class="fas fa-calendar-alt"></i> Monthly Attendance Report (PDF)
                            </button>
                            <button class="btn btn-outline-primary w-100" onclick="SMS.generateAttendanceSummary()">
                                <i class="fas fa-chart-bar"></i> Attendance Summary
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Fee Reports -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-success text-white">
                            <h5><i class="fas fa-money-bill-wave"></i> Fee Reports</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-success w-100 mb-2" onclick="SMS.generateFeeDefaultersReport()">
                                <i class="fas fa-exclamation-triangle"></i> Fee Defaulters (Excel)
                            </button>
                            <button class="btn btn-outline-success w-100 mb-2" onclick="SMS.generateDailyCollectionReport()">
                                <i class="fas fa-coins"></i> Daily Collection Report
                            </button>
                            <button class="btn btn-outline-success w-100" onclick="SMS.generateMonthlyCollectionReport()">
                                <i class="fas fa-chart-line"></i> Monthly Collection Report
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Exam Reports -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-warning text-dark">
                            <h5><i class="fas fa-file-alt"></i> Exam Reports</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-warning w-100 mb-2" onclick="SMS.generateReportCard()">
                                <i class="fas fa-id-card"></i> Student Report Card (PDF)
                            </button>
                            <button class="btn btn-outline-warning w-100 mb-2" onclick="SMS.generateClassPerformanceReport()">
                                <i class="fas fa-users"></i> Class Performance Report
                            </button>
                            <button class="btn btn-outline-warning w-100" onclick="SMS.generateTopPerformersReport()">
                                <i class="fas fa-trophy"></i> Top Performers Report
                            </button>
                        </div>
                    </div>
                </div>

                <!-- Student Reports -->
                <div class="col-md-6 mb-4">
                    <div class="card">
                        <div class="card-header bg-info text-white">
                            <h5><i class="fas fa-user-graduate"></i> Student Reports</h5>
                        </div>
                        <div class="card-body">
                            <button class="btn btn-outline-info w-100 mb-2" onclick="SMS.generateStudentProfile()">
                                <i class="fas fa-user"></i> Student Profile Report
                            </button>
                            <button class="btn btn-outline-info w-100 mb-2" onclick="SMS.generateClassListReport()">
                                <i class="fas fa-list"></i> Class-wise Student List
                            </button>
                            <button class="btn btn-outline-info w-100" onclick="SMS.generateStudentStatistics()">
                                <i class="fas fa-chart-pie"></i> Student Statistics
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        `;
    };

    SMS.generateDailyAttendanceReport = async function() {
        const { value: date } = await Swal.fire({
            title: 'Select Date',
            input: 'date',
            inputValue: new Date().toISOString().split('T')[0],
            showCancelButton: true,
            confirmButtonText: 'Generate Report'
        });

        if (date) {
            try {
                const response = await fetch(SMS.apiUrl(`/attendance/daily-summary?attendance_date=${date}`), {
                    headers: {
                        'Authorization': `Bearer ${SMS.authToken}`
                    }
                });

                if (response.ok) {
                    const data = await response.json();
                    SMS.displayAttendanceReportModal(data, date);
                } else {
                    SMS.showError('Failed to generate report');
                }
            } catch (error) {
                console.error('Error generating report:', error);
                SMS.showError('Error generating report');
            }
        }
    };

    SMS.displayAttendanceReportModal = function(data, date) {
        let html = `
            <div class="text-start">
                <h5>Daily Attendance Report - ${date}</h5>
                <table class="table table-sm table-striped">
                    <thead>
                        <tr>
                            <th>Class</th>
                            <th>Total</th>
                            <th>Present</th>
                            <th>Absent</th>
                            <th>%</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        data.forEach(record => {
            html += `
                <tr>
                    <td>${record.class_name} - ${record.section}</td>
                    <td>${record.total_students}</td>
                    <td><span class="badge bg-success">${record.present_count}</span></td>
                    <td><span class="badge bg-danger">${record.absent_count}</span></td>
                    <td><strong>${record.attendance_percentage}%</strong></td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        Swal.fire({
            title: 'Attendance Report',
            html: html,
            width: '700px',
            showCloseButton: true
        });
    };

    SMS.generateMonthlyAttendanceReport = async function() {
        const result = await Swal.fire({
            title: 'Monthly Attendance Report',
            html: `
                <div class="text-start">
                    <div class="mb-3">
                        <label class="form-label">Select Class</label>
                        <select class="form-control" id="reportClassId">
                            <option value="">Loading...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Year</label>
                        <input type="number" class="form-control" id="reportYear" value="${new Date().getFullYear()}">
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Month</label>
                        <select class="form-control" id="reportMonth">
                            <option value="1">January</option>
                            <option value="2">February</option>
                            <option value="3">March</option>
                            <option value="4">April</option>
                            <option value="5">May</option>
                            <option value="6">June</option>
                            <option value="7">July</option>
                            <option value="8">August</option>
                            <option value="9">September</option>
                            <option value="10">October</option>
                            <option value="11">November</option>
                            <option value="12">December</option>
                        </select>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: 'Download PDF',
            didOpen: async () => {
                const response = await fetch(SMS.apiUrl('/classes'), {
                    headers: { 'Authorization': `Bearer ${SMS.authToken}` }
                });
                if (response.ok) {
                    const classes = await response.json();
                    const select = document.getElementById('reportClassId');
                    select.innerHTML = '<option value="">Select Class</option>' +
                        classes.map(c => `<option value="${c.class_id}">${c.class_name} - ${c.section}</option>`).join('');
                }
            },
            preConfirm: () => {
                return {
                    classId: document.getElementById('reportClassId').value,
                    year: document.getElementById('reportYear').value,
                    month: document.getElementById('reportMonth').value
                };
            }
        });

        if (result.isConfirmed && result.value.classId) {
            const url = `${SMS.API_BASE}/reports/attendance/monthly-pdf?class_id=${result.value.classId}&year=${result.value.year}&month=${result.value.month}`;
            window.open(url, '_blank');
            SMS.showSuccess('Report is being generated!');
        }
    };

    SMS.generateAttendanceSummary = async function() {
        SMS.showError('Attendance summary report coming soon!');
    };

    SMS.generateFeeDefaultersReport = async function() {
        const { value: year } = await Swal.fire({
            title: 'Fee Defaulters Report',
            input: 'number',
            inputLabel: 'Academic Year',
            inputValue: new Date().getFullYear(),
            showCancelButton: true,
            confirmButtonText: 'Download Excel'
        });

        if (year) {
            const url = `${SMS.API_BASE}/reports/fees/defaulters-excel?academic_year=${year}`;
            window.open(url, '_blank');
            SMS.showSuccess('Excel report is being generated!');
        }
    };

    SMS.generateDailyCollectionReport = async function() {
        SMS.showError('Daily collection report coming soon!');
    };

    SMS.generateMonthlyCollectionReport = async function() {
        SMS.showError('Monthly collection report coming soon!');
    };

    SMS.generateReportCard = async function() {
        SMS.showError('Report card generation coming soon!');
    };

    SMS.generateClassPerformanceReport = async function() {
        SMS.showError('Class performance report coming soon!');
    };

    SMS.generateTopPerformersReport = async function() {
        SMS.showError('Top performers report coming soon!');
    };

    SMS.generateStudentProfile = async function() {
        SMS.showError('Student profile report coming soon!');
    };

    SMS.generateClassListReport = async function() {
        SMS.showError('Class list report coming soon!');
    };

    SMS.generateStudentStatistics = async function() {
        SMS.showError('Student statistics report coming soon!');
    };

    // Backwards-compatible aliases
    window.loadReportsPage = function(){ return SMS.loadReportsPage(); };
    window.generateDailyAttendanceReport = function(){ return SMS.generateDailyAttendanceReport(); };
    window.generateMonthlyAttendanceReport = function(){ return SMS.generateMonthlyAttendanceReport(); };
    window.generateFeeDefaultersReport = function(){ return SMS.generateFeeDefaultersReport(); };

})(window);

