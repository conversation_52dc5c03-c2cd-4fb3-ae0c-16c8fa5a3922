@echo off
REM School Management System - Server Start Script (Windows Batch)
REM This script starts the SMS backend server

echo === School Management System - Server Start ===
echo Starting SMS Backend Server...

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Python is not installed or not in PATH
    echo Please install Python 3.8+ and try again
    pause
    exit /b 1
)

REM Check if virtual environment exists
if not exist "venv" (
    echo Virtual environment not found. Creating...
    python -m venv venv
    echo Virtual environment created.
)

REM Activate virtual environment
echo Activating virtual environment...
call venv\Scripts\activate.bat

REM Install requirements if needed
if exist "requirements.txt" (
    echo Installing/updating requirements...
    pip install -r requirements.txt --quiet
)

REM Kill any existing server processes
echo Stopping any existing server processes...
taskkill /f /im python.exe >nul 2>&1

REM Change to backend directory
cd backend

REM Start the server
echo Starting SMS Backend Server on http://127.0.0.1:8000
echo Press Ctrl+C to stop the server
echo ========================================

python main.py

pause
