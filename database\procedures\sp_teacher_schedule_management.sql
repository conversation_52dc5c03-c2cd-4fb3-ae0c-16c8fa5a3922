-- Teacher Schedule Management Stored Procedures
USE SchoolManagementDB;
GO

-- =============================================
-- Get All Teacher Schedules
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAllSchedules', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAllSchedules;
GO
CREATE PROCEDURE sp_Teacher_GetAllSchedules
    @academic_year INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    IF @academic_year IS NULL
        SELECT @academic_year = MAX(academic_year) FROM classes;
    
    SELECT DISTINCT u.user_id AS teacher_id, u.full_name AS teacher_name, s.designation
    FROM users u
    LEFT JOIN staff s ON u.user_id = s.user_id
    WHERE u.role_id IN (SELECT role_id FROM roles WHERE role_name LIKE '%Teacher%' OR role_name = 'Staff')
      AND u.is_active = 1
    ORDER BY u.full_name;
END
GO

-- =============================================
-- Get Teacher Schedule by ID
-- =============================================
IF OBJECT_ID('sp_Teacher_GetSchedule', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetSchedule;
GO
CREATE PROCEDURE sp_Teacher_GetSchedule
    @teacher_id INT,
    @academic_year INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    IF @academic_year IS NULL
        SELECT @academic_year = MAX(academic_year) FROM classes;
    
    SELECT u.user_id, u.full_name, s.designation
    FROM users u
    LEFT JOIN staff s ON u.user_id = s.user_id
    WHERE u.user_id = @teacher_id;
END
GO

-- =============================================
-- Get Teacher Periods
-- =============================================
IF OBJECT_ID('sp_Teacher_GetPeriods', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetPeriods;
GO
CREATE PROCEDURE sp_Teacher_GetPeriods
    @teacher_id INT,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT ct.timetable_id, ct.class_id, c.class_name, c.section,
           ct.subject_id, sub.subject_name, ct.day_of_week, ct.period_number,
           pt.start_time, pt.end_time
    FROM class_timetables ct
    INNER JOIN classes c ON ct.class_id = c.class_id
    INNER JOIN subjects sub ON ct.subject_id = sub.subject_id
    INNER JOIN period_templates pt ON ct.period_number = pt.period_number
    WHERE ct.teacher_id = @teacher_id
      AND c.academic_year = @academic_year
    ORDER BY ct.day_of_week, ct.period_number;
END
GO

-- =============================================
-- Check Period Conflict for Teacher
-- =============================================
IF OBJECT_ID('sp_Teacher_CheckPeriodConflict', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_CheckPeriodConflict;
GO
CREATE PROCEDURE sp_Teacher_CheckPeriodConflict
    @teacher_id INT,
    @day_of_week INT,
    @period_number INT,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT COUNT(*) AS conflict_count
    FROM class_timetables ct
    INNER JOIN classes c ON ct.class_id = c.class_id
    WHERE ct.teacher_id = @teacher_id
      AND ct.day_of_week = @day_of_week
      AND ct.period_number = @period_number
      AND c.academic_year = @academic_year;
END
GO

-- =============================================
-- Create Teacher Period
-- =============================================
IF OBJECT_ID('sp_Teacher_CreatePeriod', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_CreatePeriod;
GO
CREATE PROCEDURE sp_Teacher_CreatePeriod
    @teacher_id INT,
    @class_id INT,
    @subject_id INT,
    @day_of_week INT,
    @period_number INT
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO class_timetables (class_id, subject_id, teacher_id, day_of_week, period_number)
    VALUES (@class_id, @subject_id, @teacher_id, @day_of_week, @period_number);
    
    SELECT 'Success' AS Result, 'Period created successfully' AS Message, SCOPE_IDENTITY() AS PeriodId;
END
GO

-- =============================================
-- Delete Teacher Period
-- =============================================
IF OBJECT_ID('sp_Teacher_DeletePeriod', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_DeletePeriod;
GO
CREATE PROCEDURE sp_Teacher_DeletePeriod
    @period_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DELETE FROM class_timetables WHERE timetable_id = @period_id;
    
    SELECT 'Success' AS Result, 'Period deleted successfully' AS Message;
END
GO
