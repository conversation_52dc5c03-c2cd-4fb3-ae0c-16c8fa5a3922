2025-12-10 12:17:29,890 - main - INFO - Starting School Management System v1.0.0
2025-12-10 12:17:30,015 - main - INFO - Database connection successful
2025-12-10 12:34:07,823 - main - INFO - [REQUEST] GET /
2025-12-10 12:34:07,823 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:07,833 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:08,456 - main - INFO - [REQUEST] GET /static/js/app.js
2025-12-10 12:34:08,457 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:08,522 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:08,523 - main - INFO - [REQUEST] GET /static/js/teacher_pages.js
2025-12-10 12:34:08,524 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:08,526 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:08,529 - main - INFO - [REQUEST] GET /static/js/timetable.js
2025-12-10 12:34:08,530 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:08,532 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:08,568 - main - INFO - [REQUEST] GET /static/js/timetable_grid.js
2025-12-10 12:34:08,569 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:08,579 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:08,916 - main - INFO - [REQUEST] GET /static/js/module-loader.js
2025-12-10 12:34:08,917 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:08,921 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,247 - main - INFO - [REQUEST] GET /static/js/core.module.js
2025-12-10 12:34:09,248 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,250 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,285 - main - INFO - [REQUEST] GET /static/js/api.js
2025-12-10 12:34:09,285 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,287 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,291 - main - INFO - [REQUEST] GET /static/js/auth.js
2025-12-10 12:34:09,292 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,293 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,302 - main - INFO - [REQUEST] GET /api/auth/me
2025-12-10 12:34:09,302 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 12:34:09,338 - auth - ERROR - JWT decode error: Signature has expired.
2025-12-10 12:34:09,339 - main - INFO - [REQUEST] GET /static/js/router.js
2025-12-10 12:34:09,340 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,340 - main - INFO - [RESPONSE] Status: 401
2025-12-10 12:34:09,343 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,367 - main - INFO - [REQUEST] GET /static/js/dashboard.js
2025-12-10 12:34:09,367 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,369 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,375 - main - INFO - [REQUEST] GET /static/js/users.js
2025-12-10 12:34:09,375 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,377 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,385 - main - INFO - [REQUEST] GET /static/js/classes.js
2025-12-10 12:34:09,385 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,386 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,393 - main - INFO - [REQUEST] GET /static/js/students.module.js
2025-12-10 12:34:09,393 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,395 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,424 - main - INFO - [REQUEST] GET /static/js/attendance.module.js
2025-12-10 12:34:09,424 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,425 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,431 - main - INFO - [REQUEST] GET /static/js/fees.module.js
2025-12-10 12:34:09,431 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,434 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,440 - main - INFO - [REQUEST] GET /static/js/exams.js
2025-12-10 12:34:09,440 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,442 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,474 - main - INFO - [REQUEST] GET /static/js/reports.js
2025-12-10 12:34:09,474 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,476 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,504 - main - INFO - [REQUEST] GET /static/js/subjects.js
2025-12-10 12:34:09,505 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,506 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,534 - main - INFO - [REQUEST] GET /static/js/staff.js
2025-12-10 12:34:09,534 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,536 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,567 - main - INFO - [REQUEST] GET /static/js/holidays.js
2025-12-10 12:34:09,567 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,569 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,596 - main - INFO - [REQUEST] GET /static/js/settings.js
2025-12-10 12:34:09,596 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,598 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,626 - main - INFO - [REQUEST] GET /static/js/student-modals.js
2025-12-10 12:34:09,626 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,628 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,656 - main - INFO - [REQUEST] GET /static/js/attendance-functions.js
2025-12-10 12:34:09,656 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,658 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,684 - main - INFO - [REQUEST] GET /static/js/attendance-functions-2.js
2025-12-10 12:34:09,685 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,686 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,711 - main - INFO - [REQUEST] GET /static/js/attendance-functions-3.js
2025-12-10 12:34:09,711 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,713 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,741 - main - INFO - [REQUEST] GET /static/js/attendance-functions-4.js
2025-12-10 12:34:09,741 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,743 - main - INFO - [RESPONSE] Status: 200
2025-12-10 12:34:09,774 - main - INFO - [REQUEST] GET /static/js/fee-functions.js
2025-12-10 12:34:09,775 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,777 - main - INFO - [RESPONSE] Status: 404
2025-12-10 12:34:09,780 - main - INFO - [REQUEST] GET /static/js/report-functions.js
2025-12-10 12:34:09,780 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,784 - main - INFO - [RESPONSE] Status: 404
2025-12-10 12:34:09,788 - main - INFO - [REQUEST] GET /static/js/subject-functions.js
2025-12-10 12:34:09,789 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,793 - main - INFO - [RESPONSE] Status: 404
2025-12-10 12:34:09,799 - main - INFO - [REQUEST] GET /static/js/staff-functions.js
2025-12-10 12:34:09,799 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,801 - main - INFO - [RESPONSE] Status: 404
2025-12-10 12:34:09,804 - main - INFO - [REQUEST] GET /static/js/holiday-functions.js
2025-12-10 12:34:09,804 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,806 - main - INFO - [RESPONSE] Status: 404
2025-12-10 12:34:09,810 - main - INFO - [REQUEST] GET /static/js/teacher-functions.js
2025-12-10 12:34:09,810 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,812 - main - INFO - [RESPONSE] Status: 404
2025-12-10 12:34:09,818 - main - INFO - [REQUEST] GET /static/js/monthly-overview.js
2025-12-10 12:34:09,819 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,821 - main - INFO - [RESPONSE] Status: 404
2025-12-10 12:34:09,824 - main - INFO - [REQUEST] GET /static/js/sms-namespace.js
2025-12-10 12:34:09,825 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,826 - main - INFO - [RESPONSE] Status: 404
2025-12-10 12:34:09,944 - main - INFO - [REQUEST] GET /favicon.ico
2025-12-10 12:34:09,945 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 12:34:09,946 - main - INFO - [RESPONSE] Status: 404
2025-12-10 13:05:42,949 - main - INFO - [REQUEST] POST /api/auth/login
2025-12-10 13:05:42,950 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 13:05:43,288 - main - INFO - [RESPONSE] Status: 401
2025-12-10 13:05:51,870 - main - INFO - [REQUEST] POST /api/auth/login
2025-12-10 13:05:51,870 - main - INFO - [HEADERS] Authorization: None...
2025-12-10 13:05:52,159 - main - INFO - [RESPONSE] Status: 200
2025-12-10 13:05:52,170 - main - INFO - [REQUEST] GET /api/auth/me
2025-12-10 13:05:52,170 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:05:52,175 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:05:52,181 - main - INFO - [RESPONSE] Status: 200
2025-12-10 13:05:52,189 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-10 13:05:52,190 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:05:52,191 - main - INFO - [RESPONSE] Status: 404
2025-12-10 13:05:52,206 - main - INFO - [REQUEST] GET /api/students
2025-12-10 13:05:52,206 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:05:52,207 - main - INFO - [RESPONSE] Status: 404
2025-12-10 13:05:52,210 - main - INFO - [REQUEST] GET /api/users/
2025-12-10 13:05:52,211 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:05:52,214 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:05:52,234 - main - INFO - [RESPONSE] Status: 200
2025-12-10 13:05:52,236 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-10 13:05:52,237 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:05:52,239 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:05:52,242 - main - INFO - [RESPONSE] Status: 422
2025-12-10 13:05:52,244 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-10 13:05:52,245 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:05:52,246 - main - INFO - [RESPONSE] Status: 404
2025-12-10 13:05:57,392 - main - INFO - [REQUEST] GET /api/users/
2025-12-10 13:05:57,392 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:05:57,412 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:05:57,423 - main - INFO - [RESPONSE] Status: 200
2025-12-10 13:05:59,336 - main - INFO - [REQUEST] GET /api/staff/
2025-12-10 13:05:59,336 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:05:59,351 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:05:59,380 - main - INFO - [RESPONSE] Status: 200
2025-12-10 13:06:00,826 - main - INFO - [REQUEST] GET /api/dashboard/stats/
2025-12-10 13:06:00,826 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:00,827 - main - INFO - [RESPONSE] Status: 404
2025-12-10 13:06:00,837 - main - INFO - [REQUEST] GET /api/students
2025-12-10 13:06:00,837 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:00,839 - main - INFO - [REQUEST] GET /api/attendance/daily-summary
2025-12-10 13:06:00,839 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:00,840 - main - INFO - [REQUEST] GET /api/users/
2025-12-10 13:06:00,840 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:00,860 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:06:00,863 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:06:00,871 - main - INFO - [RESPONSE] Status: 404
2025-12-10 13:06:00,872 - main - INFO - [RESPONSE] Status: 422
2025-12-10 13:06:00,874 - main - INFO - [RESPONSE] Status: 200
2025-12-10 13:06:00,875 - main - INFO - [REQUEST] GET /api/fees/defaulters
2025-12-10 13:06:00,875 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:00,876 - main - INFO - [RESPONSE] Status: 404
2025-12-10 13:06:02,087 - main - INFO - [REQUEST] GET /api/timetable/config/
2025-12-10 13:06:02,087 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:02,088 - main - INFO - [RESPONSE] Status: 404
2025-12-10 13:06:02,105 - main - INFO - [REQUEST] GET /api/timetable/periods/templates
2025-12-10 13:06:02,106 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:02,109 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:06:02,110 - main - INFO - [RESPONSE] Status: 422
2025-12-10 13:06:04,544 - main - INFO - [REQUEST] GET /api/classes/
2025-12-10 13:06:04,544 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:04,550 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:06:04,558 - database - ERROR - Database operation error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-10 13:06:04,559 - database - ERROR - Database connection error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-10 13:06:04,560 - routes.class_routes - WARNING - Stored procedure failed, using fallback query: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-10 13:06:04,576 - main - INFO - [REQUEST] GET /api/staff/
2025-12-10 13:06:04,576 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:04,577 - main - INFO - [REQUEST] GET /api/subjects/
2025-12-10 13:06:04,577 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:04,580 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:06:04,601 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:06:04,608 - main - INFO - [RESPONSE] Status: 200
2025-12-10 13:06:04,609 - main - INFO - [RESPONSE] Status: 200
2025-12-10 13:06:04,610 - main - INFO - [RESPONSE] Status: 200
2025-12-10 13:06:09,270 - main - INFO - [REQUEST] GET /api/timetable/classes/3
2025-12-10 13:06:09,271 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:09,277 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:06:09,279 - main - INFO - [REQUEST] GET /api/timetable/periods/templates
2025-12-10 13:06:09,279 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:09,283 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:06:09,285 - main - INFO - [RESPONSE] Status: 422
2025-12-10 13:06:09,286 - main - INFO - [RESPONSE] Status: 422
2025-12-10 13:06:11,133 - main - INFO - [REQUEST] GET /api/staff/
2025-12-10 13:06:11,134 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:11,137 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:06:11,141 - main - INFO - [RESPONSE] Status: 200
2025-12-10 13:06:11,148 - main - INFO - [REQUEST] GET /api/teachers/schedules/
2025-12-10 13:06:11,149 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:11,150 - main - INFO - [RESPONSE] Status: 404
2025-12-10 13:06:12,323 - main - INFO - [REQUEST] GET /api/teachers/attendance/summary
2025-12-10 13:06:12,323 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:12,326 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:06:12,327 - main - INFO - [RESPONSE] Status: 422
2025-12-10 13:06:15,721 - main - INFO - [REQUEST] GET /api/classes/
2025-12-10 13:06:15,722 - main - INFO - [HEADERS] Authorization: Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJ1c2...
2025-12-10 13:06:15,726 - auth - INFO - User authenticated: admin - Role: Administrator
2025-12-10 13:06:15,728 - database - ERROR - Database operation error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-10 13:06:15,729 - database - ERROR - Database connection error: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-10 13:06:15,730 - routes.class_routes - WARNING - Stored procedure failed, using fallback query: ('42000', "[42000] [Microsoft][ODBC Driver 17 for SQL Server][SQL Server]Could not find stored procedure 'sp_GetAllClasses'. (2812) (SQLExecDirectW)")
2025-12-10 13:06:15,733 - main - INFO - [RESPONSE] Status: 200
