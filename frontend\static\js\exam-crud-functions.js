// Exam CRUD Functions Module
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    // =============================================
    // EXAM CRUD OPERATIONS
    // =============================================

    async function showAddExamModal() {
        const { value: formValues} = await Swal.fire({
            title: 'Create New Exam',
            html: `
                <form id="addExamForm" class="text-start">
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Exam Name *</label>
                            <input type="text" class="form-control" id="examName" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Exam Type *</label>
                            <select class="form-control" id="examType" required>
                                <option value="">Select Type</option>
                                <option value="Monthly">Monthly</option>
                                <option value="Mid-Term">Mid-Term</option>
                                <option value="Final">Final</option>
                                <option value="Quiz">Quiz</option>
                            </select>
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Academic Year *</label>
                            <input type="number" class="form-control" id="academicYear" value="${new Date().getFullYear()}" min="2020" max="2030" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Total Marks</label>
                            <input type="number" class="form-control" id="totalMarks" value="100" min="1">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Start Date *</label>
                            <input type="date" class="form-control" id="startDate" required>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">End Date *</label>
                            <input type="date" class="form-control" id="endDate" required>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Description</label>
                        <textarea class="form-control" id="examDescription" rows="3"></textarea>
                    </div>
                    <div class="form-check">
                        <input class="form-check-input" type="checkbox" id="isActive" checked>
                        <label class="form-check-label" for="isActive">
                            Active Exam
                        </label>
                    </div>
                </form>
            `,
            showCancelButton: true,
            confirmButtonText: 'Create Exam',
            cancelButtonText: 'Cancel',
            width: '600px',
            preConfirm: () => {
                const examName = document.getElementById('examName').value;
                const examType = document.getElementById('examType').value;
                const academicYear = document.getElementById('academicYear').value;
                const totalMarks = document.getElementById('totalMarks').value;
                const startDate = document.getElementById('startDate').value;
                const endDate = document.getElementById('endDate').value;
                const description = document.getElementById('examDescription').value;
                const isActive = document.getElementById('isActive').checked;

                if (!examName || !examType || !academicYear || !startDate || !endDate) {
                    Swal.showValidationMessage('Please fill in all required fields');
                    return false;
                }

                if (new Date(startDate) > new Date(endDate)) {
                    Swal.showValidationMessage('Start date cannot be after end date');
                    return false;
                }

                return {
                    exam_name: examName,
                    exam_type: examType,
                    academic_year: parseInt(academicYear),
                    total_marks: parseInt(totalMarks) || 100,
                    start_date: startDate,
                    end_date: endDate,
                    description: description,
                    is_active: isActive
                };
            }
        });

        if (formValues) {
            await createExam(formValues);
        }
    }

    async function createExam(examData) {
        try {
            const response = await fetch(SMS.apiUrl('/exams'), {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${SMS.authToken}`
                },
                body: JSON.stringify(examData)
            });

            const result = await response.json();
            
            if (response.ok) {
                SMS.showSuccess('Exam created successfully!');
                loadExams(); // Reload the exams table
            } else {
                SMS.showError(result.detail || 'Failed to create exam');
            }
        } catch (error) {
            console.error('Error creating exam:', error);
            SMS.showError('Error creating exam');
        }
    }

    async function editExam(examId) {
        try {
            const response = await fetch(SMS.apiUrl(`/exams/`), {
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`
                }
            });
            
            if (!response.ok) {
                SMS.showError('Failed to load exam details');
                return;
            }
            
            const exams = await response.json();
            const exam = exams.find(e => e.exam_id === examId);
            
            if (!exam) {
                SMS.showError('Exam not found');
                return;
            }

            // Show edit modal with pre-filled data
            const { value: formValues} = await Swal.fire({
                title: 'Edit Exam',
                html: `
                    <form id="editExamForm" class="text-start">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Exam Name *</label>
                                <input type="text" class="form-control" id="examName" value="${exam.exam_name}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Exam Type *</label>
                                <select class="form-control" id="examType" required>
                                    <option value="">Select Type</option>
                                    <option value="Monthly" ${exam.exam_type === 'Monthly' ? 'selected' : ''}>Monthly</option>
                                    <option value="Mid-Term" ${exam.exam_type === 'Mid-Term' ? 'selected' : ''}>Mid-Term</option>
                                    <option value="Final" ${exam.exam_type === 'Final' ? 'selected' : ''}>Final</option>
                                    <option value="Quiz" ${exam.exam_type === 'Quiz' ? 'selected' : ''}>Quiz</option>
                                </select>
                            </div>
                        </div>
                        <!-- Additional form fields would go here -->
                    </form>
                `,
                showCancelButton: true,
                confirmButtonText: 'Update Exam',
                cancelButtonText: 'Cancel',
                width: '600px'
            });

            if (formValues) {
                // Handle exam update
                SMS.showInfo('Edit functionality will be implemented');
            }
        } catch (error) {
            console.error('Error editing exam:', error);
            SMS.showError('Error loading exam details');
        }
    }

    // Export functions to global scope for backward compatibility
    window.showAddExamModal = showAddExamModal;
    window.createExam = createExam;
    window.editExam = editExam;

})(window);
