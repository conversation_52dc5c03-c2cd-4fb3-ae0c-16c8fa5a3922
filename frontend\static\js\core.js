// Core utilities and initialization for SMS frontend (module)
(function(window){
    window.SMS = window.SMS || {};

    // Global state
    SMS.authToken = null;
    SMS.currentUser = null;
    SMS.API_BASE = '/api';

    SMS.showLoading = function(message = 'Loading...') {
        const contentArea = document.getElementById('contentArea');
        if (contentArea) {
            contentArea.innerHTML = `
                <div class="d-flex justify-content-center align-items-center" style="min-height: 400px;">
                    <div class="text-center">
                        <div class="spinner-border text-primary" role="status" style="width: 3rem; height: 3rem;">
                            <span class="visually-hidden">Loading...</span>
                        </div>
                        <p class="mt-3 text-muted">${message}</p>
                    </div>
                </div>
            `;
        }
    };

    SMS.showLoadingInline = function(containerId, message = 'Loading...') {
        const container = document.getElementById(containerId);
        if (container) {
            container.innerHTML = `
                <div class="text-center py-4">
                    <div class="spinner-border text-primary" role="status">
                        <span class="visually-hidden">Loading...</span>
                    </div>
                    <p class="mt-2 text-muted">${message}</p>
                </div>
            `;
        }
    };

    SMS.formatDate = function(dateString) {
        if (!dateString) return '';
        const date = new Date(dateString);
        if (isNaN(date)) return '';
        return date.toLocaleDateString('en-US', {
            year: 'numeric', month: 'short', day: 'numeric'
        });
    };

    SMS.formatCurrency = function(amount) {
        if (amount === null || amount === undefined) return 'Rs. 0.00';
        return 'Rs. ' + parseFloat(amount).toLocaleString('en-US', {
            minimumFractionDigits: 2, maximumFractionDigits: 2
        });
    };

    SMS.showSuccess = function(message) {
        if (window.Swal) {
            Swal.fire({ icon: 'success', title: 'Success!', text: message, timer: 2000, showConfirmButton: false });
        } else {
            console.log('SUCCESS:', message);
            alert(message);
        }
    };

    SMS.showError = function(message, details = null) {
        if (window.Swal) {
            const htmlContent = details ? (`<p>${message}</p><div class="alert alert-light text-start mt-3"><small><strong>Details:</strong> ${details}</small></div>`) : message;
            Swal.fire({ icon: 'error', title: 'Error', html: htmlContent });
        } else {
            console.error('ERROR:', message, details || '');
            alert(message + (details ? '\n\n' + details : ''));
        }
    };

    // Backwards-compatible globals
    window.showSuccess = function(msg){ SMS.showSuccess(msg); };
    window.showError = function(msg, details){ SMS.showError(msg, details); };

    document.addEventListener('DOMContentLoaded', function() {
        SMS.authToken = localStorage.getItem('authToken');
        if (SMS.authToken && typeof window.loadUserProfile === 'function') {
            window.loadUserProfile();
        }
        if (typeof window.setupEventListeners === 'function') setupEventListeners();
    });

})(window);
