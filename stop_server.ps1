#!/usr/bin/env pwsh
# School Management System - Server Stop Script
# This script stops all SMS backend server processes

Write-Host "=== School Management System - Server Stop ===" -ForegroundColor Red
Write-Host "Stopping SMS Backend Server..." -ForegroundColor Yellow

# Find and kill all Python processes running main.py
$processes = Get-Process -Name "python" -ErrorAction SilentlyContinue | Where-Object { 
    $_.CommandLine -like "*main.py*" -or 
    $_.CommandLine -like "*uvicorn*" -or
    $_.CommandLine -like "*fastapi*"
}

if ($processes) {
    Write-Host "Found $($processes.Count) server process(es) to stop..." -ForegroundColor Yellow
    foreach ($process in $processes) {
        try {
            Write-Host "Stopping process ID: $($process.Id)" -ForegroundColor Yellow
            Stop-Process -Id $process.Id -Force
            Write-Host "Process $($process.Id) stopped successfully" -ForegroundColor Green
        } catch {
            Write-Host "Failed to stop process $($process.Id): $_" -ForegroundColor Red
        }
    }
} else {
    Write-Host "No SMS server processes found running" -ForegroundColor Yellow
}

# Also kill any processes using port 8000
Write-Host "Checking for processes using port 8000..." -ForegroundColor Yellow
try {
    $portProcesses = netstat -ano | Select-String ":8000" | ForEach-Object {
        $parts = $_.ToString().Split(' ', [StringSplitOptions]::RemoveEmptyEntries)
        if ($parts.Length -gt 4) {
            $parts[-1]
        }
    } | Sort-Object -Unique

    foreach ($pid in $portProcesses) {
        if ($pid -and $pid -ne "0") {
            try {
                $process = Get-Process -Id $pid -ErrorAction SilentlyContinue
                if ($process) {
                    Write-Host "Stopping process using port 8000: PID $pid" -ForegroundColor Yellow
                    Stop-Process -Id $pid -Force
                    Write-Host "Process $pid stopped successfully" -ForegroundColor Green
                }
            } catch {
                Write-Host "Failed to stop process $pid: $_" -ForegroundColor Red
            }
        }
    }
} catch {
    Write-Host "Could not check port usage: $_" -ForegroundColor Yellow
}

Write-Host "SMS Backend Server stopped" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
