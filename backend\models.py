"""
Pydantic models for request/response validation
"""
from pydantic import BaseModel, EmailStr, Field, validator
from typing import Optional, List, Dict
from datetime import date, datetime
from decimal import Decimal


# =============================================
# Authentication Models
# =============================================
class LoginRequest(BaseModel):
    username: str
    password: str


class TokenResponse(BaseModel):
    access_token: str
    refresh_token: str
    token_type: str = "bearer"
    user_id: int
    username: str
    role: str


class UserCreate(BaseModel):
    username: str = Field(..., min_length=3, max_length=50)
    password: str = Field(..., min_length=8)
    full_name: str
    full_name_urdu: Optional[str] = None
    email: Optional[EmailStr] = None
    phone: Optional[str] = None
    cnic: Optional[str] = None
    role_id: int


class UserResponse(BaseModel):
    user_id: int
    username: str
    full_name: str
    full_name_urdu: Optional[str]
    email: Optional[str]
    phone: Optional[str]
    role_id: int
    role_name: Optional[str]
    is_active: bool
    created_at: datetime


# =============================================
# Student Models
# =============================================
class StudentCreate(BaseModel):
    admission_number: str
    full_name: str
    full_name_urdu: Optional[str] = None
    father_name: str
    father_name_urdu: Optional[str] = None
    date_of_birth: date
    gender: str
    cnic: Optional[str] = None
    b_form: Optional[str] = None
    class_id: int
    parent_phone: str
    parent_email: Optional[EmailStr] = None
    address: Optional[str] = None
    address_urdu: Optional[str] = None


class StudentUpdate(BaseModel):
    full_name: Optional[str] = None
    full_name_urdu: Optional[str] = None
    father_name: Optional[str] = None
    class_id: Optional[int] = None
    parent_phone: Optional[str] = None
    parent_email: Optional[EmailStr] = None
    address: Optional[str] = None
    status: Optional[str] = None


class StudentResponse(BaseModel):
    student_id: int
    admission_number: str
    full_name: str
    full_name_urdu: Optional[str]
    father_name: str
    date_of_birth: date
    gender: str
    class_id: Optional[int]
    class_name: Optional[str]
    section: Optional[str]
    status: str
    parent_phone: Optional[str]
    parent_email: Optional[str]
    is_active: bool


# =============================================
# Attendance Models
# =============================================
class AttendanceMarkRequest(BaseModel):
    student_id: int
    class_id: int
    attendance_date: date
    status: str  # Present, Absent, Leave, Late, Half-Day, Short Leave, Holiday, Sick Leave, Casual Leave
    remarks: Optional[str] = None
    attendance_type: Optional[str] = "Daily Class Attendance"  # Daily, Special, Subject-wise
    period: Optional[str] = None  # Morning, Afternoon, Evening
    subject_id: Optional[int] = None  # For subject-wise attendance
    arrival_time: Optional[str] = None  # For late arrivals
    departure_time: Optional[str] = None  # For half-day
    leave_type: Optional[str] = None  # Sick Leave, Casual Leave, etc.
    medical_certificate: Optional[str] = None  # File path for medical certificate


class AttendanceBulkMarkRequest(BaseModel):
    class_id: int
    attendance_date: date
    attendance_records: List[dict]  # [{student_id, status, remarks, ...}]
    attendance_type: Optional[str] = "Daily Class Attendance"
    period: Optional[str] = "Morning"
    subject_id: Optional[int] = None


class AttendanceEditRequest(BaseModel):
    """Request to edit existing attendance record"""
    attendance_id: int
    status: str
    remarks: Optional[str] = None
    edit_reason: str  # Required for audit trail
    arrival_time: Optional[str] = None
    departure_time: Optional[str] = None
    leave_type: Optional[str] = None


class AttendanceResponse(BaseModel):
    attendance_id: int
    student_id: int
    student_name: str
    class_id: int
    attendance_date: date
    status: str
    remarks: Optional[str]
    marked_by: int
    attendance_type: Optional[str] = None
    period: Optional[str] = None
    arrival_time: Optional[str] = None
    is_edited: Optional[bool] = False
    edited_at: Optional[str] = None


class AttendanceSummaryResponse(BaseModel):
    class_name: str
    section: str
    total_students: int
    present_count: int
    absent_count: int
    leave_count: int
    late_count: int
    half_day_count: int
    attendance_percentage: Decimal


class StudentAttendanceReport(BaseModel):
    """Individual student attendance report"""
    student_id: int
    student_name: str
    class_name: str
    total_days: int
    present_days: int
    absent_days: int
    late_days: int
    half_days: int
    leave_days: int
    attendance_percentage: Decimal
    trend: str  # Improving, Declining, Stable
    status: str  # Good, Warning, Poor


class ClassAttendanceSummary(BaseModel):
    """Class-wise attendance summary"""
    class_id: int
    class_name: str
    month: int
    year: int
    total_students: int
    average_attendance: Decimal
    best_attendance_student: Optional[str] = None
    worst_attendance_student: Optional[str] = None
    total_working_days: int


class AttendanceSettings(BaseModel):
    """Attendance settings for a class"""
    class_id: int
    academic_year: int
    working_days_per_week: int = 5
    min_attendance_percentage: Decimal = Decimal("75.00")
    grace_days: int = 3
    auto_mark_holidays: bool = True
    notification_enabled: bool = True


# =============================================
# Exam Subject Mapping Models
# =============================================
class ExamSubjectCreate(BaseModel):
    exam_id: int
    subject_id: int
    class_id: Optional[int] = None  # NULL means all classes
    max_marks: Decimal = Decimal('100')
    passing_marks: Decimal = Decimal('40')
    theory_max: Optional[Decimal] = None
    practical_max: Optional[Decimal] = None
    oral_max: Optional[Decimal] = None
    assignment_max: Optional[Decimal] = None


class ExamMarksEntry(BaseModel):
    exam_id: int
    student_id: int
    subject_id: int
    obtained_marks: Decimal
    total_marks: Decimal
    theory_marks: Optional[Decimal] = None
    practical_marks: Optional[Decimal] = None
    oral_marks: Optional[Decimal] = None
    assignment_marks: Optional[Decimal] = None
    remarks: Optional[str] = None


class StudentExamSummary(BaseModel):
    summary_id: int
    exam_id: int
    student_id: int
    student_name: str
    class_id: int
    total_obtained_marks: Decimal
    total_max_marks: Decimal
    percentage: Decimal
    grade: str
    result_status: str  # Pass, Fail, Pending
    class_rank: Optional[int] = None
    section_rank: Optional[int] = None


# =============================================
# Fee Models
# =============================================
class ClassFeeStructureCreate(BaseModel):
    class_id: int
    academic_year: int
    monthly_fee: Decimal
    admission_fee: Decimal = Decimal('0')
    annual_fee: Decimal = Decimal('0')
    exam_fee: Decimal = Decimal('0')
    transport_fee: Decimal = Decimal('0')
    other_fee: Decimal = Decimal('0')


class StudentDiscountCreate(BaseModel):
    student_id: int
    academic_year: int
    discount_type: str  # Scholarship, Sibling Discount, Merit, Financial Aid
    discount_percentage: Decimal = Decimal('0')
    discount_amount: Decimal = Decimal('0')
    apply_to: str = 'Monthly Fee'  # Monthly Fee, Annual Fee, All Fees
    remarks: Optional[str] = None


class FeeCollectionRequest(BaseModel):
    student_id: int
    academic_year: int
    amount: Decimal
    payment_method: str  # Cash, Bank, Online, Cheque
    reference_number: Optional[str] = None
    description: Optional[str] = None


class FeeStructureCreate(BaseModel):
    class_id: int
    academic_year: int
    fee_type: str
    fee_type_urdu: Optional[str] = None
    amount: Decimal
    due_day: int = 10
    fine_per_day: Decimal = Decimal('0')


class FeeTransactionResponse(BaseModel):
    transaction_id: int
    student_id: int
    student_name: str
    transaction_date: date
    fee_type: str
    amount: Decimal
    fine_amount: Decimal
    total_amount: Decimal
    payment_method: str
    receipt_number: str


class FeeAccountResponse(BaseModel):
    fee_account_id: int
    student_id: int
    student_name: str
    academic_year: int
    total_fee: Decimal
    total_paid: Decimal
    total_arrears: Decimal
    total_fines: Decimal
    balance: Decimal
    last_payment_date: Optional[date]


# =============================================
# Exam Models
# =============================================
class ExamCreate(BaseModel):
    exam_name: str
    exam_name_urdu: Optional[str] = None
    exam_type: str  # Monthly, Mid-Term, Final, Quiz, Practical
    term_type: Optional[str] = None  # First Term, Second Term, Final Term
    academic_year: int
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    total_marks: Optional[int] = 100
    passing_marks: Optional[int] = 40
    weightage: Optional[Decimal] = None  # Percentage weightage in final result
    is_active: Optional[bool] = True


class ExamSubjectConfig(BaseModel):
    """Configuration for subject-specific exam settings"""
    exam_id: int
    subject_id: int
    theory_marks: Optional[int] = None
    practical_marks: Optional[int] = None
    oral_marks: Optional[int] = None
    assignment_marks: Optional[int] = None
    total_marks: int
    passing_marks: int


class ExamResultCreate(BaseModel):
    exam_id: int
    student_id: int
    subject_id: int
    theory_marks: Optional[Decimal] = None
    practical_marks: Optional[Decimal] = None
    oral_marks: Optional[Decimal] = None
    assignment_marks: Optional[Decimal] = None
    obtained_marks: Decimal  # Total obtained marks
    total_marks: Decimal


class BulkMarksEntry(BaseModel):
    """Bulk marks entry for a class"""
    exam_id: int
    class_id: int
    subject_id: int
    marks_data: List[Dict]  # List of {student_id, theory_marks, practical_marks, etc.}
    is_draft: Optional[bool] = False


class ExamResultResponse(BaseModel):
    result_id: int
    exam_id: int
    exam_name: str
    student_id: int
    student_name: str
    subject_id: int
    subject_name: str
    theory_marks: Optional[Decimal]
    practical_marks: Optional[Decimal]
    oral_marks: Optional[Decimal]
    assignment_marks: Optional[Decimal]
    obtained_marks: Decimal
    total_marks: Decimal
    grade: str
    percentage: Optional[Decimal]
    status: Optional[str]  # Pass/Fail


class StudentReportCard(BaseModel):
    student_id: int
    student_name: str
    admission_number: str
    class_name: str
    section: str
    exam_name: str
    academic_year: int
    results: List[ExamResultResponse]
    total_obtained: Decimal
    total_marks: Decimal
    percentage: Decimal
    overall_grade: str
    status: str  # Pass/Fail
    class_rank: Optional[int] = None
    total_students: Optional[int] = None


class ProgressiveReport(BaseModel):
    """Progressive report showing performance across multiple terms"""
    student_id: int
    student_name: str
    admission_number: str
    class_name: str
    academic_year: int
    term_results: List[Dict]  # Results for each term
    subject_wise_progress: List[Dict]  # Subject-wise comparison across terms
    overall_trend: str  # Improving/Declining/Stable


class ClassPerformanceSummary(BaseModel):
    """Class-wise performance summary"""
    class_id: int
    class_name: str
    section: str
    exam_id: int
    exam_name: str
    subject_summaries: List[Dict]  # Per-subject statistics
    overall_stats: Dict  # Overall class statistics
    top_performers: List[Dict]
    weak_students: List[Dict]
    overall_grade: str


# =============================================
# Class Models
# =============================================
class ClassCreate(BaseModel):
    class_name: str
    class_name_urdu: Optional[str] = None
    section: str
    academic_year: int
    class_teacher_id: Optional[int] = None
    capacity: int = 40


class ClassResponse(BaseModel):
    class_id: int
    class_name: str
    class_name_urdu: Optional[str]
    section: str
    academic_year: int
    class_teacher_id: Optional[int]
    teacher_name: Optional[str]
    capacity: int
    current_strength: Optional[int]
    is_active: bool


# =============================================
# Subject Models
# =============================================
class SubjectCreate(BaseModel):
    subject_name: str
    subject_name_urdu: Optional[str] = None
    subject_code: str
    total_marks: int = 100
    passing_marks: int = 40


class SubjectResponse(BaseModel):
    subject_id: int
    subject_name: str
    subject_name_urdu: Optional[str]
    subject_code: str
    total_marks: int
    passing_marks: int
    is_active: bool


# =============================================
# Notification Models
# =============================================
class NotificationCreate(BaseModel):
    recipient_type: str  # Student, Parent, Staff
    recipient_id: int
    notification_type: str  # SMS, Email
    subject: Optional[str] = None
    message: str


class NotificationResponse(BaseModel):
    notification_id: int
    recipient_type: str
    notification_type: str
    subject: Optional[str]
    message: str
    status: str
    sent_at: Optional[datetime]
    created_at: datetime


# =============================================
# Common Models
# =============================================
class PaginationParams(BaseModel):
    page: int = Field(1, ge=1)
    page_size: int = Field(20, ge=1, le=100)


class PaginatedResponse(BaseModel):
    items: List[Any]
    total: int
    page: int
    page_size: int
    total_pages: int


class SuccessResponse(BaseModel):
    success: bool = True
    message: str
    data: Optional[dict] = None


class ErrorResponse(BaseModel):
    success: bool = False
    error: str
    details: Optional[str] = None


# =============================================
# Teacher Management Models
# =============================================
class TeacherPeriodCreate(BaseModel):
    teacher_id: int
    class_id: int
    subject_id: Optional[int] = None
    days_of_week: List[int]  # List of days: 1=Monday, 2=Tuesday, ..., 7=Sunday
    period_number: int
    start_time: str  # Format: "HH:MM:SS"
    end_time: str
    academic_year: int


class TeacherPeriodResponse(BaseModel):
    period_id: int
    teacher_id: int
    teacher_name: Optional[str]
    class_id: int
    class_name: Optional[str]
    section: Optional[str]
    subject_id: Optional[int]
    subject_name: Optional[str]
    day_of_week: int
    period_number: int
    start_time: str
    end_time: str
    academic_year: int
    is_active: bool


class TeacherAttendanceCreate(BaseModel):
    teacher_id: int
    attendance_date: date
    status: str  # Present, Absent, Leave, Half-Day, Late
    check_in_time: Optional[str] = None
    check_out_time: Optional[str] = None
    leave_type: Optional[str] = None
    remarks: Optional[str] = None


class TeacherAttendanceResponse(BaseModel):
    teacher_attendance_id: int
    teacher_id: int
    teacher_name: Optional[str]
    attendance_date: date
    status: str
    check_in_time: Optional[str]
    check_out_time: Optional[str]
    leave_type: Optional[str]
    remarks: Optional[str]
    marked_by: int


class PeriodSubstitutionCreate(BaseModel):
    original_period_id: int
    substitute_teacher_id: int
    substitution_date: date
    remarks: Optional[str] = None


class PeriodSubstitutionResponse(BaseModel):
    substitution_id: int
    original_period_id: int
    substitute_teacher_id: int
    substitute_teacher_name: Optional[str]
    substitution_date: date
    remarks: Optional[str]
    assigned_by: int


class TeacherScheduleResponse(BaseModel):
    teacher_id: int
    teacher_name: str
    designation: Optional[str]
    periods: List[TeacherPeriodResponse]


class AbsentTeacherWithPeriodsResponse(BaseModel):
    teacher_id: int
    teacher_name: str
    attendance_date: date
    status: str
    periods: List[TeacherPeriodResponse]


class TeacherLeaveCreate(BaseModel):
    teacher_id: int
    leave_type: str  # Casual, Medical, Earned, etc.
    leave_date: date
    remarks: Optional[str] = None


class TeacherLeaveResponse(BaseModel):
    leave_id: int
    teacher_id: int
    teacher_name: Optional[str]
    leave_type: str
    leave_date: date
    remarks: Optional[str]
    approved_by: Optional[int]
    approved_at: Optional[datetime]


class TeacherAttendanceSummaryResponse(BaseModel):
    teacher_id: int
    teacher_name: str
    total_present: int
    total_absent: int
    total_leave: int
    total_half_day: int
    total_late: int
    leave_count: float  # 2 half-days = 1 leave
    working_days: int
    attendance_percentage: float


# =============================================
# School Configuration Models
# =============================================
class SchoolConfigCreate(BaseModel):
    config_key: str
    config_value: str
    description: Optional[str] = None


class SchoolConfigResponse(BaseModel):
    config_id: int
    config_key: str
    config_value: str
    description: Optional[str]
    updated_at: Optional[datetime]


class PeriodTemplateCreate(BaseModel):
    period_number: int
    period_name: str
    start_time: str  # Format: "HH:MM:SS"
    end_time: str
    is_teaching_period: bool = True
    academic_year: int


class PeriodTemplateResponse(BaseModel):
    template_id: int
    period_number: int
    period_name: str
    start_time: str
    end_time: str
    is_teaching_period: bool
    academic_year: int
    is_active: bool


class ClassTimetableCreate(BaseModel):
    class_id: int
    subject_id: Optional[int] = None
    teacher_id: Optional[int] = None
    day_of_week: int  # 1=Monday to 5=Friday
    period_number: int
    start_time: str
    end_time: str
    academic_year: int


class ClassTimetableResponse(BaseModel):
    timetable_id: int
    class_id: int
    class_name: Optional[str]
    section: Optional[str]
    subject_id: Optional[int]
    subject_name: Optional[str]
    teacher_id: Optional[int]
    teacher_name: Optional[str]
    day_of_week: int
    period_number: int
    period_name: Optional[str]
    start_time: str
    end_time: str
    academic_year: int
    is_teaching_period: Optional[bool]


class TeacherAvailabilityCheck(BaseModel):
    is_available: bool
    conflicting_class: Optional[str]
    conflicting_subject: Optional[str]


class TimetableGridResponse(BaseModel):
    class_id: int
    class_name: str
    section: str
    timetable: List[ClassTimetableResponse]
