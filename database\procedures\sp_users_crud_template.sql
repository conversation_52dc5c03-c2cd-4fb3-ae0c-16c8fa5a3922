/*
  Template: Users CRUD stored procedures
  NOTE: This is a template / documentation file only. Actual procedures for users already exist in sp_user_management.sql.
  Keep this file as a reference for expected procedure names and signatures.

  Example procedures referenced by backend code:
    - sp_GetAllUsers()
    - sp_GetUserById(@user_id INT)
    - sp_CheckUsernameExists(@username NVARCHAR(150))
    - sp_CheckEmailExists(@email NVARCHAR(255))
    - sp_CheckEmailExistsExceptUser(@email NVARCHAR(255), @user_id INT)
    - sp_CreateUser(@username, @password_hash, @email, @full_name, @full_name_urdu, @role_id, @phone)
    - sp_UpdateUser(@user_id, @email, @full_name, @full_name_urdu, @role_id, @phone, @is_active)
    - sp_GetPasswordHashByUserId(@user_id INT)
    - sp_UpdatePassword(@user_id INT, @password_hash NVARCHAR(255))
    - sp_SoftDeleteUser(@user_id INT)

  Add or edit the actual CREATE PROCEDURE statements in sp_user_management.sql when applying to the DB.
*/
