USE SchoolManagementDB;
GO

-- =============================================
-- Procedure: sp_GetAllClasses
-- Returns all classes with teacher name and student count
-- =============================================
IF OBJECT_ID('dbo.sp_GetAllClasses', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_GetAllClasses;
GO
CREATE PROCEDURE dbo.sp_GetAllClasses
AS
BEGIN
    SET NOCOUNT ON;
    SELECT c.class_id, c.class_name, c.class_name_urdu, c.section,
           c.academic_year, c.class_teacher_id, c.capacity, c.is_active,
           c.created_at, c.updated_at,
           u.full_name AS teacher_name,
           ISNULL(sc.student_count, 0) AS student_count
    FROM classes c
    LEFT JOIN users u ON c.class_teacher_id = u.user_id
    LEFT JOIN (
        SELECT class_id, COUNT(*) AS student_count
        FROM students
        WHERE status = 'Active' OR is_active = 1
        GROUP BY class_id
    ) sc ON sc.class_id = c.class_id
    ORDER BY c.class_name, c.section;
END
GO

-- =============================================
-- Procedure: sp_GetClass
-- Returns a single class by id with teacher name and student count
-- =============================================
IF OBJECT_ID('dbo.sp_GetClass', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_GetClass;
GO
CREATE PROCEDURE dbo.sp_GetClass
    @class_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT c.class_id, c.class_name, c.class_name_urdu, c.section,
           c.academic_year, c.class_teacher_id, c.capacity, c.is_active,
           c.created_at, c.updated_at,
           u.full_name AS teacher_name,
           ISNULL((SELECT COUNT(*) FROM students s WHERE s.class_id = c.class_id AND (s.status = 'Active' OR s.is_active = 1)), 0) AS student_count
    FROM classes c
    LEFT JOIN users u ON c.class_teacher_id = u.user_id
    WHERE c.class_id = @class_id;
END
GO

-- =============================================
-- Procedure: sp_GetClassStudents
-- Returns all active students in a class
-- =============================================
IF OBJECT_ID('dbo.sp_GetClassStudents', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_GetClassStudents;
GO
CREATE PROCEDURE dbo.sp_GetClassStudents
    @class_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT student_id, admission_number, full_name, full_name_urdu,
           father_name, date_of_birth, gender,
           CASE WHEN is_active = 1 THEN 'Active' ELSE 'Inactive' END AS status
    FROM students
    WHERE class_id = @class_id AND is_active = 1
    ORDER BY full_name;
END
GO

-- =============================================
-- Procedure: sp_CreateClass
-- Create a class if not exists and return created row
-- =============================================
IF OBJECT_ID('dbo.sp_CreateClass', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_CreateClass;
GO
CREATE PROCEDURE dbo.sp_CreateClass
    @class_name NVARCHAR(200),
    @class_name_urdu NVARCHAR(200) = NULL,
    @section NVARCHAR(50),
    @academic_year INT,
    @class_teacher_id INT = NULL,
    @capacity INT = 30
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        IF EXISTS (SELECT 1 FROM classes WHERE class_name = @class_name AND section = @section AND academic_year = @academic_year)
        BEGIN
            SELECT 'Error' AS Result, 'Class already exists' AS Message;
            RETURN;
        END

        INSERT INTO classes (class_name, class_name_urdu, section, academic_year, class_teacher_id, capacity, is_active, created_at)
        VALUES (@class_name, @class_name_urdu, @section, @academic_year, @class_teacher_id, @capacity, 1, GETDATE());

        DECLARE @id INT = SCOPE_IDENTITY();

        SELECT c.class_id, c.class_name, c.class_name_urdu, c.section,
               c.academic_year, c.class_teacher_id, c.capacity, c.is_active,
               c.created_at, c.updated_at,
               u.full_name AS teacher_name,
               0 AS student_count
        FROM classes c
        LEFT JOIN users u ON c.class_teacher_id = u.user_id
        WHERE c.class_id = @id;
    END TRY
    BEGIN CATCH
        SELECT 'Error' AS Result, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_UpdateClass
-- Update class fields and return the updated row
-- =============================================
IF OBJECT_ID('dbo.sp_UpdateClass', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_UpdateClass;
GO
CREATE PROCEDURE dbo.sp_UpdateClass
    @class_id INT,
    @class_name NVARCHAR(200) = NULL,
    @class_name_urdu NVARCHAR(200) = NULL,
    @section NVARCHAR(50) = NULL,
    @academic_year INT = NULL,
    @class_teacher_id INT = NULL,
    @capacity INT = NULL,
    @is_active BIT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        UPDATE classes
        SET class_name = COALESCE(@class_name, class_name),
            class_name_urdu = COALESCE(@class_name_urdu, class_name_urdu),
            section = COALESCE(@section, section),
            academic_year = COALESCE(@academic_year, academic_year),
            class_teacher_id = COALESCE(@class_teacher_id, class_teacher_id),
            capacity = COALESCE(@capacity, capacity),
            is_active = COALESCE(@is_active, is_active),
            updated_at = GETDATE()
        WHERE class_id = @class_id;

        SELECT c.class_id, c.class_name, c.class_name_urdu, c.section,
               c.academic_year, c.class_teacher_id, c.capacity, c.is_active,
               c.created_at, c.updated_at,
               u.full_name AS teacher_name,
               ISNULL((SELECT COUNT(*) FROM students s WHERE s.class_id = c.class_id AND (s.status = 'Active' OR s.is_active = 1)), 0) AS student_count
        FROM classes c
        LEFT JOIN users u ON c.class_teacher_id = u.user_id
        WHERE c.class_id = @class_id;
    END TRY
    BEGIN CATCH
        SELECT 'Error' AS Result, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- =============================================
-- Procedure: sp_DeleteClass
-- Soft delete a class (set is_active = 0)
-- =============================================
IF OBJECT_ID('dbo.sp_DeleteClass', 'P') IS NOT NULL
    DROP PROCEDURE dbo.sp_DeleteClass;
GO
CREATE PROCEDURE dbo.sp_DeleteClass
    @class_id INT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        -- Ensure no active students
        IF EXISTS (SELECT 1 FROM students WHERE class_id = @class_id AND (status = 'Active' OR is_active = 1))
        BEGIN
            SELECT 'Error' AS Result, 'Cannot delete class with active students' AS Message;
            RETURN;
        END

        UPDATE classes
        SET is_active = 0, updated_at = GETDATE()
        WHERE class_id = @class_id;

        SELECT 'Success' AS Result, 'Class deleted successfully' AS Message;
    END TRY
    BEGIN CATCH
        SELECT 'Error' AS Result, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO
