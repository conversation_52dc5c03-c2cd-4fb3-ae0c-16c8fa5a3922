-- User Management Stored Procedures
USE SchoolManagementDB;
GO

-- Get all users with role information
CREATE OR ALTER PROCEDURE sp_GetAllUsers
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        u.user_id,
        u.username,
        u.email,
        u.full_name,
        u.full_name_urdu,
        u.phone,
        u.is_active,
        u.last_login,
        u.created_at,
        u.updated_at,
        r.role_id,
        r.role_name,
        r.role_name_urdu
    FROM users u
    LEFT JOIN roles r ON u.role_id = r.role_id
    WHERE u.is_active = 1
    ORDER BY u.created_at DESC;
END;
GO

-- Get user by ID
CREATE OR ALTER PROCEDURE sp_GetUserById
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        u.user_id,
        u.username,
        u.email,
        u.full_name,
        u.full_name_urdu,
        u.phone,
        u.is_active,
        u.last_login,
        u.created_at,
        u.updated_at,
        r.role_id,
        r.role_name,
        r.role_name_urdu
    FROM users u
    LEFT JOIN roles r ON u.role_id = r.role_id
    WHERE u.user_id = @user_id;
END;
GO

-- Get all roles
CREATE OR ALTER PROCEDURE sp_GetAllRoles
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        role_id,
        role_name,
        role_name_urdu,
        description,
        is_active
    FROM roles
    WHERE is_active = 1
    ORDER BY role_name;
END;
GO

-- Check if username exists
CREATE OR ALTER PROCEDURE sp_CheckUsernameExists
    @username NVARCHAR(50)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT COUNT(*) as count
    FROM users
    WHERE username = @username AND is_active = 1;
END;
GO

-- Check if email exists
CREATE OR ALTER PROCEDURE sp_CheckEmailExists
    @email NVARCHAR(100)
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT COUNT(*) as count
    FROM users
    WHERE email = @email AND is_active = 1;
END;
GO

-- Check if email exists except for specific user
CREATE OR ALTER PROCEDURE sp_CheckEmailExistsExceptUser
    @email NVARCHAR(100),
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT COUNT(*) as count
    FROM users
    WHERE email = @email AND user_id != @user_id AND is_active = 1;
END;
GO

-- Create new user
CREATE OR ALTER PROCEDURE sp_CreateUser
    @username NVARCHAR(50),
    @password_hash NVARCHAR(255),
    @email NVARCHAR(100),
    @full_name NVARCHAR(100),
    @full_name_urdu NVARCHAR(100) = NULL,
    @role_id INT,
    @phone NVARCHAR(20) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @new_user_id INT;
    
    INSERT INTO users (username, password_hash, email, full_name, full_name_urdu, role_id, phone, is_active, created_at, updated_at)
    VALUES (@username, @password_hash, @email, @full_name, @full_name_urdu, @role_id, @phone, 1, GETDATE(), GETDATE());
    
    SET @new_user_id = SCOPE_IDENTITY();
    
    -- Return the created user
    EXEC sp_GetUserById @user_id = @new_user_id;
END;
GO

-- Update user
CREATE OR ALTER PROCEDURE sp_UpdateUser
    @user_id INT,
    @email NVARCHAR(100) = NULL,
    @full_name NVARCHAR(100) = NULL,
    @full_name_urdu NVARCHAR(100) = NULL,
    @role_id INT = NULL,
    @phone NVARCHAR(20) = NULL,
    @is_active BIT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE users
    SET 
        email = COALESCE(@email, email),
        full_name = COALESCE(@full_name, full_name),
        full_name_urdu = COALESCE(@full_name_urdu, full_name_urdu),
        role_id = COALESCE(@role_id, role_id),
        phone = COALESCE(@phone, phone),
        is_active = COALESCE(@is_active, is_active),
        updated_at = GETDATE()
    WHERE user_id = @user_id;
    
    -- Return the updated user
    EXEC sp_GetUserById @user_id = @user_id;
END;
GO

-- Get password hash by user ID
CREATE OR ALTER PROCEDURE sp_GetPasswordHashByUserId
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT password_hash
    FROM users
    WHERE user_id = @user_id AND is_active = 1;
END;
GO

-- Update password
CREATE OR ALTER PROCEDURE sp_UpdatePassword
    @user_id INT,
    @password_hash NVARCHAR(255)
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE users
    SET password_hash = @password_hash, updated_at = GETDATE()
    WHERE user_id = @user_id;
END;
GO

-- Soft delete user
CREATE OR ALTER PROCEDURE sp_SoftDeleteUser
    @user_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    UPDATE users
    SET is_active = 0, updated_at = GETDATE()
    WHERE user_id = @user_id;
END;
GO

PRINT 'User management stored procedures created successfully!';
