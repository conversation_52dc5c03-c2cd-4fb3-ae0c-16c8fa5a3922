-- =============================================
-- Exam System Enhancements Migration
-- =============================================

-- Add new columns to exams table if they don't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'exams') AND name = 'term_type')
BEGIN
    ALTER TABLE exams ADD term_type NVARCHAR(50) NULL;
    PRINT 'Added term_type column to exams table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'exams') AND name = 'total_marks')
BEGIN
    ALTER TABLE exams ADD total_marks INT NULL;
    PRINT 'Added total_marks column to exams table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'exams') AND name = 'passing_marks')
BEGIN
    ALTER TABLE exams ADD passing_marks INT NULL;
    PRINT 'Added passing_marks column to exams table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'exams') AND name = 'weightage')
BEGIN
    ALTER TABLE exams ADD weightage DECIMAL(5,2) NULL;
    PRINT 'Added weightage column to exams table';
END

-- Add new columns to exam_results table if they don't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'exam_results') AND name = 'theory_marks')
BEGIN
    ALTER TABLE exam_results ADD theory_marks DECIMAL(5,2) NULL;
    PRINT 'Added theory_marks column to exam_results table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'exam_results') AND name = 'practical_marks')
BEGIN
    ALTER TABLE exam_results ADD practical_marks DECIMAL(5,2) NULL;
    PRINT 'Added practical_marks column to exam_results table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'exam_results') AND name = 'oral_marks')
BEGIN
    ALTER TABLE exam_results ADD oral_marks DECIMAL(5,2) NULL;
    PRINT 'Added oral_marks column to exam_results table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'exam_results') AND name = 'assignment_marks')
BEGIN
    ALTER TABLE exam_results ADD assignment_marks DECIMAL(5,2) NULL;
    PRINT 'Added assignment_marks column to exam_results table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'exam_results') AND name = 'is_draft')
BEGIN
    ALTER TABLE exam_results ADD is_draft BIT NULL;
    PRINT 'Added is_draft column to exam_results table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'exam_results') AND name = 'entered_by')
BEGIN
    ALTER TABLE exam_results ADD entered_by INT NULL;
    PRINT 'Added entered_by column to exam_results table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'exam_results') AND name = 'updated_at')
BEGIN
    ALTER TABLE exam_results ADD updated_at DATETIME NULL;
    PRINT 'Added updated_at column to exam_results table';
END

-- Add foreign key constraint if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_exam_results_entered_by')
BEGIN
    ALTER TABLE exam_results ADD CONSTRAINT FK_exam_results_entered_by FOREIGN KEY (entered_by) REFERENCES users(user_id);
    PRINT 'Added FK_exam_results_entered_by foreign key constraint';
END

PRINT '';
PRINT '✅ Exam system enhancements migration completed successfully!';

