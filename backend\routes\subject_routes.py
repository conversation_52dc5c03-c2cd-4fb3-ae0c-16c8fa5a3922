"""
Subject Routes - Subject management
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from pydantic import BaseModel
from auth import get_current_user, require_admin
from database import execute_query, execute_non_query
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


class SubjectCreate(BaseModel):
    subject_name: str
    subject_name_urdu: Optional[str] = None
    subject_code: str
    total_marks: int = 100
    is_active: bool = True


class SubjectResponse(BaseModel):
    subject_id: int
    subject_name: str
    subject_name_urdu: Optional[str]
    subject_code: str
    total_marks: int
    is_active: bool


@router.post("/", status_code=status.HTTP_201_CREATED)
async def create_subject(
    subject: SubjectCreate,
    current_user: dict = Depends(require_admin)
):
    """Create a new subject (Admin only)"""
    try:
        query = "EXEC sp_Subject_Create @subject_name=?, @subject_name_urdu=?, @subject_code=?, @total_marks=?, @is_active=?"
        execute_non_query(query, (
            subject.subject_name,
            subject.subject_name_urdu,
            subject.subject_code,
            subject.total_marks,
            subject.is_active
        ))

        return {"message": "Subject created successfully"}
    except Exception as e:
        logger.error(f"Error creating subject: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create subject: {str(e)}"
        )


@router.get("/", response_model=List[SubjectResponse])
async def get_all_subjects(
    is_active: Optional[bool] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get all subjects"""
    query = "EXEC sp_Subject_GetAll @is_active=?"
    results = execute_query(query, (is_active,))
    return results


@router.get("/{subject_id}", response_model=SubjectResponse)
async def get_subject(
    subject_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get subject by ID"""
    query = "EXEC sp_Subject_GetById @subject_id=?"
    results = execute_query(query, (subject_id,))

    if not results:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Subject not found"
        )

    return results[0]


@router.put("/{subject_id}")
async def update_subject(
    subject_id: int,
    subject: SubjectCreate,
    current_user: dict = Depends(require_admin)
):
    """Update subject (Admin only)"""
    try:
        query = "EXEC sp_Subject_Update @subject_id=?, @subject_name=?, @subject_name_urdu=?, @subject_code=?, @total_marks=?, @is_active=?"
        execute_non_query(query, (
            subject_id,
            subject.subject_name,
            subject.subject_name_urdu,
            subject.subject_code,
            subject.total_marks,
            subject.is_active
        ))

        return {"message": "Subject updated successfully"}
    except Exception as e:
        logger.error(f"Error updating subject: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update subject: {str(e)}"
        )


@router.delete("/{subject_id}")
async def delete_subject(
    subject_id: int,
    current_user: dict = Depends(require_admin)
):
    """Delete subject (Admin only)"""
    try:
        query = "EXEC sp_Subject_Delete @subject_id=?"
        execute_non_query(query, (subject_id,))

        return {"message": "Subject deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting subject: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete subject: {str(e)}"
        )


@router.post("/assign-to-class")
async def assign_subject_to_class(
    class_id: int,
    subject_id: int,
    teacher_id: Optional[int] = None,
    current_user: dict = Depends(require_admin)
):
    """Assign subject to a class"""
    try:
        query = "EXEC sp_Subject_AssignToClass @class_id=?, @subject_id=?, @teacher_id=?"
        execute_query(query, (class_id, subject_id, teacher_id))

        return {"message": "Subject assigned to class successfully"}
    except Exception as e:
        logger.error(f"Error assigning subject: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to assign subject: {str(e)}"
        )


@router.get("/class/{class_id}/subjects")
async def get_class_subjects(
    class_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get all subjects for a class"""
    query = "EXEC sp_Subject_GetByClass @class_id=?"
    results = execute_query(query, (class_id,))
    return results

