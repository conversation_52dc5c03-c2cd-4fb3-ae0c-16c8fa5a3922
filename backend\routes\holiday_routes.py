"""
Holiday Routes - Holiday management
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from pydantic import BaseModel
from datetime import date
from auth import get_current_user, require_admin
from database import execute_query, execute_non_query
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


class HolidayCreate(BaseModel):
    holiday_name: str
    holiday_name_urdu: Optional[str] = None
    holiday_date: date
    holiday_type: Optional[str] = "Public"  # Public, School Event, etc.
    description: Optional[str] = None
    is_active: bool = True


class HolidayResponse(BaseModel):
    holiday_id: int
    holiday_name: str
    holiday_name_urdu: Optional[str]
    holiday_date: date
    holiday_type: Optional[str]
    description: Optional[str]
    is_active: bool


@router.post("/", status_code=status.HTTP_201_CREATED)
async def create_holiday(
    holiday: HolidayCreate,
    current_user: dict = Depends(require_admin)
):
    """Create a new holiday (Admin only)"""
    try:
        query = "EXEC sp_Holiday_Create @holiday_name=?, @holiday_name_urdu=?, @holiday_date=?, @holiday_type=?, @description=?, @is_active=?"
        execute_non_query(query, (
            holiday.holiday_name,
            holiday.holiday_name_urdu,
            holiday.holiday_date,
            holiday.holiday_type,
            holiday.description,
            holiday.is_active
        ))

        return {"message": "Holiday created successfully"}
    except Exception as e:
        logger.error(f"Error creating holiday: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create holiday: {str(e)}"
        )


@router.get("/", response_model=List[HolidayResponse])
async def get_all_holidays(
    year: Optional[int] = Query(None),
    month: Optional[int] = Query(None, ge=1, le=12),
    is_active: Optional[bool] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get all holidays with optional filters"""
    try:
        query = "EXEC sp_Holiday_GetAll @year=?, @month=?, @is_active=?"
        results = execute_query(query, (year, month, is_active))
        return results
    except Exception as e:
        logger.warning(f"Stored procedure failed, using fallback query: {e}")
        # Fallback to direct SQL query if stored procedure doesn't exist
        try:
            fallback_query = """
                SELECT holiday_id, holiday_name, holiday_name_urdu, holiday_date,
                       holiday_type, description, is_active
                FROM holidays
                WHERE (@year IS NULL OR YEAR(holiday_date) = @year)
                  AND (@month IS NULL OR MONTH(holiday_date) = @month)
                  AND (@is_active IS NULL OR is_active = @is_active)
                ORDER BY holiday_date
            """
            # Use simpler query without parameters for fallback
            simple_query = "SELECT holiday_id, holiday_name, holiday_name_urdu, holiday_date, holiday_type, description, is_active FROM holidays ORDER BY holiday_date"
            results = execute_query(simple_query)
            # Filter in Python if needed
            if year:
                results = [r for r in results if r['holiday_date'].year == year]
            if month:
                results = [r for r in results if r['holiday_date'].month == month]
            if is_active is not None:
                results = [r for r in results if r['is_active'] == is_active]
            return results
        except Exception as e2:
            logger.error(f"Fallback query also failed: {e2}")
            return []


@router.get("/{holiday_id}", response_model=HolidayResponse)
async def get_holiday(
    holiday_id: int,
    current_user: dict = Depends(get_current_user)
):
    """Get holiday by ID"""
    query = "EXEC sp_Holiday_GetById @holiday_id=?"
    results = execute_query(query, (holiday_id,))

    if not results:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Holiday not found"
        )

    return results[0]


@router.put("/{holiday_id}")
async def update_holiday(
    holiday_id: int,
    holiday: HolidayCreate,
    current_user: dict = Depends(require_admin)
):
    """Update holiday (Admin only)"""
    try:
        query = "EXEC sp_Holiday_Update @holiday_id=?, @holiday_name=?, @holiday_name_urdu=?, @holiday_date=?, @holiday_type=?, @description=?, @is_active=?"
        execute_non_query(query, (
            holiday_id,
            holiday.holiday_name,
            holiday.holiday_name_urdu,
            holiday.holiday_date,
            holiday.holiday_type,
            holiday.description,
            holiday.is_active
        ))

        return {"message": "Holiday updated successfully"}
    except Exception as e:
        logger.error(f"Error updating holiday: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update holiday: {str(e)}"
        )


@router.delete("/{holiday_id}")
async def delete_holiday(
    holiday_id: int,
    current_user: dict = Depends(require_admin)
):
    """Delete holiday (Admin only)"""
    try:
        query = "EXEC sp_Holiday_Delete @holiday_id=?"
        execute_non_query(query, (holiday_id,))

        return {"message": "Holiday deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting holiday: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete holiday: {str(e)}"
        )


@router.get("/check/{check_date}")
async def check_holiday(
    check_date: date,
    current_user: dict = Depends(get_current_user)
):
    """Check if a specific date is a holiday"""
    query = "EXEC sp_Holiday_CheckDate @check_date=?"
    results = execute_query(query, (check_date,))

    return {
        "date": check_date,
        "is_holiday": len(results) > 0,
        "holiday": results[0] if results else None
    }


@router.get("/upcoming/list")
async def get_upcoming_holidays(
    limit: int = Query(10, ge=1, le=50),
    current_user: dict = Depends(get_current_user)
):
    """Get upcoming holidays"""
    query = "EXEC sp_Holiday_GetUpcoming @limit=?"
    results = execute_query(query, (limit,))
    return results

