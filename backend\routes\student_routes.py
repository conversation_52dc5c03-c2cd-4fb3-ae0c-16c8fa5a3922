"""
Student Routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional
from models import StudentCreate, StudentUpdate, StudentResponse, SuccessResponse
from auth import get_current_user, require_receptionist
from database import execute_query, execute_non_query, execute_scalar, execute_procedure, execute_procedure_single
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/", response_model=SuccessResponse)
async def create_student(student: StudentCreate, current_user: dict = Depends(require_receptionist)):
    """
    Create a new student (Receptionist/Admin only)

    Args:
        student: Student data
        current_user: Current authenticated user

    Returns:
        Success response with student ID
    """
    try:
        # Create student via stored procedure
        params = {
            'admission_number': student.admission_number,
            'full_name': student.full_name,
            'full_name_urdu': student.full_name_urdu,
            'father_name': student.father_name,
            'father_name_urdu': student.father_name_urdu,
            'date_of_birth': student.date_of_birth,
            'gender': student.gender,
            'cnic': student.cnic,
            'b_form': student.b_form,
            'class_id': student.class_id,
            'parent_phone': student.parent_phone,
            'parent_email': student.parent_email,
            'address': student.address,
            'address_urdu': student.address_urdu
        }

        res = execute_procedure_single('sp_CreateStudent', params)
        student_id = res.get('student_id') if res else None
        return SuccessResponse(message="Student created successfully", data={'student_id': student_id})
    except Exception as e:
        logger.error(f"Error creating student: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create student: {str(e)}"
        )


@router.get("/", response_model=list)
async def get_students(
    class_id: Optional[int] = Query(None),
    status_filter: Optional[str] = Query(None, alias="status"),
    search: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get list of students with optional filters
    
    Args:
        class_id: Filter by class ID
        status_filter: Filter by status (Active, Discharged, etc.)
        search: Search by name or admission number
        current_user: Current authenticated user
    
    Returns:
        List of students
    """
    try:
        # Delegate to stored procedure which supports optional parameters
        proc_params = {
            'class_id': class_id,
            'status': status_filter,
            'search': search
        }
        students = execute_procedure('sp_GetStudents', proc_params)
        return students
    except Exception as e:
        logger.warning(f"Stored procedure failed, using fallback query: {e}")
        # Fallback to direct SQL query
        try:
            fallback_query = """
                SELECT
                    s.student_id, s.admission_number, s.full_name, s.full_name_urdu,
                    s.father_name, s.father_name_urdu, s.date_of_birth, s.gender,
                    s.b_form, s.class_id, s.admission_date, s.discharge_date,
                    s.status, s.parent_email, s.address, s.address_urdu,
                    s.photo_url, s.is_active, s.created_at, s.updated_at,
                    c.class_name, c.section
                FROM students s
                LEFT JOIN classes c ON s.class_id = c.class_id
                WHERE s.is_active = 1
                ORDER BY s.full_name
            """
            results = execute_query(fallback_query)
            # Filter in Python if needed
            if class_id:
                results = [r for r in results if r.get('class_id') == class_id]
            if status_filter:
                results = [r for r in results if r.get('status') == status_filter]
            if search:
                search_lower = search.lower()
                results = [r for r in results if search_lower in r.get('full_name', '').lower() or search_lower in r.get('admission_number', '').lower()]
            return results
        except Exception as e2:
            logger.error(f"Fallback query also failed: {e2}")
            return []


@router.get("/{student_id}", response_model=dict)
async def get_student(student_id: int, current_user: dict = Depends(get_current_user)):
    """
    Get student details by ID
    
    Args:
        student_id: Student ID
        current_user: Current authenticated user
    
    Returns:
        Student details
    """
    result = execute_procedure_single('sp_GetStudentById', {'student_id': student_id})
    if not result:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Student not found")
    return result


@router.put("/{student_id}", response_model=SuccessResponse)
async def update_student(
    student_id: int,
    student_update: StudentUpdate,
    current_user: dict = Depends(require_receptionist)
):
    """
    Update student information (Receptionist/Admin only)
    
    Args:
        student_id: Student ID
        student_update: Updated student data
        current_user: Current authenticated user
    
    Returns:
        Success response
    """
    proc_params = {
        'student_id': student_id,
        'full_name': student_update.full_name,
        'full_name_urdu': student_update.full_name_urdu,
        'father_name': student_update.father_name,
        'class_id': student_update.class_id,
        'parent_phone': student_update.parent_phone,
        'parent_email': student_update.parent_email,
        'address': student_update.address,
        'status': student_update.status
    }
    execute_procedure_single('sp_UpdateStudent', proc_params)
    return SuccessResponse(message="Student updated successfully")


@router.delete("/{student_id}", response_model=SuccessResponse)
async def discharge_student(student_id: int, current_user: dict = Depends(require_receptionist)):
    """
    Discharge a student (Receptionist/Admin only)
    
    Args:
        student_id: Student ID
        current_user: Current authenticated user
    
    Returns:
        Success response
    """
    execute_procedure_single('sp_DischargeStudent', {'student_id': student_id})
    return SuccessResponse(message="Student discharged successfully")


@router.get("/{student_id}/profile", response_model=dict)
async def get_student_profile(student_id: int, current_user: dict = Depends(get_current_user)):
    """
    Get complete student profile with attendance, fees, and exam data
    
    Args:
        student_id: Student ID
        current_user: Current authenticated user
    
    Returns:
        Complete student profile
    """
    # Get student basic info
    student = execute_procedure_single('sp_GetStudentById', {'student_id': student_id})
    if not student:
        raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Student not found")
    
    # Get attendance summary (current month)
    from datetime import date
    from services.attendance_service import AttendanceService
    
    today = date.today()
    start_of_month = date(today.year, today.month, 1)
    
    attendance_stats = AttendanceService.get_attendance_percentage(student_id, start_of_month, today)
    
    # Get fee account
    from services.fee_service import FeeService
    fee_account = FeeService.get_fee_account(student_id, student['academic_year'])
    
    # Get recent exam results
    from services.exam_service import ExamService
    exam_results = ExamService.get_student_results(student_id)
    
    return {
        "student": student,
        "attendance": attendance_stats,
        "fee_account": fee_account,
        "recent_exams": exam_results[:5] if exam_results else []
    }


@router.get("/class/{class_id}/list", response_model=list)
async def get_class_students(class_id: int, current_user: dict = Depends(get_current_user)):
    """
    Get all students in a class
    
    Args:
        class_id: Class ID
        current_user: Current authenticated user
    
    Returns:
        List of students in the class
    """
    students = execute_procedure('sp_GetClassStudents', {'class_id': class_id})
    return students

