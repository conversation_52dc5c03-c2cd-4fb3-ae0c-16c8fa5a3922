"""
Teacher Schedule Routes
Handles teacher schedule viewing and management
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from models import (TeacherScheduleResponse, TeacherPeriodCreate)
from auth import get_current_user, require_admin
from services.teacher_service import TeacherService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/schedules", response_model=List[TeacherScheduleResponse])
async def get_all_teacher_schedules(
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Get schedules for all teachers
    
    Args:
        academic_year: Optional academic year filter
        current_user: Current authenticated user
    
    Returns:
        List of teacher schedules
    """
    try:
        result = TeacherService.get_all_teacher_schedules(academic_year)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching teacher schedules: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching teacher schedules"
        )


@router.get("/schedules/{teacher_id}", response_model=TeacherScheduleResponse)
async def get_teacher_schedule(
    teacher_id: int,
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Get schedule for a specific teacher
    
    Args:
        teacher_id: Teacher ID
        academic_year: Optional academic year filter
        current_user: Current authenticated user
    
    Returns:
        Teacher schedule details
    """
    try:
        result = TeacherService.get_teacher_schedule(teacher_id, academic_year)
        
        if result:
            return result
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Teacher schedule not found"
            )
    except Exception as e:
        logger.error(f"Error fetching teacher schedule: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching teacher schedule"
        )


@router.post("/periods", status_code=status.HTTP_201_CREATED)
async def create_teacher_period(
    period: TeacherPeriodCreate,
    current_user: dict = Depends(require_admin)
):
    """
    Create a new teacher period assignment (Admin only)
    
    Args:
        period: Teacher period data
        current_user: Current authenticated user
    
    Returns:
        Success response with period ID
    """
    try:
        result = TeacherService.create_teacher_period(period, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return {
                "message": result.get('Message', 'Teacher period created successfully'),
                "period_id": result.get('PeriodId')
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to create teacher period')
            )
    except Exception as e:
        logger.error(f"Error creating teacher period: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during teacher period creation"
        )


@router.delete("/periods/{period_id}")
async def delete_teacher_period(
    period_id: int,
    current_user: dict = Depends(require_admin)
):
    """
    Delete a teacher period assignment (Admin only)
    
    Args:
        period_id: Period ID to delete
        current_user: Current authenticated user
    
    Returns:
        Success response
    """
    try:
        result = TeacherService.delete_teacher_period(period_id, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return {"message": result.get('Message', 'Teacher period deleted successfully')}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to delete teacher period')
            )
    except Exception as e:
        logger.error(f"Error deleting teacher period: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during teacher period deletion"
        )
