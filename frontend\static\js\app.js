/**
 * Main Application Entry Point
 * This file loads the module loader which handles all other modules
 */

// Load the module loader
(function(){
    const moduleLoaderScript = document.createElement('script');
    moduleLoaderScript.src = '/static/js/module-loader.js';
    moduleLoaderScript.defer = true;
    moduleLoaderScript.onload = () => {
        console.log('[APP] Module loader loaded successfully');
    };
    moduleLoaderScript.onerror = () => {
        console.error('[APP] Failed to load module loader');
    };
    document.head.appendChild(moduleLoaderScript);
})();

// Note: All functionality has been moved to separate modular files:
// - router.js: Page routing and navigation
// - student-modals.js: Student-related modal dialogs
// - attendance-functions.js: Attendance management (split into 4 files)
// - fee-functions.js: Fee management functions
// - report-functions.js: Report generation functions
// - subject-functions.js: Subject management functions
// - staff-functions.js: Staff management functions
// - holiday-functions.js: Holiday management functions
// - teacher-functions.js: Teacher management functions
// - monthly-overview.js: Monthly overview functionality
// - sms-namespace.js: SMS namespace wrapper functions
