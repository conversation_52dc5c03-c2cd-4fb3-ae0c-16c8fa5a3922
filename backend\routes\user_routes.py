"""
"""
User Management Routes
"""
from fastapi import APIRouter, HTTPException, Depends, status
from typing import List, Optional
from pydantic import BaseModel, EmailStr
from datetime import datetime
from auth import get_current_user, hash_password
from database import execute_query, execute_non_query, execute_procedure, execute_procedure_single

router = APIRouter()


# Pydantic models
class UserBase(BaseModel):
    username: str
    email: EmailStr
    full_name: str
    full_name_urdu: Optional[str] = None
    role_id: int
    phone: Optional[str] = None


class UserCreate(UserBase):
    password: str


class UserUpdate(BaseModel):
    email: Optional[EmailStr] = None
    full_name: Optional[str] = None
    full_name_urdu: Optional[str] = None
    role_id: Optional[int] = None
    phone: Optional[str] = None
    is_active: Optional[bool] = None


class UserPasswordUpdate(BaseModel):
    current_password: str
    new_password: str


class UserResponse(UserBase):
    user_id: int
    role_name: str
    role_name_urdu: Optional[str] = None
    is_active: bool
    last_login: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    class Config:
        from_attributes = True


class RoleResponse(BaseModel):
    role_id: int
    role_name: str
    role_name_urdu: Optional[str] = None
    description: Optional[str] = None
    is_active: bool


@router.get("/roles", response_model=List[RoleResponse])
async def get_all_roles(current_user: dict = Depends(get_current_user)):
    """Get all available roles"""
    results = execute_procedure('sp_GetAllRoles')
    return results


@router.get("/", response_model=List[UserResponse])
async def get_all_users(current_user: dict = Depends(get_current_user)):
    """Get all users"""
    print(f"[USER_ROUTES] get_all_users called - current_user: {current_user.get('username')}")
    # Only Admin and Owner can view all users
    if current_user['role_name'] not in ['Administrator', 'Owner']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can view all users"
        )
    
    results = execute_procedure('sp_GetAllUsers')
    return results


@router.get("/{user_id}", response_model=UserResponse)
async def get_user(user_id: int, current_user: dict = Depends(get_current_user)):
    """Get a specific user by ID"""
    # Users can only view their own profile unless they're admin
    if current_user['user_id'] != user_id and current_user['role_name'] not in ['Administrator', 'Owner']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only view your own profile"
        )
    
    result = execute_procedure_single('sp_GetUserById', {'user_id': user_id})
    if not result:
        raise HTTPException(status_code=404, detail="User not found")
    return result


@router.post("/", response_model=UserResponse, status_code=status.HTTP_201_CREATED)
async def create_user(user_data: UserCreate, current_user: dict = Depends(get_current_user)):
    """Create a new user"""
    # Only Admin and Owner can create users
    if current_user['role_name'] not in ['Administrator', 'Owner']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can create users"
        )
    
    # Check if username/email already exists via stored procedures
    if execute_procedure('sp_CheckUsernameExists', {'username': user_data.username}):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Username already exists")
    if execute_procedure('sp_CheckEmailExists', {'email': user_data.email}):
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email already exists")
    
    # Hash password
    password_hash = hash_password(user_data.password)
    
    # Create user via stored procedure
    params = {
        'username': user_data.username,
        'password_hash': password_hash,
        'email': user_data.email,
        'full_name': user_data.full_name,
        'full_name_urdu': user_data.full_name_urdu,
        'role_id': user_data.role_id,
        'phone': user_data.phone
    }

    created = execute_procedure_single('sp_CreateUser', params)
    if not created:
        raise HTTPException(status_code=500, detail='Failed to create user')
    return created


@router.put("/{user_id}", response_model=UserResponse)
async def update_user(user_id: int, user_data: UserUpdate, current_user: dict = Depends(get_current_user)):
    """Update a user"""
    # Users can only update their own profile unless they're admin
    if current_user['user_id'] != user_id and current_user['role_name'] not in ['Administrator', 'Owner']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update your own profile"
        )
    
    # Check if user exists
    existing = execute_procedure_single('sp_GetUserById', {'user_id': user_id})
    if not existing:
        raise HTTPException(status_code=404, detail="User not found")
    
    # Build update query dynamically
    update_fields = []
    params = []
    
    if user_data.email is not None:
        # Check if email is already used by another user
        if execute_procedure('sp_CheckEmailExistsExceptUser', {'email': user_data.email, 'user_id': user_id}):
            raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email already exists")
        update_fields.append("email = ?")
        params.append(user_data.email)
    
    if user_data.full_name is not None:
        update_fields.append("full_name = ?")
        params.append(user_data.full_name)
    
    if user_data.full_name_urdu is not None:
        update_fields.append("full_name_urdu = ?")
        params.append(user_data.full_name_urdu)
    
    if user_data.role_id is not None:
        # Only admin can change roles
        if current_user['role_name'] not in ['Administrator', 'Owner']:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only administrators can change user roles"
            )
        update_fields.append("role_id = ?")
        params.append(user_data.role_id)
    
    if user_data.phone is not None:
        update_fields.append("phone = ?")
        params.append(user_data.phone)
    
    if user_data.is_active is not None:
        # Only admin can activate/deactivate users
        if current_user['role_name'] not in ['Administrator', 'Owner']:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only administrators can activate/deactivate users"
            )
        update_fields.append("is_active = ?")
        params.append(1 if user_data.is_active else 0)
    
    if not update_fields:
        raise HTTPException(status_code=400, detail="No fields to update")
    
    # Prepare parameters for stored procedure (use None for values not provided)
    proc_params = {
        'user_id': user_id,
        'email': user_data.email,
        'full_name': user_data.full_name,
        'full_name_urdu': user_data.full_name_urdu,
        'role_id': user_data.role_id,
        'phone': user_data.phone,
        'is_active': (1 if user_data.is_active else 0) if user_data.is_active is not None else None
    }

    updated = execute_procedure_single('sp_UpdateUser', proc_params)
    if not updated:
        # Fallback: return the existing user
        return await get_user(user_id, current_user)
    return updated


@router.put("/{user_id}/password")
async def update_password(user_id: int, password_data: UserPasswordUpdate, current_user: dict = Depends(get_current_user)):
    """Update user password"""
    # Users can only update their own password
    if current_user['user_id'] != user_id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only update your own password"
        )
    
    # Verify current password
    from auth import verify_password
    result = execute_procedure_single('sp_GetPasswordHashByUserId', {'user_id': user_id})
    if not result:
        raise HTTPException(status_code=404, detail="User not found")

    if not verify_password(password_data.current_password, result.get('password_hash')):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Current password is incorrect"
        )
    
    # Hash new password
    new_password_hash = hash_password(password_data.new_password)
    
    # Update password via stored procedure
    execute_procedure_single('sp_UpdatePassword', {'user_id': user_id, 'password_hash': new_password_hash})
    return {"message": "Password updated successfully"}


@router.delete("/{user_id}", status_code=status.HTTP_204_NO_CONTENT)
async def delete_user(user_id: int, current_user: dict = Depends(get_current_user)):
    """Delete a user (soft delete)"""
    # Only Admin and Owner can delete users
    if current_user['role_name'] not in ['Administrator', 'Owner']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can delete users"
        )
    
    # Cannot delete yourself
    if current_user['user_id'] == user_id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You cannot delete your own account"
        )
    
    # Soft delete via stored procedure
    execute_procedure_single('sp_SoftDeleteUser', {'user_id': user_id})
    return None

