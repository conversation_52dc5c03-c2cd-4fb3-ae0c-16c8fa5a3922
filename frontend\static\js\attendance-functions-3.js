/**
 * Attendance Management Functions - Part 3
 * Contains individual attendance, submission, and reporting functions
 */

async function showIndividualAttendanceModal(studentId, classId, date) {
    try {
        // Get student details
        const studentResponse = await fetch(SMS.apiUrl(`/students/${studentId}`), {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (!studentResponse.ok) {
            showError('Failed to load student details');
            return;
        }

        const student = await studentResponse.json();

        // Check if attendance already exists
        const attendanceResponse = await fetch(SMS.apiUrl(`/attendance/class/${classId}/students-for-marking?attendance_date=${date}`), {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        let existingStatus = 'Present';
        let existingRemarks = '';

        if (attendanceResponse.ok) {
            const students = await attendanceResponse.json();
            const existingRecord = students.find(s => s.student_id === studentId);
            if (existingRecord) {
                existingStatus = existingRecord.status || 'Present';
                existingRemarks = existingRecord.remarks || '';
            }
        }

        Swal.fire({
            title: `📋 Mark Attendance - ${student.full_name}`,
            html: `
                <div class="text-start">
                    <div class="alert alert-info">
                        <strong>Student:</strong> ${student.full_name}<br>
                        <strong>Admission #:</strong> ${student.admission_number || 'N/A'}<br>
                        <strong>Date:</strong> ${date}
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Attendance Status *</label>
                        <select class="form-control" id="individualAttendanceStatus">
                            <option value="Present" ${existingStatus === 'Present' ? 'selected' : ''}>✅ Present (1.0)</option>
                            <option value="Absent" ${existingStatus === 'Absent' ? 'selected' : ''}>❌ Absent (0.0)</option>
                            <option value="Late" ${existingStatus === 'Late' ? 'selected' : ''}>⏰ Late (1.0)</option>
                            <option value="Half-Day" ${existingStatus === 'Half-Day' ? 'selected' : ''}>🕛 Half-Day (0.5)</option>
                            <option value="Short Leave" ${existingStatus === 'Short Leave' ? 'selected' : ''}>⏱️ Short Leave (0.5)</option>
                            <option value="Leave" ${existingStatus === 'Leave' ? 'selected' : ''}>📝 Leave (0.0)</option>
                            <option value="Sick Leave" ${existingStatus === 'Sick Leave' ? 'selected' : ''}>🏥 Sick Leave (0.0)</option>
                            <option value="Casual Leave" ${existingStatus === 'Casual Leave' ? 'selected' : ''}>📅 Casual Leave (0.0)</option>
                            <option value="Holiday" ${existingStatus === 'Holiday' ? 'selected' : ''}>🎉 Holiday (1.0)</option>
                        </select>
                        <small class="text-muted">Numbers in parentheses show attendance value (1.0 = full day, 0.5 = half day, 0.0 = absent)</small>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Remarks (Optional)</label>
                        <textarea class="form-control" id="individualAttendanceRemarks" rows="2" placeholder="Enter any remarks...">${existingRemarks}</textarea>
                    </div>
                </div>
            `,
            showCancelButton: true,
            confirmButtonText: '💾 Save Attendance',
            cancelButtonText: 'Cancel',
            preConfirm: () => {
                const status = document.getElementById('individualAttendanceStatus').value;
                const remarks = document.getElementById('individualAttendanceRemarks').value;

                return { status, remarks };
            }
        }).then(async (result) => {
            if (result.isConfirmed) {
                await submitIndividualAttendance(studentId, classId, date, result.value.status, result.value.remarks);
            }
        });
    } catch (error) {
        console.error('Error showing individual attendance modal:', error);
        showError('Error loading student details');
    }
}

async function submitIndividualAttendance(studentId, classId, date, status, remarks) {
    try {
        const response = await fetch(SMS.apiUrl('/attendance/mark'), {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                student_id: studentId,
                class_id: classId,
                attendance_date: date,
                status: status,
                remarks: remarks
            })
        });

        if (response.ok) {
            const result = await response.json();
            showSuccess(result.message || 'Attendance marked successfully!');
        } else {
            const error = await response.json();
            showError(error.detail || 'Failed to mark attendance');
        }
    } catch (error) {
        console.error('Error submitting individual attendance:', error);
        showError('Error submitting attendance');
    }
}

async function submitAttendance(data) {
    try {
        const response = await fetch(SMS.apiUrl('/attendance/mark-bulk'), {
            method: 'POST',
            headers: {
                'Authorization': `Bearer ${authToken}`,
                'Content-Type': 'application/json'
            },
            body: JSON.stringify({
                class_id: data.classId,
                attendance_date: data.date,
                attendance_records: data.attendance
            })
        });

        if (response.ok) {
            const result = await response.json();
            showSuccess(result.message || 'Attendance marked successfully!');
            // Reload attendance records if we're on the attendance page
            if (document.getElementById('attendanceDate')) {
                loadAttendanceRecords();
            }
        } else {
            const error = await response.json();
            showError(error.detail || 'Failed to mark attendance');
        }
    } catch (error) {
        console.error('Error submitting attendance:', error);
        showError('Error submitting attendance');
    }
}

async function loadAttendanceRecords() {
    const date = document.getElementById('attendanceDate').value;
    const classId = document.getElementById('attendanceClassFilter').value;

    if (!date) {
        showError('Please select a date');
        return;
    }

    try {
        let url = SMS.apiUrl(`/attendance/daily-summary?attendance_date=${date}`);
        if (classId) {
            url = SMS.apiUrl(`/attendance/daily-summary?attendance_date=${date}&class_id=${classId}`);
        }

        const response = await fetch(url, {
            headers: {
                'Authorization': `Bearer ${authToken}`
            }
        });

        if (response.ok) {
            const records = await response.json();
            displayAttendanceRecords(records);
        } else {
            showError('Failed to load attendance records');
        }
    } catch (error) {
        console.error('Error loading attendance:', error);
        showError('Error loading attendance records');
    }
}

// Export functions to global scope
window.showIndividualAttendanceModal = showIndividualAttendanceModal;
window.submitIndividualAttendance = submitIndividualAttendance;
window.submitAttendance = submitAttendance;
window.loadAttendanceRecords = loadAttendanceRecords;
