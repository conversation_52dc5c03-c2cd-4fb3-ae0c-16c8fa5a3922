"""
Timetable Management Routes - School configuration, period templates, and class timetables
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from datetime import datetime
from auth import get_current_user, require_admin
from database import execute_query, execute_non_query, execute_procedure, execute_procedure_single
from models import (
    SchoolConfigCreate,
    SchoolConfigResponse,
    PeriodTemplateCreate,
    PeriodTemplateResponse,
    ClassTimetableCreate,
    ClassTimetableResponse,
    TeacherAvailabilityCheck,
    TimetableGridResponse
)
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


# =============================================
# School Configuration Management
# =============================================

@router.get("/config", response_model=List[SchoolConfigResponse])
async def get_school_config(
    current_user: dict = Depends(get_current_user)
):
    """Get all school configuration settings"""
    try:
        results = execute_procedure('sp_Config_GetAll')
        return results if results else []
    except Exception as e:
        logger.error(f"Error getting school config: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get school config: {str(e)}"
        )


@router.get("/config/{config_key}", response_model=SchoolConfigResponse)
async def get_school_config_by_key(
    config_key: str,
    current_user: dict = Depends(get_current_user)
):
    """Get specific configuration by key"""
    try:
        result = execute_procedure_single('sp_Config_GetByKey', {'config_key': config_key})
        if not result:
            raise HTTPException(status_code=404, detail="Configuration not found")
        return result
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error getting config: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get config: {str(e)}"
        )


@router.post("/config", status_code=status.HTTP_201_CREATED)
async def create_or_update_config(
    config: SchoolConfigCreate,
    current_user: dict = Depends(require_admin)
):
    """Create or update school configuration"""
    try:
        execute_procedure_single('sp_Config_Upsert', {
            'config_key': config.config_key,
            'config_value': config.config_value,
            'description': config.description,
            'updated_by': current_user['user_id']
        })
        return {"message": "Configuration saved successfully"}
    except Exception as e:
        logger.error(f"Error saving config: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to save config: {str(e)}"
        )


# =============================================
# Period Templates Management
# =============================================

@router.get("/periods/templates", response_model=List[PeriodTemplateResponse])
async def get_period_templates(
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get all period templates for a given academic year"""
    try:
        if not academic_year:
            academic_year = datetime.now().year

        results = execute_procedure('sp_PeriodTemplate_GetAll', {'academic_year': academic_year})
        return results if results else []
    except Exception as e:
        logger.error(f"Error getting period templates: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to get period templates: {str(e)}"
        )


@router.post("/periods/templates", status_code=status.HTTP_201_CREATED)
async def create_period_template(
    period: PeriodTemplateCreate,
    current_user: dict = Depends(require_admin)
):
    """Create a new period template"""
    try:
        execute_procedure_single('sp_PeriodTemplate_Create', {
            'period_number': period.period_number,
            'period_name': period.period_name,
            'start_time': period.start_time,
            'end_time': period.end_time,
            'is_teaching_period': period.is_teaching_period,
            'academic_year': period.academic_year
        })
        return {"message": "Period template created successfully"}
    except Exception as e:
        logger.error(f"Error creating period template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to create period template: {str(e)}"
        )


@router.put("/periods/templates/{template_id}")
async def update_period_template(
    template_id: int,
    period: PeriodTemplateCreate,
    current_user: dict = Depends(require_admin)
):
    """Update an existing period template"""
    try:
        execute_procedure_single('sp_PeriodTemplate_Update', {
            'template_id': template_id,
            'period_number': period.period_number,
            'period_name': period.period_name,
            'start_time': period.start_time,
            'end_time': period.end_time,
            'is_teaching_period': period.is_teaching_period
        })
        return {"message": "Period template updated successfully"}
    except Exception as e:
        logger.error(f"Error updating period template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to update period template: {str(e)}"
        )


@router.delete("/periods/templates/{template_id}")
async def delete_period_template(
    template_id: int,
    current_user: dict = Depends(require_admin)
):
    """Delete a period template (soft delete)"""
    try:
        execute_procedure_single('sp_PeriodTemplate_Delete', {'template_id': template_id})
        return {"message": "Period template deleted successfully"}
    except Exception as e:
        logger.error(f"Error deleting period template: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to delete period template: {str(e)}"
        )


# =============================================
# Class Timetable Management
# =============================================

@router.get("/classes/{class_id}", response_model=List[ClassTimetableResponse])
async def get_class_timetable(
    class_id: int,
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get complete timetable for a specific class"""
    try:
        if not academic_year:
            academic_year = datetime.now().year

        results = execute_procedure('sp_GetClassTimetable', {'class_id': class_id, 'academic_year': academic_year})
        return results if results else []
    except Exception as e:
        logger.warning(f"Stored procedure failed, using fallback: {e}")
        # Fallback to direct SQL query
        try:
            fallback_query = """
                SELECT
                    ct.timetable_id,
                    ct.class_id,
                    c.class_name,
                    c.section,
                    ct.day_of_week,
                    ct.period_number,
                    CONVERT(VARCHAR(8), ct.start_time, 108) as start_time,
                    CONVERT(VARCHAR(8), ct.end_time, 108) as end_time,
                    ct.subject_id,
                    s.subject_name,
                    ct.teacher_id,
                    u.full_name as teacher_name,
                    ct.academic_year,
                    NULL as period_name,
                    1 as is_teaching_period,
                    ct.is_active,
                    ct.created_at,
                    ct.updated_at
                FROM class_timetable ct
                INNER JOIN classes c ON ct.class_id = c.class_id
                LEFT JOIN subjects s ON ct.subject_id = s.subject_id
                LEFT JOIN staff st ON ct.teacher_id = st.staff_id
                LEFT JOIN users u ON st.user_id = u.user_id
                WHERE ct.class_id = ? AND ct.academic_year = ? AND ct.is_active = 1
                ORDER BY ct.day_of_week, ct.period_number
            """
            results = execute_query(fallback_query, (class_id, academic_year))
            return results if results else []
        except Exception as fallback_error:
            logger.error(f"Fallback query also failed: {fallback_error}")
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail=f"Failed to get class timetable: {str(fallback_error)}"
            )


@router.get("/teachers/{teacher_id}", response_model=List[ClassTimetableResponse])
async def get_teacher_timetable(
    teacher_id: int,
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get complete timetable for a specific teacher"""
    if not academic_year:
        academic_year = datetime.now().year

    try:
        results = execute_procedure('sp_GetTeacherTimetable', {'TeacherId': teacher_id, 'AcademicYear': academic_year})
        return results if results else []
    except Exception as e:
        logger.warning(f"Stored procedure failed, using fallback: {e}")
        # Fallback to direct SQL query
        try:
            fallback_query = """
                SELECT
                    ct.timetable_id, ct.class_id, c.class_name, c.section,
                    ct.subject_id, s.subject_name, ct.day_of_week, ct.period_number,
                    CONVERT(VARCHAR(8), ct.start_time, 108) as start_time,
                    CONVERT(VARCHAR(8), ct.end_time, 108) as end_time,
                    ct.academic_year
                FROM class_timetable ct
                JOIN classes c ON ct.class_id = c.class_id
                LEFT JOIN subjects s ON ct.subject_id = s.subject_id
                WHERE ct.teacher_id = ? AND ct.academic_year = ? AND ct.is_active = 1
                ORDER BY ct.day_of_week, ct.period_number
            """
            results = execute_query(fallback_query, (teacher_id, academic_year))
            return results if results else []
        except Exception as e2:
            logger.error(f"Fallback also failed: {e2}")
            return []


@router.get("/grid", response_model=List[TimetableGridResponse])
async def get_all_timetables_grid(
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get all class timetables in grid format"""
    if not academic_year:
        academic_year = datetime.now().year

    try:
        # Get all classes using stored procedure
        classes = execute_procedure('sp_Timetable_GetClasses', {'academic_year': academic_year})

        result = []
        for cls in classes:
            # Get timetable for this class
            timetable = execute_procedure('sp_GetClassTimetable', {'class_id': cls['class_id'], 'academic_year': academic_year})

            result.append({
                'class_id': cls['class_id'],
                'class_name': cls['class_name'],
                'section': cls['section'],
                'timetable': timetable if timetable else []
            })

        return result
    except Exception as e:
        logger.warning(f"Stored procedure failed, using fallback: {e}")
        # Fallback to direct SQL query
        try:
            classes_query = "SELECT class_id, class_name, section FROM classes WHERE is_active = 1 AND academic_year = ? ORDER BY class_name, section"
            classes = execute_query(classes_query, (academic_year,))

            result = []
            for cls in classes:
                result.append({
                    'class_id': cls['class_id'],
                    'class_name': cls['class_name'],
                    'section': cls['section'],
                    'timetable': []  # Empty timetable for now
                })
            return result
        except Exception as e2:
            logger.error(f"Fallback also failed: {e2}")
            return []


@router.post("/assign", status_code=status.HTTP_201_CREATED)
async def assign_class_period(
    timetable: ClassTimetableCreate,
    current_user: dict = Depends(require_admin)
):
    """Assign a teacher to a class period"""
    try:
        # Check if teacher is available
        if timetable.teacher_id:
            availability = execute_procedure_single('sp_CheckTeacherAvailability', {
                'TeacherId': timetable.teacher_id,
                'DayOfWeek': timetable.day_of_week,
                'PeriodNumber': timetable.period_number,
                'AcademicYear': timetable.academic_year
            })

            if availability and not availability.get('is_available', True):
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Teacher is already assigned to {availability.get('conflicting_class')} "
                           f"for {availability.get('conflicting_subject')} at this time"
                )

        # Check if period already exists for this class
        existing = execute_procedure_single('sp_Timetable_CheckPeriodExists', {
            'class_id': timetable.class_id,
            'day_of_week': timetable.day_of_week,
            'period_number': timetable.period_number,
            'academic_year': timetable.academic_year
        })

        if existing and existing.get('timetable_id'):
            # Update existing period
            execute_procedure_single('sp_Timetable_UpdatePeriod', {
                'timetable_id': existing['timetable_id'],
                'teacher_id': timetable.teacher_id,
                'subject_id': timetable.subject_id,
                'start_time': timetable.start_time,
                'end_time': timetable.end_time
            })
            return {"message": "Period updated successfully"}
        else:
            # Insert new period
            execute_procedure_single('sp_Timetable_AssignPeriod', {
                'class_id': timetable.class_id,
                'subject_id': timetable.subject_id,
                'teacher_id': timetable.teacher_id,
                'day_of_week': timetable.day_of_week,
                'period_number': timetable.period_number,
                'start_time': timetable.start_time,
                'end_time': timetable.end_time,
                'academic_year': timetable.academic_year
            })
            return {"message": "Period assigned successfully"}
    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error assigning period: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to assign period: {str(e)}"
        )


@router.delete("/assign/{timetable_id}")
async def remove_class_period(
    timetable_id: int,
    current_user: dict = Depends(require_admin)
):
    """Remove a period assignment"""
    try:
        execute_procedure_single('sp_Timetable_RemovePeriod', {'timetable_id': timetable_id})
        return {"message": "Period removed successfully"}
    except Exception as e:
        logger.error(f"Error removing period: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to remove period: {str(e)}"
        )


@router.post("/check-availability", response_model=TeacherAvailabilityCheck)
async def check_teacher_availability(
    teacher_id: int,
    day_of_week: int,
    period_number: int,
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """Check if a teacher is available for a specific period"""
    try:
        if not academic_year:
            academic_year = datetime.now().year

        result = execute_procedure_single('sp_CheckTeacherAvailability', {
            'TeacherId': teacher_id,
            'DayOfWeek': day_of_week,
            'PeriodNumber': period_number,
            'AcademicYear': academic_year
        })
        return result if result else {"is_available": True, "conflicting_class": None, "conflicting_subject": None}
    except Exception as e:
        logger.error(f"Error checking availability: {e}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to check availability: {str(e)}"
        )
