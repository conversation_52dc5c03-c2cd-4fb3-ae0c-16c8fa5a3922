"""
Exam Routes
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional
from models import (ExamCreate, ExamResultCreate, BulkMarksEntry, SuccessResponse,
                    ExamSubjectCreate, ExamMarksEntry)
from auth import get_current_user, require_teacher, require_admin
from services.exam_service import ExamService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/", response_model=SuccessResponse)
async def create_exam(
    exam: ExamCreate,
    current_user: dict = Depends(require_admin)
):
    """
    Create a new exam (Admin only)
    
    Args:
        exam: Exam data
        current_user: Current authenticated user
    
    Returns:
        Success response with exam ID
    """
    try:
        exam_id = ExamService.create_exam(exam)
        
        return SuccessResponse(
            message="Exam created successfully",
            data={"exam_id": exam_id}
        )
    except Exception as e:
        logger.error(f"Error creating exam: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to create exam: {str(e)}"
        )


@router.get("/", response_model=list)
async def get_exams(
    academic_year: Optional[int] = Query(None),
    exam_type: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get list of exams

    Args:
        academic_year: Optional academic year filter
        exam_type: Optional exam type filter
        current_user: Current authenticated user

    Returns:
        List of exams
    """
    exams = ExamService.get_exams(academic_year, exam_type)
    return exams


@router.put("/{exam_id}", response_model=SuccessResponse)
async def update_exam(
    exam_id: int,
    exam: ExamCreate,
    current_user: dict = Depends(require_admin)
):
    """
    Update an existing exam (Admin only)

    Args:
        exam_id: Exam ID to update
        exam: Updated exam data
        current_user: Current authenticated user

    Returns:
        Success response
    """
    try:
        ExamService.update_exam(exam_id, exam)

        return SuccessResponse(
            message="Exam updated successfully",
            data={"exam_id": exam_id}
        )
    except Exception as e:
        logger.error(f"Error updating exam: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to update exam: {str(e)}"
        )


@router.post("/results", response_model=SuccessResponse)
async def record_marks(
    result: ExamResultCreate,
    current_user: dict = Depends(require_teacher)
):
    """
    Record exam marks for a student (Teacher/Admin only)
    
    Args:
        result: Exam result data
        current_user: Current authenticated user
    
    Returns:
        Success response with grade
    """
    result_data = ExamService.record_marks(result, current_user['user_id'])
    
    if result_data and result_data.get('Result') == 'Success':
        return SuccessResponse(
            message=result_data.get('Message', 'Marks recorded successfully'),
            data={'grade': result_data.get('Grade')}
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result_data.get('Message', 'Failed to record marks')
        )


@router.get("/results/student/{student_id}", response_model=list)
async def get_student_results(
    student_id: int,
    exam_id: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get exam results for a student
    
    Args:
        student_id: Student ID
        exam_id: Optional exam ID filter
        current_user: Current authenticated user
    
    Returns:
        List of exam results
    """
    results = ExamService.get_student_results(student_id, exam_id)
    return results


@router.get("/results/exam/{exam_id}", response_model=list)
async def get_exam_results(
    exam_id: int,
    class_id: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all results for an exam
    
    Args:
        exam_id: Exam ID
        class_id: Optional class ID filter
        current_user: Current authenticated user
    
    Returns:
        List of exam results
    """
    results = ExamService.get_exam_results(exam_id, class_id)
    return results


@router.get("/report-card/{student_id}/{exam_id}", response_model=dict)
async def get_report_card(
    student_id: int,
    exam_id: int,
    current_user: dict = Depends(get_current_user)
):
    """
    Generate report card for a student
    
    Args:
        student_id: Student ID
        exam_id: Exam ID
        current_user: Current authenticated user
    
    Returns:
        Report card data
    """
    report_card = ExamService.get_report_card(student_id, exam_id)
    
    if not report_card:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Report card not found"
        )
    
    return report_card


@router.get("/performance/class/{class_id}", response_model=dict)
async def get_class_performance(
    class_id: int,
    exam_id: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get class performance statistics
    
    Args:
        class_id: Class ID
        exam_id: Exam ID
        current_user: Current authenticated user
    
    Returns:
        Performance statistics
    """
    performance = ExamService.get_class_performance(exam_id, class_id)
    return performance


@router.post("/promote", response_model=SuccessResponse)
async def promote_students(
    current_class_id: int = Query(...),
    next_class_id: int = Query(...),
    academic_year: int = Query(...),
    current_user: dict = Depends(require_admin)
):
    """
    Promote students to next class (Admin only)
    
    Args:
        current_class_id: Current class ID
        next_class_id: Next class ID
        academic_year: Academic year
        current_user: Current authenticated user
    
    Returns:
        Success response with promotion count
    """
    result = ExamService.promote_students(
        current_class_id,
        next_class_id,
        academic_year,
        current_user['user_id']
    )
    
    if result and result.get('Result') == 'Success':
        return SuccessResponse(
            message=result.get('Message', 'Students promoted successfully'),
            data={'promoted_count': result.get('PromotedCount')}
        )
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=result.get('Message', 'Failed to promote students')
        )


@router.get("/top-performers/{exam_id}", response_model=list)
async def get_top_performers(
    exam_id: int,
    class_id: Optional[int] = Query(None),
    limit: int = Query(10, ge=1, le=50),
    current_user: dict = Depends(get_current_user)
):
    """
    Get top performing students
    
    Args:
        exam_id: Exam ID
        class_id: Optional class ID filter
        limit: Number of top students to return
        current_user: Current authenticated user
    
    Returns:
        List of top performers
    """
    top_performers = ExamService.get_top_performers(exam_id, class_id, limit)
    return top_performers

@router.post("/marks/bulk", response_model=SuccessResponse)
async def bulk_marks_entry(
    bulk_data: BulkMarksEntry,
    current_user: dict = Depends(get_current_user)
):
    """
    Bulk marks entry for a class (Teacher/Admin only)

    Args:
        bulk_data: Bulk marks data
        current_user: Current authenticated user

    Returns:
        Success response with entry statistics
    """
    try:
        result = ExamService.bulk_marks_entry(bulk_data, current_user['user_id'])

        status_msg = "draft" if bulk_data.is_draft else "final"
        message = f"Marks entry completed: {result['success_count']}/{result['total_count']} students ({status_msg})"

        return SuccessResponse(
            message=message,
            data=result
        )
    except Exception as e:
        logger.error(f"Error in bulk marks entry: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to enter marks: {str(e)}"
        )


@router.get("/marks/class/{class_id}/students", response_model=list)
async def get_class_students_for_marks(
    class_id: int,
    exam_id: int = Query(...),
    subject_id: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get list of students in a class for marks entry (Teacher/Admin only)

    Args:
        class_id: Class ID
        exam_id: Exam ID
        subject_id: Subject ID
        current_user: Current authenticated user

    Returns:
        List of students with existing marks
    """
    students = ExamService.get_class_students_for_marks_entry(exam_id, class_id, subject_id)
    return students


@router.get("/performance/subject/{subject_id}", response_model=dict)
async def get_subject_performance(
    subject_id: int,
    exam_id: int = Query(...),
    class_id: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get performance summary for a subject

    Args:
        subject_id: Subject ID
        exam_id: Exam ID
        class_id: Class ID
        current_user: Current authenticated user

    Returns:
        Performance summary
    """
    summary = ExamService.get_subject_performance_summary(exam_id, class_id, subject_id)

    if not summary:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="No performance data found"
        )

    return summary


@router.post("/subjects/add", response_model=SuccessResponse)
async def add_exam_subjects(
    exam_subject: ExamSubjectCreate,
    current_user: dict = Depends(require_admin)
):
    """
    Add subjects to an exam

    Args:
        exam_subject: Exam subject mapping data
        current_user: Current authenticated user

    Returns:
        Success response
    """
    try:
        result = ExamService.add_exam_subjects(exam_subject)
        return SuccessResponse(
            message=result.get('message', 'Exam subject added successfully'),
            data=result
        )
    except Exception as e:
        logger.error(f"Error adding exam subjects: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to add exam subjects: {str(e)}"
        )


@router.post("/marks/enter", response_model=SuccessResponse)
async def enter_exam_marks(
    marks_entry: ExamMarksEntry,
    current_user: dict = Depends(get_current_user)
):
    """
    Enter or update marks for a student in a subject

    Args:
        marks_entry: Marks entry data
        current_user: Current authenticated user

    Returns:
        Success response with grade and percentage
    """
    try:
        result = ExamService.enter_exam_marks(marks_entry, current_user['user_id'])
        return SuccessResponse(
            message=result.get('message', 'Marks entered successfully'),
            data=result
        )
    except Exception as e:
        logger.error(f"Error entering exam marks: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to enter exam marks: {str(e)}"
        )


@router.get("/{exam_id}/class/{class_id}/students-marks", response_model=list)
async def get_students_for_marks_entry(
    exam_id: int,
    class_id: int,
    subject_id: int = Query(...),
    current_user: dict = Depends(get_current_user)
):
    """
    Get all students in a class with their marks for a subject

    Args:
        exam_id: Exam ID
        class_id: Class ID
        subject_id: Subject ID
        current_user: Current authenticated user

    Returns:
        List of students with their marks
    """
    try:
        students = ExamService.get_students_for_marks_entry(exam_id, class_id, subject_id)
        return students if students else []
    except Exception as e:
        logger.error(f"Error getting students for marks entry: {e}", exc_info=True)
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"Failed to load students: {str(e)}"
        )


@router.get("/{exam_id}/subjects", response_model=list)
async def get_exam_subjects(
    exam_id: int,
    class_id: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get subjects for an exam

    Args:
        exam_id: Exam ID
        class_id: Optional class ID filter
        current_user: Current authenticated user

    Returns:
        List of subjects in the exam
    """
    try:
        subjects = ExamService.get_exam_subjects(exam_id, class_id)
        return subjects
    except Exception as e:
        logger.error(f"Error getting exam subjects: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get exam subjects: {str(e)}"
        )


@router.post("/{exam_id}/calculate-summary/{student_id}", response_model=SuccessResponse)
async def calculate_student_summary(
    exam_id: int,
    student_id: int,
    current_user: dict = Depends(require_teacher)
):
    """
    Calculate overall exam summary for a student

    Args:
        exam_id: Exam ID
        student_id: Student ID
        current_user: Current authenticated user

    Returns:
        Success response with summary data
    """
    try:
        result = ExamService.calculate_student_summary(exam_id, student_id)
        return SuccessResponse(
            message=result.get('message', 'Summary calculated successfully'),
            data=result
        )
    except Exception as e:
        logger.error(f"Error calculating student summary: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to calculate summary: {str(e)}"
        )


@router.get("/{exam_id}/results", response_model=list)
async def get_exam_results(
    exam_id: int,
    class_id: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get exam results for all students

    Args:
        exam_id: Exam ID
        class_id: Optional class ID filter
        current_user: Current authenticated user

    Returns:
        List of student exam summaries
    """
    try:
        results = ExamService.get_exam_results(exam_id, class_id)
        return results
    except Exception as e:
        logger.error(f"Error getting exam results: {e}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=f"Failed to get exam results: {str(e)}"
        )


