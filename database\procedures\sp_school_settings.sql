-- =============================================
-- School Settings Stored Procedures
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Get School Configuration
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetSchoolConfig]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetSchoolConfig;
GO

CREATE PROCEDURE sp_GetSchoolConfig
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT
        config_key,
        config_value,
        'string' as config_type,
        description,
        1 as is_active
    FROM school_config
    ORDER BY config_key;
END
GO

-- =============================================
-- Update School Configuration
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_UpdateSchoolConfig]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_UpdateSchoolConfig;
GO

CREATE PROCEDURE sp_UpdateSchoolConfig
    @config_key NVARCHAR(100),
    @config_value NVARCHAR(MAX),
    @updated_by INT
AS
BEGIN
    SET NOCOUNT ON;
    
    IF EXISTS (SELECT 1 FROM school_config WHERE config_key = @config_key)
    BEGIN
        UPDATE school_config 
        SET 
            config_value = @config_value,
            updated_at = GETDATE(),
            updated_by = @updated_by
        WHERE config_key = @config_key;
    END
    ELSE
    BEGIN
        INSERT INTO school_config (config_key, config_value, description, updated_at, updated_by)
        VALUES (@config_key, @config_value, 'Configuration setting', GETDATE(), @updated_by);
    END
    
    SELECT 'Configuration updated successfully' as message;
END
GO

-- =============================================
-- Initialize Default School Configuration
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_InitializeSchoolConfig]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_InitializeSchoolConfig;
GO

CREATE PROCEDURE sp_InitializeSchoolConfig
AS
BEGIN
    SET NOCOUNT ON;
    
    -- Insert default configuration if not exists
    IF NOT EXISTS (SELECT 1 FROM school_config WHERE config_key = 'school_name')
    BEGIN
        INSERT INTO school_config (config_key, config_value, description, updated_at) VALUES
        ('school_name', 'ABC School System', 'Name of the school', GETDATE()),
        ('school_address', '123 Education Street, City', 'School address', GETDATE()),
        ('school_phone', '+92-300-1234567', 'School contact number', GETDATE()),
        ('school_email', '<EMAIL>', 'School email address', GETDATE()),
        ('academic_year', '2025', 'Current academic year', GETDATE()),
        ('school_start_time', '08:30', 'School start time', GETDATE()),
        ('school_end_time', '14:30', 'School end time', GETDATE()),
        ('periods_per_day', '8', 'Number of periods per day', GETDATE()),
        ('period_duration', '40', 'Duration of each period in minutes', GETDATE()),
        ('break_duration', '10', 'Break duration in minutes', GETDATE()),
        ('lunch_duration', '30', 'Lunch break duration in minutes', GETDATE()),
        ('short_break_after_period', '2', 'Short break after which period', GETDATE()),
        ('lunch_after_period', '4', 'Lunch break after which period', GETDATE());
        
        SELECT 'Default school configuration initialized' as message;
    END
    ELSE
    BEGIN
        SELECT 'School configuration already exists' as message;
    END
END
GO

-- =============================================
-- Get Time Slot Configuration
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetTimeSlotConfig]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetTimeSlotConfig;
GO

CREATE PROCEDURE sp_GetTimeSlotConfig
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT
        config_key,
        config_value
    FROM school_config
    WHERE config_key IN (
        'school_start_time', 'school_end_time', 'periods_per_day',
        'period_duration', 'break_duration', 'lunch_duration',
        'short_break_after_period', 'lunch_after_period'
    );
END
GO

-- Initialize default configuration
EXEC sp_InitializeSchoolConfig;

PRINT 'School settings stored procedures created successfully!';
GO
