param(
    [string]$Server = "localhost\\MSSQLSERVER",
    [string]$User = "sa",
    [string]$Password = "YourStrong!Passw0rd",
    [string]$Database = "SMS"
)

$ErrorActionPreference = "Stop"
$here = Split-Path -Parent $MyInvocation.MyCommand.Definition
$root = Resolve-Path (Join-Path $here "..")
$sqlRoot = Join-Path $root "sql"
$procDir = Join-Path $sqlRoot "procedures"

Write-Host "Creating database if needed..." -ForegroundColor Cyan
sqlcmd -S $Server -U $User -P $Password -b -i (Join-<PERSON> $sqlRoot "01_create_database.sql")

$files = @(
    "02_tables_core.sql",
    "03_tables_students_classes.sql",
    "04_tables_fees.sql",
    "05_tables_academics.sql",
    "06_indexes.sql",
    "07_seed_data.sql"
)

foreach ($f in $files) {
    $path = Join-Path $sqlRoot $f
    Write-Host "Applying $f..." -ForegroundColor Cyan
    sqlcmd -S $Server -U $User -P $Password -d $Database -b -i $path
}

Write-Host "Applying stored procedures..." -ForegroundColor Cyan
Get-ChildItem $procDir -Filter *.sql | Sort-Object Name | ForEach-Object {
    Write-Host "  -> $($_.Name)" -ForegroundColor DarkCyan
    sqlcmd -S $Server -U $User -P $Password -d $Database -b -i $_.FullName
}

Write-Host "Database initialization complete." -ForegroundColor Green

