# SMS File Mapping Guide

## Overview
This document explains how to use the `SMS_File_Mapping.xlsx` file to efficiently make changes to your School Management System.

## Excel Sheets Explanation

### 1. Module Overview
- **Purpose**: High-level view of all modules in the SMS system
- **Use Case**: When you want to understand which files are involved in a specific module (e.g., Dashboard, Students, Fees)
- **Columns**:
  - Module: The functional area (Dashboard, Students, etc.)
  - Frontend_JS: JavaScript files for this module
  - Backend_Route: Python route files
  - Stored_Procedures: Database stored procedures
  - HTML_Template: Template sections
  - CSS_Classes: Relevant CSS classes
  - API_Endpoints: API endpoints used
  - Description: What this module does

### 2. File Relationships
- **Purpose**: Shows detailed file-to-file dependencies
- **Use Case**: When you need to trace how a change in one file affects others
- **Columns**:
  - Source_File: The file making the call/reference
  - Target_File: The file being called/referenced
  - Relationship: Type of relationship (API Call, SP Call, etc.)
  - Function/Endpoint: Specific function or endpoint involved

### 3. File Inventory
- **Purpose**: Complete list of all files with their purpose and dependencies
- **Use Case**: When you need to understand what each file does and what it depends on
- **Columns**:
  - File_Path: Full path to the file
  - Type: File type (Frontend JS, Backend Python, Database SP, etc.)
  - Module: Which module this file belongs to
  - Purpose: What this file does
  - Dependencies: What other files this depends on

### 4. Change Guide
- **Purpose**: Quick reference for common types of changes
- **Use Case**: When you want to make a specific type of change and need to know which files to modify
- **Columns**:
  - Change_Type: Type of change you want to make
  - Frontend_Changes: Which frontend files to modify
  - Backend_Changes: Which backend files to modify
  - Database_Changes: Which database files/procedures to modify
  - Testing_Files: What to test after making changes
  - Notes: Important considerations

## How to Use This Mapping

### Scenario 1: Adding a New Field to Students
1. Go to **Change Guide** sheet
2. Look for "Add New Student Field" row
3. Follow the files listed in each column
4. Make changes in the order: Database → Backend → Frontend → Testing

### Scenario 2: Modifying Dashboard Statistics
1. Check **Module Overview** for Dashboard module
2. See all related files at a glance
3. Use **File Relationships** to understand data flow
4. Make changes following the dependency chain

### Scenario 3: Understanding Cross-Module Dependencies
1. Use **File Relationships** sheet
2. Filter by source file to see what it calls
3. Filter by target file to see what calls it
4. Plan changes considering all dependencies

## Best Practices

1. **Always check dependencies** before making changes
2. **Follow the three-tier pattern**: Database → Backend → Frontend
3. **Test thoroughly** using the testing guidance in Change Guide
4. **Update stored procedures first** before modifying backend code
5. **Consider cross-module impacts** especially for shared utilities

## File Naming Conventions

- **Frontend JS**: `module-name.js` or `module-name.module.js`
- **Backend Routes**: `module-name_routes.py`
- **Stored Procedures**: `sp_module-name_management.sql`
- **Modular files**: Use `.module.js` for newer modular architecture

## Quick Reference

### Most Important Core Files
- `frontend/static/js/core.module.js` - Core functionality
- `backend/main.py` - Main application entry
- `backend/database.py` - Database utilities
- `backend/auth.py` - Authentication

### Most Connected Files (High Impact)
- `dashboard.js` - Calls multiple modules
- `auth.js` - Used by all modules
- `api.js` - Used by all frontend modules
- `database.py` - Used by all backend routes

This mapping will help you make changes efficiently while understanding the full impact of your modifications.
