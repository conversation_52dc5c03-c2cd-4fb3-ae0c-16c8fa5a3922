// Exams Management Module
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    // =============================================
    // EXAM MANAGEMENT
    // =============================================

    SMS.loadExamsPage = async function() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-file-alt"></i> Exam Management</h2>
                <div>
                    <button class="btn btn-success me-2" onclick="showEnterMarksInterface()">
                        <i class="fas fa-pen"></i> Enter Marks
                    </button>
                    <button class="btn btn-primary" onclick="showAddExamModal()">
                        <i class="fas fa-plus"></i> Create Exam
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label>Academic Year</label>
                            <input type="number" class="form-control" id="examYearFilter" value="${new Date().getFullYear()}" min="2020" max="2030">
                        </div>
                        <div class="col-md-4">
                            <label>Exam Type</label>
                            <select class="form-control" id="examTypeFilter">
                                <option value="">All Types</option>
                                <option value="Monthly">Monthly</option>
                                <option value="Mid-Term">Mid-Term</option>
                                <option value="Final">Final</option>
                                <option value="Quiz">Quiz</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label>&nbsp;</label>
                            <button class="btn btn-primary w-100" onclick="loadExams()">
                                <i class="fas fa-search"></i> Search Exams
                            </button>
                        </div>
                    </div>
                    <div id="examsTableContainer">
                        <p class="text-center text-muted">Click "Search Exams" to view exams</p>
                    </div>
                </div>
            </div>
        `;

        // Auto-load exams for current year
        await loadExams();
    };

    async function loadExams() {
        const year = document.getElementById('examYearFilter').value;
        const type = document.getElementById('examTypeFilter').value;

        showLoadingInline('examsTableContainer', 'Loading exams...');

        try {
            const params = [];
            if (year) params.push(`academic_year=${year}`);
            if (type) params.push(`exam_type=${type}`);

            let path = '/exams';
            if (params.length > 0) {
                path += '?' + params.join('&');
            }

            const url = SMS.apiUrl(path);

            const response = await fetch(url, {
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`
                }
            });

            if (response.ok) {
                const exams = await response.json();
                displayExamsTable(exams);
            } else {
                const error = await response.json();
                showError('Failed to load exams', error.detail);
                document.getElementById('examsTableContainer').innerHTML = `
                    <div class="alert alert-danger">
                        <i class="fas fa-exclamation-triangle"></i> Failed to load exams. Please try again.
                    </div>
                `;
            }
        } catch (error) {
            console.error('Error loading exams:', error);
            showError('Error loading exams', 'Unable to connect to server');
            document.getElementById('examsTableContainer').innerHTML = `
                <div class="alert alert-danger">
                    <i class="fas fa-exclamation-triangle"></i> Error loading exams. Please check your connection.
                </div>
            `;
        }
    }

    function displayExamsTable(exams) {
        const container = document.getElementById('examsTableContainer');

        if (exams.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">No exams found. Create a new exam to get started.</p>';
            return;
        }

        let html = `
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead class="table-dark">
                        <tr>
                            <th>Exam Name</th>
                            <th>Type</th>
                            <th>Term</th>
                            <th>Academic Year</th>
                            <th>Dates</th>
                            <th>Marks</th>
                            <th>Weightage</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        exams.forEach(exam => {
            const statusBadge = exam.is_active ?
                '<span class="badge bg-success">Active</span>' :
                '<span class="badge bg-secondary">Inactive</span>';

            const dates = exam.start_date && exam.end_date ?
                `${exam.start_date} to ${exam.end_date}` :
                'Not Set';

            const weightage = exam.weightage ? `${exam.weightage}%` : 'N/A';

            html += `
                <tr>
                    <td><strong>${exam.exam_name}</strong></td>
                    <td><span class="badge bg-primary">${exam.exam_type}</span></td>
                    <td><span class="badge bg-info">${exam.term_type || 'N/A'}</span></td>
                    <td>${exam.academic_year}</td>
                    <td><small>${dates}</small></td>
                    <td>${exam.total_marks} (Pass: ${exam.passing_marks})</td>
                    <td>${weightage}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <button class="btn btn-sm btn-success" onclick="showMarksEntryModal(${exam.exam_id}, '${exam.exam_name}')" title="Enter Marks">
                            <i class="fas fa-pen"></i>
                        </button>
                        <button class="btn btn-sm btn-info" onclick="viewExamResults(${exam.exam_id}, '${exam.exam_name}')" title="View Results">
                            <i class="fas fa-chart-bar"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="editExam(${exam.exam_id})" title="Edit">
                            <i class="fas fa-edit"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;
    }

    async function showAddExamModal() {
        const { value: formValues} = await Swal.fire({
            title: 'Create New Exam',
            html: `
                <div class="text-start">
                    <div class="mb-3">
                        <label class="form-label">Exam Name *</label>
                        <input type="text" id="examName" class="swal2-input" placeholder="e.g., First Term Exam" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Exam Type *</label>
                            <select id="examType" class="swal2-select" required>
                                <option value="">Select Type</option>
                                <option value="Monthly">Monthly Test</option>
                                <option value="Mid-Term">Mid-Term</option>
                                <option value="Final">Final</option>
                                <option value="Quiz">Quiz</option>
                                <option value="Practical">Practical</option>
                            </select>
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Term *</label>
                            <select id="examTerm" class="swal2-select" required>
                                <option value="">Select Term</option>
                                <option value="First Term">First Term</option>
                                <option value="Second Term">Second Term</option>
                                <option value="Final Term">Final Term</option>
                            </select>
                        </div>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Academic Year *</label>
                        <input type="number" id="examYear" class="swal2-input" value="${new Date().getFullYear()}" min="2020" max="2030" required>
                    </div>
                    <div class="row">
                        <div class="col-md-6 mb-3">
                            <label class="form-label">Start Date</label>
                            <input type="date" id="examStartDate" class="swal2-input">
                        </div>
                        <div class="col-md-6 mb-3">
                            <label class="form-label">End Date</label>
                            <input type="date" id="examEndDate" class="swal2-input">
                        </div>
                    </div>
                    <div class="row">
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Total Marks *</label>
                            <input type="number" id="examTotalMarks" class="swal2-input" value="100" min="1" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Passing Marks *</label>
                            <input type="number" id="examPassingMarks" class="swal2-input" value="40" min="1" required>
                        </div>
                        <div class="col-md-4 mb-3">
                            <label class="form-label">Weightage (%)</label>
                            <input type="number" id="examWeightage" class="swal2-input" placeholder="e.g., 30" min="0" max="100">
                        </div>
                    </div>
                </div>
            `,
            width: '600px',
            showCancelButton: true,
            confirmButtonText: 'Create Exam',
            preConfirm: () => {
                const name = document.getElementById('examName').value;
                const type = document.getElementById('examType').value;
                const term = document.getElementById('examTerm').value;
                const year = document.getElementById('examYear').value;
                const totalMarks = document.getElementById('examTotalMarks').value;
                const passingMarks = document.getElementById('examPassingMarks').value;

                if (!name || !type || !term || !year || !totalMarks || !passingMarks) {
                    Swal.showValidationMessage('Please fill all required fields');
                    return false;
                }

                return {
                    exam_name: name,
                    exam_type: type,
                    term_type: term,
                    academic_year: parseInt(year),
                    start_date: document.getElementById('examStartDate').value || null,
                    end_date: document.getElementById('examEndDate').value || null,
                    total_marks: parseInt(totalMarks),
                    passing_marks: parseInt(passingMarks),
                    weightage: document.getElementById('examWeightage').value ? parseFloat(document.getElementById('examWeightage').value) : null,
                    is_active: true
                };
            }
        });

        if (formValues) {
            await createExam(formValues);
        }
    }

    async function createExam(examData) {
        try {
            const response = await fetch(SMS.apiUrl('/exams'), {
                method: 'POST',
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`,
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(examData)
            });

            if (response.ok) {
                showSuccess('Exam created successfully!');
                loadExams();
            } else {
                const error = await response.json();
                showError('Failed to create exam', error.detail);
            }
        } catch (error) {
            console.error('Error creating exam:', error);
            showError('Error creating exam');
        }
    }

    function getGradeBadgeColor(grade) {
        const gradeColors = {
            'A+': 'success',
            'A': 'success',
            'B': 'primary',
            'C': 'info',
            'D': 'warning',
            'E': 'warning',
            'F': 'danger'
        };
        return gradeColors[grade] || 'secondary';
    }

    async function editExam(examId) {
        try {
            const response = await fetch(SMS.apiUrl(`/exams/`), {
                headers: { 'Authorization': `Bearer ${SMS.authToken}` }
            });

            if (!response.ok) throw new Error('Failed to load exam details');

            const exams = await response.json();
            const exam = exams.find(e => e.exam_id === examId);
            if (!exam) throw new Error('Exam not found');

            const { value: formValues } = await Swal.fire({
                title: 'Edit Exam',
                html: `
                    <form id="editExamForm" class="text-start">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Exam Name *</label>
                                <input type="text" class="form-control" id="editExamName" value="${exam.exam_name}" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Exam Name (Urdu)</label>
                                <input type="text" class="form-control" id="editExamNameUrdu" value="${exam.exam_name_urdu || ''}" dir="rtl">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Exam Type *</label>
                                <select class="form-control" id="editExamType" required>
                                    <option value="Monthly" ${exam.exam_type === 'Monthly' ? 'selected' : ''}>Monthly</option>
                                    <option value="Mid-Term" ${exam.exam_type === 'Mid-Term' ? 'selected' : ''}>Mid-Term</option>
                                    <option value="Final" ${exam.exam_type === 'Final' ? 'selected' : ''}>Final</option>
                                    <option value="Quiz" ${exam.exam_type === 'Quiz' ? 'selected' : ''}>Quiz</option>
                                    <option value="Practical" ${exam.exam_type === 'Practical' ? 'selected' : ''}>Practical</option>
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">Term Type</label>
                                <select class="form-control" id="editTermType">
                                    <option value="">Select Term</option>
                                    <option value="First Term" ${exam.term_type === 'First Term' ? 'selected' : ''}>First Term</option>
                                    <option value="Second Term" ${exam.term_type === 'Second Term' ? 'selected' : ''}>Second Term</option>
                                    <option value="Final Term" ${exam.term_type === 'Final Term' ? 'selected' : ''}>Final Term</option>
                                </select>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Academic Year *</label>
                                <input type="number" class="form-control" id="editAcademicYear" value="${exam.academic_year}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Start Date</label>
                                <input type="date" class="form-control" id="editStartDate" value="${exam.start_date || ''}">
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">End Date</label>
                                <input type="date" class="form-control" id="editEndDate" value="${exam.end_date || ''}">
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Total Marks *</label>
                                <input type="number" class="form-control" id="editTotalMarks" value="${exam.total_marks}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Passing Marks *</label>
                                <input type="number" class="form-control" id="editPassingMarks" value="${exam.passing_marks}" required>
                            </div>
                            <div class="col-md-4 mb-3">
                                <label class="form-label">Weightage (%)</label>
                                <input type="number" step="0.01" class="form-control" id="editWeightage" value="${exam.weightage || ''}">
                            </div>
                        </div>
                        <div class="mb-3">
                            <div class="form-check">
                                <input type="checkbox" class="form-check-input" id="editIsActive" ${exam.is_active ? 'checked' : ''}>
                                <label class="form-check-label" for="editIsActive">Active</label>
                            </div>
                        </div>
                    </form>
                `,
                width: '800px',
                showCancelButton: true,
                confirmButtonText: 'Update Exam',
                preConfirm: () => {
                    const examName = document.getElementById('editExamName').value;
                    const examType = document.getElementById('editExamType').value;
                    const academicYear = document.getElementById('editAcademicYear').value;
                    const totalMarks = document.getElementById('editTotalMarks').value;
                    const passingMarks = document.getElementById('editPassingMarks').value;

                    if (!examName || !examType || !academicYear || !totalMarks || !passingMarks) {
                        Swal.showValidationMessage('Please fill all required fields');
                        return false;
                    }

                    return {
                        exam_name: examName,
                        exam_name_urdu: document.getElementById('editExamNameUrdu').value || null,
                        exam_type: examType,
                        term_type: document.getElementById('editTermType').value || null,
                        academic_year: parseInt(academicYear),
                        start_date: document.getElementById('editStartDate').value || null,
                        end_date: document.getElementById('editEndDate').value || null,
                        total_marks: parseInt(totalMarks),
                        passing_marks: parseInt(passingMarks),
                        weightage: parseFloat(document.getElementById('editWeightage').value) || null,
                        is_active: document.getElementById('editIsActive').checked
                    };
                }
            });

            if (formValues) {
                const updateResponse = await fetch(SMS.apiUrl(`/exams/${examId}`), {
                    method: 'PUT',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${SMS.authToken}`
                    },
                    body: JSON.stringify(formValues)
                });

                if (!updateResponse.ok) {
                    const error = await updateResponse.json();
                    throw new Error(error.detail || 'Failed to update exam');
                }

                showSuccess('Exam updated successfully!');
                SMS.loadExamsPage();
            }
        } catch (error) {
            console.error('Error editing exam:', error);
            showError(`Failed to edit exam: ${error.message}`);
        }
    }

    // =============================================
    // Bulk Marks Entry Functions
    // =============================================

    async function showMarksEntryModal(examId, examName) {
        const classes = await fetchClasses();
        const subjects = await fetchSubjects();

        const { value: formValues } = await Swal.fire({
            title: `Enter Marks - ${examName}`,
            html: `
                <div class="text-start">
                    <div class="mb-3">
                        <label class="form-label">Select Class *</label>
                        <select id="marksClass" class="swal2-select" required>
                            <option value="">Select Class</option>
                            ${classes.map(c => `<option value="${c.class_id}">${c.class_name} - ${c.section}</option>`).join('')}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Select Subject *</label>
                        <select id="marksSubject" class="swal2-select" required>
                            <option value="">Select Subject</option>
                            ${subjects.map(s => `<option value="${s.subject_id}">${s.subject_name}</option>`).join('')}
                        </select>
                    </div>
                </div>
            `,
            width: '500px',
            showCancelButton: true,
            confirmButtonText: 'Continue',
            preConfirm: () => {
                const classId = document.getElementById('marksClass').value;
                const subjectId = document.getElementById('marksSubject').value;

                if (!classId || !subjectId) {
                    Swal.showValidationMessage('Please select both class and subject');
                    return false;
                }

                return { classId: parseInt(classId), subjectId: parseInt(subjectId) };
            }
        });

        if (formValues) {
            await showBulkMarksEntryTable(examId, examName, formValues.classId, formValues.subjectId);
        }
    }

    async function showBulkMarksEntryTable(examId, examName, classId, subjectId) {
        try {
            const response = await fetch(SMS.apiUrl(`/exams/${examId}/class/${classId}/students-marks?subject_id=${subjectId}`), {
                headers: { 'Authorization': `Bearer ${SMS.authToken}` }
            });

            if (!response.ok) throw new Error('Failed to load students');

            const students = await response.json();

            if (students.length === 0) {
                Swal.fire({ icon: 'info', title: 'No Students', text: 'No students found in this class' });
                return;
            }

            let tableHtml = `
                <div class="marks-entry-container" style="max-height: 500px; overflow-y: auto;">
                    <table class="table table-sm table-bordered">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th>Adm. No.</th>
                                <th>Student Name</th>
                                <th>Theory</th>
                                <th>Practical</th>
                                <th>Oral</th>
                                <th>Assignment</th>
                                <th>Total</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            students.forEach((student, index) => {
                const theoryMarks = student.theory_marks || '';
                const practicalMarks = student.practical_marks || '';
                const oralMarks = student.oral_marks || '';
                const assignmentMarks = student.assignment_marks || '';
                const total = student.obtained_marks || 0;

                tableHtml += `
                    <tr>
                        <td>${student.admission_number}</td>
                        <td>${student.full_name}</td>
                        <td><input type="number" class="form-control form-control-sm marks-input"
                            id="theory_${index}" data-student-id="${student.student_id}"
                            value="${theoryMarks}" min="0" max="100" placeholder="0"></td>
                        <td><input type="number" class="form-control form-control-sm marks-input"
                            id="practical_${index}" data-student-id="${student.student_id}"
                            value="${practicalMarks}" min="0" max="100" placeholder="0"></td>
                        <td><input type="number" class="form-control form-control-sm marks-input"
                            id="oral_${index}" data-student-id="${student.student_id}"
                            value="${oralMarks}" min="0" max="100" placeholder="0"></td>
                        <td><input type="number" class="form-control form-control-sm marks-input"
                            id="assignment_${index}" data-student-id="${student.student_id}"
                            value="${assignmentMarks}" min="0" max="100" placeholder="0"></td>
                        <td><strong id="total_${index}">${total}</strong></td>
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
                <div class="mt-3">
                    <label class="form-check-label">
                        <input type="checkbox" id="saveDraft" class="form-check-input">
                        Save as Draft (not final)
                    </label>
                </div>
            `;

            const { value: confirmed } = await Swal.fire({
                title: `Bulk Marks Entry - ${examName}`,
                html: tableHtml,
                width: '90%',
                showCancelButton: true,
                confirmButtonText: 'Submit Marks',
                cancelButtonText: 'Cancel',
                didOpen: () => {
                    document.querySelectorAll('.marks-input').forEach(input => {
                        input.addEventListener('input', (e) => {
                            const index = e.target.id.split('_')[1];
                            calculateTotal(index);
                        });
                    });
                },
                preConfirm: () => {
                    const marksData = [];
                    students.forEach((student, index) => {
                        const theory = parseFloat(document.getElementById(`theory_${index}`).value) || 0;
                        const practical = parseFloat(document.getElementById(`practical_${index}`).value) || 0;
                        const oral = parseFloat(document.getElementById(`oral_${index}`).value) || 0;
                        const assignment = parseFloat(document.getElementById(`assignment_${index}`).value) || 0;

                        marksData.push({
                            student_id: student.student_id,
                            theory_marks: theory || null,
                            practical_marks: practical || null,
                            oral_marks: oral || null,
                            assignment_marks: assignment || null,
                            total_marks: 100
                        });
                    });

                    return {
                        marks_data: marksData,
                        is_draft: document.getElementById('saveDraft').checked
                    };
                }
            });

            if (confirmed) {
                await submitBulkMarks(examId, classId, subjectId, confirmed.marks_data, confirmed.is_draft);
            }

        } catch (error) {
            console.error('Error loading marks entry:', error);
            Swal.fire({ icon: 'error', title: 'Error', text: error.message || 'Failed to load marks entry form' });
        }
    }

    function calculateTotal(index) {
        const theory = parseFloat(document.getElementById(`theory_${index}`).value) || 0;
        const practical = parseFloat(document.getElementById(`practical_${index}`).value) || 0;
        const oral = parseFloat(document.getElementById(`oral_${index}`).value) || 0;
        const assignment = parseFloat(document.getElementById(`assignment_${index}`).value) || 0;

        const total = theory + practical + oral + assignment;
        document.getElementById(`total_${index}`).textContent = total.toFixed(2);
    }

    async function submitBulkMarks(examId, classId, subjectId, marksData, isDraft) {
        try {
            let successCount = 0;
            let failedCount = 0;

            for (const studentMarks of marksData) {
                try {
                    const obtainedMarks = (studentMarks.theory_marks || 0) +
                                         (studentMarks.practical_marks || 0) +
                                         (studentMarks.oral_marks || 0) +
                                         (studentMarks.assignment_marks || 0);

                    const response = await fetch(SMS.apiUrl('/exams/marks/enter'), {
                        method: 'POST',
                        headers: {
                            'Content-Type': 'application/json',
                            'Authorization': `Bearer ${SMS.authToken}`
                        },
                        body: JSON.stringify({
                            exam_id: examId,
                            student_id: studentMarks.student_id,
                            subject_id: subjectId,
                            obtained_marks: obtainedMarks,
                            total_marks: studentMarks.total_marks,
                            theory_marks: studentMarks.theory_marks,
                            practical_marks: studentMarks.practical_marks,
                            oral_marks: studentMarks.oral_marks,
                            assignment_marks: studentMarks.assignment_marks,
                            remarks: null
                        })
                    });

                    if (response.ok) {
                        successCount++;
                    } else {
                        failedCount++;
                    }
                } catch (err) {
                    console.error('Error submitting marks for student:', err);
                    failedCount++;
                }
            }

            Swal.fire({
                icon: successCount > 0 ? 'success' : 'error',
                title: successCount > 0 ? 'Success!' : 'Error',
                html: `
                    <p>Marks entry completed</p>
                    <p>Successfully entered: ${successCount}/${marksData.length} students</p>
                    ${failedCount > 0 ? `<p class="text-danger">Failed: ${failedCount}</p>` : ''}
                `,
                timer: 3000
            });

        } catch (error) {
            console.error('Error submitting marks:', error);
            Swal.fire({ icon: 'error', title: 'Error', text: error.message || 'Failed to submit marks' });
        }
    }

    async function viewExamResults(examId, examName) {
        try {
            const response = await fetch(SMS.apiUrl(`/exams/${examId}/results`), {
                headers: { 'Authorization': `Bearer ${SMS.authToken}` }
            });

            if (!response.ok) throw new Error('Failed to load exam results');

            const results = await response.json();

            if (results.length === 0) {
                Swal.fire({ icon: 'info', title: 'No Results', text: 'No results available for this exam yet' });
                return;
            }

            let tableHtml = `
                <div class="table-responsive" style="max-height: 500px; overflow-y: auto;">
                    <table class="table table-sm table-bordered table-striped">
                        <thead class="table-dark sticky-top">
                            <tr>
                                <th>Rank</th>
                                <th>Student Name</th>
                                <th>Class</th>
                                <th>Obtained</th>
                                <th>Total</th>
                                <th>Percentage</th>
                                <th>Grade</th>
                                <th>Result</th>
                            </tr>
                        </thead>
                        <tbody>
            `;

            results.forEach((result, index) => {
                const resultBadge = result.result_status === 'Pass' ?
                    '<span class="badge bg-success">Pass</span>' :
                    result.result_status === 'Fail' ?
                    '<span class="badge bg-danger">Fail</span>' :
                    '<span class="badge bg-warning">Pending</span>';

                tableHtml += `
                    <tr>
                        <td>${result.class_rank || '-'}</td>
                        <td>${result.student_name}</td>
                        <td>${result.class_name || '-'}</td>
                        <td>${result.total_obtained_marks}</td>
                        <td>${result.total_max_marks}</td>
                        <td>${result.percentage.toFixed(2)}%</td>
                        <td><span class="badge bg-primary">${result.grade}</span></td>
                        <td>${resultBadge}</td>
                    </tr>
                `;
            });

            tableHtml += `
                        </tbody>
                    </table>
                </div>
            `;

            Swal.fire({
                title: `Exam Results - ${examName}`,
                html: tableHtml,
                width: '90%',
                confirmButtonText: 'Close'
            });

        } catch (error) {
            console.error('Error loading exam results:', error);
            Swal.fire({ icon: 'error', title: 'Error', text: error.message || 'Failed to load exam results' });
        }
    }

    async function showEnterMarksInterface() {
        const [exams, classes, subjects] = await Promise.all([
            fetch(SMS.apiUrl('/exams/'), { headers: { 'Authorization': `Bearer ${SMS.authToken}` } }).then(r => r.ok ? r.json() : []),
            fetchClasses(),
            fetchSubjects()
        ]);

        const { value: formValues } = await Swal.fire({
            title: 'Enter Marks - Select Exam & Class',
            html: `
                <form id="marksSelectionForm" class="text-start">
                    <div class="mb-3">
                        <label class="form-label">Select Exam *</label>
                        <select class="form-control" id="marksExam" required>
                            <option value="">Select Exam</option>
                            ${exams.map(e => `<option value="${e.exam_id}">${e.exam_name} (${e.exam_type})</option>`).join('')}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Select Class *</label>
                        <select class="form-control" id="marksClass" required>
                            <option value="">Select Class</option>
                            ${classes.map(c => `<option value="${c.class_id}">${c.class_name} - ${c.section}</option>`).join('')}
                        </select>
                    </div>
                    <div class="mb-3">
                        <label class="form-label">Select Subject *</label>
                        <select class="form-control" id="marksSubject" required>
                            <option value="">Select Subject</option>
                            ${subjects.map(s => `<option value="${s.subject_id}">${s.subject_name}</option>`).join('')}
                        </select>
                    </div>
                    <div class="alert alert-info">
                        <i class="fas fa-info-circle"></i> You will be able to enter marks for all students in the selected class for this subject.
                    </div>
                </form>
            `,
            width: '600px',
            showCancelButton: true,
            confirmButtonText: 'Load Students',
            preConfirm: () => {
                const examId = document.getElementById('marksExam').value;
                const classId = document.getElementById('marksClass').value;
                const subjectId = document.getElementById('marksSubject').value;

                if (!examId || !classId || !subjectId) {
                    Swal.showValidationMessage('Please select exam, class, and subject');
                    return false;
                }

                return {
                    examId: parseInt(examId),
                    classId: parseInt(classId),
                    subjectId: parseInt(subjectId),
                    examName: exams.find(e => e.exam_id == examId)?.exam_name || 'Exam'
                };
            }
        });

        if (formValues) {
            await showBulkMarksEntryTable(formValues.examId, formValues.examName, formValues.classId, formValues.subjectId);
        }
    }

    async function fetchClasses() {
        try {
            const response = await fetch(SMS.apiUrl('/classes/'), {
                headers: { 'Authorization': `Bearer ${SMS.authToken}` }
            });
            if (!response.ok) throw new Error('Failed to fetch classes');
            return await response.json();
        } catch (error) {
            console.error('Error fetching classes:', error);
            return [];
        }
    }

    async function fetchSubjects() {
        try {
            const response = await fetch(SMS.apiUrl('/subjects/'), {
                headers: { 'Authorization': `Bearer ${SMS.authToken}` }
            });
            if (!response.ok) throw new Error('Failed to fetch subjects');
            return await response.json();
        } catch (error) {
            console.error('Error fetching subjects:', error);
            return [];
        }
    }

    // Expose functions to SMS namespace
    SMS.loadExams = loadExams;
    SMS.displayExamsTable = displayExamsTable;
    SMS.showAddExamModal = showAddExamModal;
    SMS.createExam = createExam;
    SMS.viewExamResults = viewExamResults;
    SMS.getGradeBadgeColor = getGradeBadgeColor;
    SMS.editExam = editExam;
    SMS.showMarksEntryModal = showMarksEntryModal;
    SMS.showBulkMarksEntryTable = showBulkMarksEntryTable;
    SMS.calculateTotal = calculateTotal;
    SMS.submitBulkMarks = submitBulkMarks;
    SMS.showEnterMarksInterface = showEnterMarksInterface;
    SMS.fetchClasses = fetchClasses;
    SMS.fetchSubjects = fetchSubjects;

    // Backwards-compatible window aliases
    window.loadExamsPage = SMS.loadExamsPage;
    window.loadExams = loadExams;
    window.displayExamsTable = displayExamsTable;
    window.showAddExamModal = showAddExamModal;
    window.createExam = createExam;
    window.viewExamResults = viewExamResults;
    window.getGradeBadgeColor = getGradeBadgeColor;
    window.editExam = editExam;
    window.showMarksEntryModal = showMarksEntryModal;
    window.showBulkMarksEntryTable = showBulkMarksEntryTable;
    window.calculateTotal = calculateTotal;
    window.submitBulkMarks = submitBulkMarks;
    window.showEnterMarksInterface = showEnterMarksInterface;
    window.fetchClasses = fetchClasses;
    window.fetchSubjects = fetchSubjects;

})(window);