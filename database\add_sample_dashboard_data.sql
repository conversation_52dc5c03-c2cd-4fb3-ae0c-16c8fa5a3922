-- =============================================
-- Add Sample Data for Dashboard Testing
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Add Sample Attendance Data for Today
-- =============================================
DECLARE @TodayDate DATE = CAST(GETDATE() AS DATE);

-- Clear existing attendance for today
DELETE FROM attendance WHERE attendance_date = @TodayDate;

-- Add sample attendance for existing students
INSERT INTO attendance (student_id, class_id, attendance_date, status, marked_by, created_at)
SELECT
    s.student_id,
    s.class_id,
    @TodayDate,
    CASE
        WHEN s.student_id % 5 = 0 THEN 'Absent'
        WHEN s.student_id % 7 = 0 THEN 'Late'
        WHEN s.student_id % 11 = 0 THEN 'Leave'
        ELSE 'Present'
    END,
    1, -- admin user_id
    GETDATE()
FROM students s
WHERE s.is_active = 1;

PRINT 'Sample attendance data added for today';

-- =============================================
-- Add Sample Fee Account Data
-- =============================================

-- Clear existing fee accounts for current year
DELETE FROM fee_accounts WHERE academic_year = 2025;

-- Add fee accounts for all active students
INSERT INTO fee_accounts (student_id, academic_year, total_fee, total_paid, total_arrears, total_fines, balance, created_at, updated_at)
SELECT 
    s.student_id,
    2025,
    CASE 
        WHEN c.class_name LIKE '%Nursery%' OR c.class_name LIKE '%Prep%' THEN 15000.00
        WHEN c.class_name LIKE '%Class [1-5]%' THEN 18000.00
        WHEN c.class_name LIKE '%Class [6-8]%' THEN 22000.00
        ELSE 25000.00
    END as total_fee,
    CASE 
        WHEN s.student_id % 3 = 0 THEN 5000.00  -- Some students have partial payments
        WHEN s.student_id % 4 = 0 THEN 0.00     -- Some students haven't paid
        ELSE 15000.00                           -- Most students have paid
    END as total_paid,
    CASE 
        WHEN s.student_id % 3 = 0 THEN 10000.00 -- Some students have arrears
        WHEN s.student_id % 4 = 0 THEN 15000.00 -- Some students have full arrears
        ELSE 0.00                               -- Most students are up to date
    END as total_arrears,
    CASE 
        WHEN s.student_id % 4 = 0 THEN 500.00   -- Late payment fine
        ELSE 0.00
    END as total_fines,
    CASE 
        WHEN s.student_id % 3 = 0 THEN -10000.00 -- Negative balance (owes money)
        WHEN s.student_id % 4 = 0 THEN -15500.00 -- Negative balance with fine
        ELSE 0.00                                -- Balanced
    END as balance,
    GETDATE(),
    GETDATE()
FROM students s
INNER JOIN classes c ON s.class_id = c.class_id
WHERE s.is_active = 1;

PRINT 'Sample fee account data added';

-- =============================================
-- Add Some Fee Transactions
-- =============================================

-- Add sample fee transactions for students who have made payments
INSERT INTO fee_transactions (student_id, fee_account_id, transaction_date, fee_month, fee_type, amount, fine_amount, total_amount, payment_method, receipt_number, collected_by, remarks, created_at)
SELECT
    s.student_id,
    fa.fee_account_id,
    DATEADD(DAY, -(s.student_id % 30), GETDATE()), -- Random dates in last 30 days
    1, -- January (month number)
    'Monthly Fee',
    5000.00,
    0.00,
    5000.00,
    CASE s.student_id % 3
        WHEN 0 THEN 'Cash'
        WHEN 1 THEN 'Bank Transfer'
        ELSE 'Online'
    END,
    'RCP' + RIGHT('000' + CAST(s.student_id as VARCHAR), 4) + '2025',
    1, -- admin user_id
    'Partial fee payment',
    GETDATE()
FROM students s
INNER JOIN fee_accounts fa ON s.student_id = fa.student_id
WHERE s.is_active = 1
AND fa.academic_year = 2025
AND s.student_id % 2 = 1; -- Only for odd student IDs

PRINT 'Sample fee transaction data added';

-- =============================================
-- Verify the data
-- =============================================
PRINT 'Data verification:';
SELECT 'Attendance records for today' as data_type, COUNT(*) as count FROM attendance WHERE attendance_date = @TodayDate;
SELECT 'Fee accounts for 2025' as data_type, COUNT(*) as count FROM fee_accounts WHERE academic_year = 2025;
SELECT 'Fee transactions' as data_type, COUNT(*) as count FROM fee_transactions;
SELECT 'Students with arrears > 1000' as data_type, COUNT(*) as count FROM fee_accounts WHERE academic_year = 2025 AND total_arrears > 1000;

PRINT 'Sample dashboard data setup complete!';
GO
