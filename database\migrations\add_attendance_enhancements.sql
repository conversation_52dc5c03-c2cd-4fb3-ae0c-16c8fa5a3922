-- =============================================
-- Comprehensive Attendance Management System Migration
-- =============================================

-- Add new columns to attendance table if they don't exist
IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'attendance') AND name = 'attendance_type')
BEGIN
    ALTER TABLE attendance ADD attendance_type NVARCHAR(50) NULL DEFAULT 'Daily Class Attendance';
    PRINT 'Added attendance_type column to attendance table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'attendance') AND name = 'period')
BEGIN
    ALTER TABLE attendance ADD period NVARCHAR(50) NULL;
    PRINT 'Added period column to attendance table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'attendance') AND name = 'subject_id')
BEGIN
    ALTER TABLE attendance ADD subject_id INT NULL;
    PRINT 'Added subject_id column to attendance table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'attendance') AND name = 'arrival_time')
BEGIN
    ALTER TABLE attendance ADD arrival_time TIME NULL;
    PRINT 'Added arrival_time column to attendance table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'attendance') AND name = 'departure_time')
BEGIN
    ALTER TABLE attendance ADD departure_time TIME NULL;
    PRINT 'Added departure_time column to attendance table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'attendance') AND name = 'is_edited')
BEGIN
    ALTER TABLE attendance ADD is_edited BIT NULL DEFAULT 0;
    PRINT 'Added is_edited column to attendance table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'attendance') AND name = 'edited_by')
BEGIN
    ALTER TABLE attendance ADD edited_by INT NULL;
    PRINT 'Added edited_by column to attendance table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'attendance') AND name = 'edit_reason')
BEGIN
    ALTER TABLE attendance ADD edit_reason NVARCHAR(500) NULL;
    PRINT 'Added edit_reason column to attendance table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'attendance') AND name = 'edited_at')
BEGIN
    ALTER TABLE attendance ADD edited_at DATETIME NULL;
    PRINT 'Added edited_at column to attendance table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'attendance') AND name = 'leave_type')
BEGIN
    ALTER TABLE attendance ADD leave_type NVARCHAR(50) NULL;
    PRINT 'Added leave_type column to attendance table';
END

IF NOT EXISTS (SELECT * FROM sys.columns WHERE object_id = OBJECT_ID(N'attendance') AND name = 'medical_certificate')
BEGIN
    ALTER TABLE attendance ADD medical_certificate NVARCHAR(500) NULL;
    PRINT 'Added medical_certificate column to attendance table';
END

-- Add foreign key constraints if they don't exist
IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_attendance_subject')
BEGIN
    ALTER TABLE attendance ADD CONSTRAINT FK_attendance_subject FOREIGN KEY (subject_id) REFERENCES subjects(subject_id);
    PRINT 'Added FK_attendance_subject foreign key constraint';
END

IF NOT EXISTS (SELECT * FROM sys.foreign_keys WHERE name = 'FK_attendance_edited_by')
BEGIN
    ALTER TABLE attendance ADD CONSTRAINT FK_attendance_edited_by FOREIGN KEY (edited_by) REFERENCES users(user_id);
    PRINT 'Added FK_attendance_edited_by foreign key constraint';
END

-- Create attendance_settings table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'attendance_settings')
BEGIN
    CREATE TABLE attendance_settings (
        setting_id INT IDENTITY(1,1) PRIMARY KEY,
        class_id INT NOT NULL,
        academic_year INT NOT NULL,
        working_days_per_week INT DEFAULT 5,
        min_attendance_percentage DECIMAL(5,2) DEFAULT 75.00,
        grace_days INT DEFAULT 3,
        auto_mark_holidays BIT DEFAULT 1,
        notification_enabled BIT DEFAULT 1,
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        CONSTRAINT FK_attendance_settings_class FOREIGN KEY (class_id) REFERENCES classes(class_id)
    );
    PRINT 'Created attendance_settings table';
END

-- Create attendance_permissions table if it doesn't exist
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'attendance_permissions')
BEGIN
    CREATE TABLE attendance_permissions (
        permission_id INT IDENTITY(1,1) PRIMARY KEY,
        user_id INT NOT NULL,
        role_id INT NOT NULL,
        can_backdate BIT DEFAULT 0,
        backdate_limit_days INT DEFAULT 0,
        can_edit BIT DEFAULT 0,
        edit_time_limit_hours INT DEFAULT 24,
        approved_classes NVARCHAR(500) NULL, -- Comma-separated class IDs
        created_at DATETIME2 DEFAULT GETDATE(),
        updated_at DATETIME2 DEFAULT GETDATE(),
        CONSTRAINT FK_attendance_permissions_user FOREIGN KEY (user_id) REFERENCES users(user_id),
        CONSTRAINT FK_attendance_permissions_role FOREIGN KEY (role_id) REFERENCES roles(role_id)
    );
    PRINT 'Created attendance_permissions table';
END

-- Create attendance_audit_log table for tracking all changes
IF NOT EXISTS (SELECT * FROM sys.tables WHERE name = 'attendance_audit_log')
BEGIN
    CREATE TABLE attendance_audit_log (
        audit_id INT IDENTITY(1,1) PRIMARY KEY,
        attendance_id INT NOT NULL,
        action_type NVARCHAR(50) NOT NULL, -- INSERT, UPDATE, DELETE
        old_status NVARCHAR(50) NULL,
        new_status NVARCHAR(50) NULL,
        old_remarks NVARCHAR(500) NULL,
        new_remarks NVARCHAR(500) NULL,
        changed_by INT NOT NULL,
        change_reason NVARCHAR(500) NULL,
        changed_at DATETIME2 DEFAULT GETDATE(),
        CONSTRAINT FK_attendance_audit_attendance FOREIGN KEY (attendance_id) REFERENCES attendance(attendance_id),
        CONSTRAINT FK_attendance_audit_user FOREIGN KEY (changed_by) REFERENCES users(user_id)
    );
    PRINT 'Created attendance_audit_log table';
END

-- Insert default attendance settings for existing classes
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'attendance_settings')
BEGIN
    INSERT INTO attendance_settings (class_id, academic_year, working_days_per_week, min_attendance_percentage, grace_days)
    SELECT 
        class_id, 
        YEAR(GETDATE()) as academic_year,
        5 as working_days_per_week,
        75.00 as min_attendance_percentage,
        3 as grace_days
    FROM classes
    WHERE class_id NOT IN (SELECT class_id FROM attendance_settings WHERE academic_year = YEAR(GETDATE()));
    
    PRINT 'Inserted default attendance settings for existing classes';
END

-- Insert default permissions for Administrator role
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'attendance_permissions')
BEGIN
    INSERT INTO attendance_permissions (user_id, role_id, can_backdate, backdate_limit_days, can_edit, edit_time_limit_hours)
    SELECT 
        u.user_id,
        u.role_id,
        1 as can_backdate,
        365 as backdate_limit_days,
        1 as can_edit,
        8760 as edit_time_limit_hours -- 1 year in hours
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE r.role_name = 'Administrator'
    AND u.user_id NOT IN (SELECT user_id FROM attendance_permissions);
    
    PRINT 'Inserted default permissions for Administrator users';
END

-- Insert default permissions for Teacher role
IF EXISTS (SELECT * FROM sys.tables WHERE name = 'attendance_permissions')
BEGIN
    INSERT INTO attendance_permissions (user_id, role_id, can_backdate, backdate_limit_days, can_edit, edit_time_limit_hours)
    SELECT 
        u.user_id,
        u.role_id,
        1 as can_backdate,
        7 as backdate_limit_days,
        1 as can_edit,
        24 as edit_time_limit_hours
    FROM users u
    INNER JOIN roles r ON u.role_id = r.role_id
    WHERE r.role_name = 'Teacher'
    AND u.user_id NOT IN (SELECT user_id FROM attendance_permissions);
    
    PRINT 'Inserted default permissions for Teacher users';
END

PRINT '';
PRINT '✅ Comprehensive Attendance Management System migration completed successfully!';

