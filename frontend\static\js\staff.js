// Staff Management Module
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    SMS.loadStaffPage = async function() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-user-tie"></i> Staff Management</h2>
                <button class="btn btn-primary" onclick="SMS.showAddStaffModal()">
                    <i class="fas fa-plus"></i> Add New Staff
                </button>
            </div>

            <div class="card">
                <div class="card-body">
                    <div id="staffTableContainer">
                        <div class="text-center">
                            <div class="spinner-border" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        `;

        await SMS.loadStaff();
    };

    SMS.loadStaff = async function() {
        try {
            const response = await fetch(SMS.apiUrl('/staff'), {
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`
                }
            });

            if (response.ok) {
                const staff = await response.json();
                SMS.displayStaffTable(staff);
            } else {
                SMS.showError('Failed to load staff');
            }
        } catch (error) {
            console.error('Error loading staff:', error);
            SMS.showError('Error loading staff');
        }
    };

    SMS.displayStaffTable = function(staff) {
        const container = document.getElementById('staffTableContainer');

        if (staff.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">No staff members found</p>';
            return;
        }

        let html = `
            <div class="table-responsive">
                <table class="table table-striped table-hover">
                    <thead>
                        <tr>
                            <th>Employee Code</th>
                            <th>Name</th>
                            <th>Designation</th>
                            <th>Department</th>
                            <th>Joining Date</th>
                            <th>Phone</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
        `;

        staff.forEach(member => {
            const statusBadge = member.is_active
                ? '<span class="badge bg-success">Active</span>'
                : '<span class="badge bg-secondary">Inactive</span>';

            html += `
                <tr>
                    <td>${member.employee_code}</td>
                    <td>${member.full_name}</td>
                    <td>${member.designation}</td>
                    <td>${member.department || 'N/A'}</td>
                    <td>${new Date(member.joining_date).toLocaleDateString()}</td>
                    <td>${member.phone || 'N/A'}</td>
                    <td>${statusBadge}</td>
                    <td>
                        <button class="btn btn-sm btn-info" onclick="SMS.viewStaff(${member.staff_id})">
                            <i class="fas fa-eye"></i>
                        </button>
                        <button class="btn btn-sm btn-warning" onclick="SMS.editStaff(${member.staff_id})">
                            <i class="fas fa-edit"></i>
                        </button>
                        <button class="btn btn-sm btn-danger" onclick="SMS.deleteStaff(${member.staff_id})">
                            <i class="fas fa-trash"></i>
                        </button>
                    </td>
                </tr>
            `;
        });

        html += `
                    </tbody>
                </table>
            </div>
        `;

        container.innerHTML = html;
    };

    SMS.showAddStaffModal = async function() {
        try {
            // Load users who are not already staff
            const usersResponse = await fetch(SMS.apiUrl('/users'), {
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`
                }
            });

            if (!usersResponse.ok) {
                SMS.showError('Failed to load users');
                return;
            }

            const users = await usersResponse.json();

            const { value: formValues } = await Swal.fire({
                title: 'Add New Staff Member',
                html: `
                    <div class="text-start">
                        <div class="mb-3">
                            <label class="form-label">Select User *</label>
                            <select id="staffUserId" class="swal2-select" required>
                                <option value="">Select User</option>
                                ${users.map(user => `<option value="${user.user_id}">${user.full_name} (${user.username})</option>`).join('')}
                            </select>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Employee Code *</label>
                            <input type="text" id="employeeCode" class="swal2-input" placeholder="e.g., EMP001" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Designation *</label>
                            <input type="text" id="designation" class="swal2-input" placeholder="e.g., Teacher" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Designation (Urdu)</label>
                            <input type="text" id="designationUrdu" class="swal2-input" dir="rtl">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Department</label>
                            <input type="text" id="department" class="swal2-input" placeholder="e.g., Science">
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Joining Date *</label>
                            <input type="date" id="joiningDate" class="swal2-input" value="${new Date().toISOString().split('T')[0]}" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">Salary</label>
                            <input type="number" id="salary" class="swal2-input" placeholder="Monthly salary">
                        </div>
                    </div>
                `,
                width: '500px',
                showCancelButton: true,
                confirmButtonText: 'Add Staff',
                preConfirm: () => {
                    const userId = document.getElementById('staffUserId').value;
                    const employeeCode = document.getElementById('employeeCode').value;
                    const designation = document.getElementById('designation').value;
                    const joiningDate = document.getElementById('joiningDate').value;

                    if (!userId || !employeeCode || !designation || !joiningDate) {
                        Swal.showValidationMessage('Please fill all required fields');
                        return false;
                    }

                    return {
                        user_id: parseInt(userId),
                        employee_code: employeeCode,
                        designation: designation,
                        designation_urdu: document.getElementById('designationUrdu').value || null,
                        department: document.getElementById('department').value || null,
                        joining_date: joiningDate,
                        salary: document.getElementById('salary').value ? parseFloat(document.getElementById('salary').value) : null,
                        is_active: true
                    };
                }
            });

            if (formValues) {
                const response = await fetch(SMS.apiUrl('/staff'), {
                    method: 'POST',
                    headers: {
                        'Authorization': `Bearer ${SMS.authToken}`,
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(formValues)
                });

                if (response.ok) {
                    SMS.showSuccess('Staff member added successfully!');
                    SMS.loadStaff();
                } else {
                    const error = await response.json();
                    SMS.showError(error.detail || 'Failed to add staff member');
                }
            }
        } catch (error) {
            console.error('Error adding staff:', error);
            SMS.showError('Error adding staff member');
        }
    };

    SMS.viewStaff = async function(staffId) {
        SMS.showError('View staff details coming soon!');
    };

    SMS.editStaff = async function(staffId) {
        SMS.showError('Edit staff functionality coming soon!');
    };

    SMS.deleteStaff = async function(staffId) {
        SMS.showError('Delete staff functionality coming soon!');
    };

    // Backwards-compatible aliases
    window.loadStaffPage = function(){ return SMS.loadStaffPage(); };
    window.showAddStaffModal = function(){ return SMS.showAddStaffModal(); };
    window.viewStaff = function(id){ return SMS.viewStaff(id); };
    window.editStaff = function(id){ return SMS.editStaff(id); };
    window.deleteStaff = function(id){ return SMS.deleteStaff(id); };

})(window);

