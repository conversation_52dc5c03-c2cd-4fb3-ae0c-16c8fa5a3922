"""
School Settings Routes
"""
from fastapi import APIRouter, HTTPException, Depends, status
from typing import List, Optional, Dict, Any
from pydantic import BaseModel
from datetime import time
from auth import get_current_user
from database import execute_query, execute_non_query, execute_procedure, execute_procedure_single

router = APIRouter()


# Pydantic models
class SchoolConfigResponse(BaseModel):
    config_key: str
    config_value: str
    config_type: str
    description: Optional[str] = None
    is_active: bool


class SchoolConfigUpdate(BaseModel):
    config_value: str


class TimeSlotTemplate(BaseModel):
    template_name: str
    school_start_time: time
    school_end_time: time
    period_duration: int
    break_duration: int
    lunch_duration: int
    periods_per_day: int
    short_break_after_period: int
    lunch_after_period: int


@router.get("/config", response_model=List[SchoolConfigResponse])
async def get_school_config(current_user: dict = Depends(get_current_user)):
    """Get all school configuration settings"""
    try:
        results = execute_procedure('sp_GetSchoolConfig')
        if not results:
            # Return empty list if no config found
            return []
        return results
    except Exception as e:
        print(f"[SETTINGS] Error getting school config: {e}")
        # Fallback query
        try:
            query = """
                SELECT 
                    config_key,
                    config_value,
                    'string' as config_type,
                    config_key as description,
                    1 as is_active
                FROM school_config
                WHERE is_active = 1
                ORDER BY config_key
            """
            results = execute_query(query)
            return results or []
        except Exception as fallback_error:
            print(f"[SETTINGS] Fallback query also failed: {fallback_error}")
            return []


@router.get("/config/{config_key}", response_model=SchoolConfigResponse)
async def get_config_by_key(config_key: str, current_user: dict = Depends(get_current_user)):
    """Get a specific configuration setting"""
    try:
        result = execute_procedure_single('sp_GetSchoolConfigByKey', {'config_key': config_key})
        if not result:
            raise HTTPException(status_code=404, detail="Configuration not found")
        return result
    except HTTPException:
        raise
    except Exception as e:
        print(f"[SETTINGS] Error getting config by key: {e}")
        # Fallback query
        try:
            query = """
                SELECT 
                    config_key,
                    config_value,
                    'string' as config_type,
                    config_key as description,
                    is_active
                FROM school_config
                WHERE config_key = ? AND is_active = 1
            """
            result = execute_query(query, (config_key,))
            if not result:
                raise HTTPException(status_code=404, detail="Configuration not found")
            return result[0]
        except Exception as fallback_error:
            print(f"[SETTINGS] Fallback query failed: {fallback_error}")
            raise HTTPException(status_code=500, detail="Failed to retrieve configuration")


@router.put("/config/{config_key}", response_model=SchoolConfigResponse)
async def update_config(
    config_key: str, 
    config_data: SchoolConfigUpdate, 
    current_user: dict = Depends(get_current_user)
):
    """Update a configuration setting"""
    # Only Admin and Owner can update settings
    if current_user['role_name'] not in ['Administrator', 'Owner']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can update school settings"
        )
    
    try:
        result = execute_procedure_single('sp_UpdateSchoolConfig', {
            'config_key': config_key,
            'config_value': config_data.config_value
        })
        if not result:
            raise HTTPException(status_code=404, detail="Configuration not found")
        return result
    except HTTPException:
        raise
    except Exception as e:
        print(f"[SETTINGS] Error updating config: {e}")
        # Fallback update
        try:
            query = """
                UPDATE school_config 
                SET config_value = ?, updated_at = GETDATE()
                WHERE config_key = ? AND is_active = 1
            """
            execute_non_query(query, (config_data.config_value, config_key))
            return await get_config_by_key(config_key, current_user)
        except Exception as fallback_error:
            print(f"[SETTINGS] Fallback update failed: {fallback_error}")
            raise HTTPException(status_code=500, detail="Failed to update configuration")


@router.post("/config", response_model=SchoolConfigResponse, status_code=status.HTTP_201_CREATED)
async def create_config(
    config_key: str,
    config_data: SchoolConfigUpdate,
    current_user: dict = Depends(get_current_user)
):
    """Create a new configuration setting"""
    # Only Admin and Owner can create settings
    if current_user['role_name'] not in ['Administrator', 'Owner']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can create school settings"
        )
    
    try:
        # Check if config already exists
        existing = execute_procedure_single('sp_GetSchoolConfigByKey', {'config_key': config_key})
        if existing:
            raise HTTPException(status_code=400, detail="Configuration already exists")
        
        # Create new config
        result = execute_procedure_single('sp_CreateSchoolConfig', {
            'config_key': config_key,
            'config_value': config_data.config_value
        })
        if not result:
            raise HTTPException(status_code=500, detail="Failed to create configuration")
        return result
    except HTTPException:
        raise
    except Exception as e:
        print(f"[SETTINGS] Error creating config: {e}")
        raise HTTPException(status_code=500, detail="Failed to create configuration")


@router.get("/time-slots/templates", response_model=List[Dict[str, Any]])
async def get_time_slot_templates(current_user: dict = Depends(get_current_user)):
    """Get all time slot templates"""
    try:
        results = execute_procedure('sp_GetTimeSlotTemplates')
        return results or []
    except Exception as e:
        print(f"[SETTINGS] Error getting time slot templates: {e}")
        # Return default template
        return [{
            "template_name": "Default",
            "school_start_time": "08:30:00",
            "school_end_time": "14:30:00",
            "period_duration": 40,
            "break_duration": 10,
            "lunch_duration": 30,
            "periods_per_day": 8,
            "short_break_after_period": 2,
            "lunch_after_period": 4
        }]


@router.post("/time-slots/templates", status_code=status.HTTP_201_CREATED)
async def create_time_slot_template(
    template: TimeSlotTemplate,
    current_user: dict = Depends(get_current_user)
):
    """Create a new time slot template"""
    # Only Admin and Owner can create templates
    if current_user['role_name'] not in ['Administrator', 'Owner']:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only administrators can create time slot templates"
        )
    
    try:
        result = execute_procedure_single('sp_CreateTimeSlotTemplate', {
            'template_name': template.template_name,
            'school_start_time': template.school_start_time.strftime('%H:%M:%S'),
            'school_end_time': template.school_end_time.strftime('%H:%M:%S'),
            'period_duration': template.period_duration,
            'break_duration': template.break_duration,
            'lunch_duration': template.lunch_duration,
            'periods_per_day': template.periods_per_day,
            'short_break_after_period': template.short_break_after_period,
            'lunch_after_period': template.lunch_after_period
        })
        return {"message": "Time slot template created successfully", "template": result}
    except Exception as e:
        print(f"[SETTINGS] Error creating time slot template: {e}")
        raise HTTPException(status_code=500, detail="Failed to create time slot template")
