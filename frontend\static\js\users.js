// User management related functions
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    SMS.loadUsersPage = async function() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-users"></i> User Management</h2>
                <button class="btn btn-primary" onclick="showAddUserModal()"><i class="fas fa-plus"></i> Add New User</button>
            </div>
            <div class="card"><div class="card-body"><div id="usersTableContainer"><div class="text-center"><div class="spinner-border" role="status"></div></div></div></div></div>
        `;
        await SMS.loadUsers();
    };

    SMS.loadUsers = async function() {
        SMS.showLoadingInline('usersTableContainer', 'Loading users...');
        try {
            const response = await fetch(SMS.apiUrl('/users'), { headers: { 'Authorization': `Bearer ${SMS.authToken}` } });
            if (response.ok) {
                const users = await response.json();
                SMS.displayUsersTable(users);
            } else {
                const error = await response.json();
                SMS.showError('Failed to load users', error.detail || 'Please try again or contact support');
            }
        } catch (error) {
            console.error('Error loading users:', error);
            SMS.showError('Error loading users', 'Unable to connect to server.');
        }
    };

    SMS.displayUsersTable = function(users) {
        const container = document.getElementById('usersTableContainer');
        container.innerHTML = `
            <table class="table table-striped table-hover" id="usersTable">
                <thead><tr><th>ID</th><th>Username</th><th>Full Name</th><th>Email</th><th>Role</th><th>Status</th><th>Actions</th></tr></thead>
                <tbody>${users.map(user => `
                    <tr>
                        <td>${user.user_id}</td>
                        <td>${user.username}</td>
                        <td>${user.full_name}</td>
                        <td>${user.email}</td>
                        <td><span class="badge bg-info">${user.role_name}</span></td>
                        <td>${user.is_active ? '<span class="badge bg-success">Active</span>' : '<span class="badge bg-danger">Inactive</span>'}</td>
                        <td>
                            <button class="btn btn-sm btn-warning" onclick="editUser(${user.user_id})"><i class="fas fa-edit"></i></button>
                            <button class="btn btn-sm btn-danger" onclick="deleteUser(${user.user_id})"><i class="fas fa-trash"></i></button>
                        </td>
                    </tr>`).join('')}</tbody>
            </table>`;
    };

    SMS.showAddUserModal = async function() {
        const roles = await SMS.loadRoles();
        if (!roles || roles.length === 0) { SMS.showError('Failed to load roles'); return; }

        const result = await Swal.fire({
            title: 'Add New User',
            html: `<div class="text-start">... (modal content simplified)</div>`,
            showCancelButton: true,
            confirmButtonText: 'Create User',
            preConfirm: () => { return false; }
        });
    };

    SMS.createUser = async function(userData) {
        try {
            const response = await fetch(SMS.apiUrl('/users'), {
                method: 'POST', headers: { 'Authorization': `Bearer ${SMS.authToken}`, 'Content-Type': 'application/json' }, body: JSON.stringify(userData)
            });
            if (response.ok) { SMS.showSuccess('User created successfully'); SMS.loadUsers(); }
            else { const error = await response.json(); SMS.showError(error.detail || 'Failed to create user'); }
        } catch (error) { console.error('Error creating user:', error); SMS.showError('Error creating user'); }
    };

    SMS.deleteUser = async function(userId) {
        const result = await Swal.fire({ title: 'Are you sure?', showCancelButton: true, confirmButtonText: 'Yes, delete it!' });
        if (result.isConfirmed) {
            try {
                const response = await fetch(SMS.apiUrl(`/users/${userId}`), { method: 'DELETE', headers: { 'Authorization': `Bearer ${SMS.authToken}` } });
                if (response.ok) { SMS.showSuccess('User deleted successfully'); SMS.loadUsers(); } else { const error = await response.json(); SMS.showError(error.detail || 'Failed to delete user'); }
            } catch (error) { console.error('Error deleting user:', error); SMS.showError('Error deleting user'); }
        }
    };

    SMS.loadRoles = async function() {
        try {
            const response = await fetch(SMS.apiUrl('/users/roles'), { headers: { 'Authorization': `Bearer ${SMS.authToken}` } });
            if (response.ok) return await response.json();
            return [];
        } catch (e) { console.error('Error loading roles:', e); return []; }
    };

})(window);
