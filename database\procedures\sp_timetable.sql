-- =============================================
-- Timetable Stored Procedures
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- Get Class Timetable
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetClassTimetable]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetClassTimetable;
GO

CREATE PROCEDURE sp_GetClassTimetable
    @class_id INT,
    @academic_year INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT
        t.timetable_id,
        t.class_id,
        c.class_name,
        c.section,
        t.day_of_week,
        t.period_number,
        CONVERT(VARCHAR(8), t.start_time, 108) as start_time,
        CONVERT(VARCHAR(8), t.end_time, 108) as end_time,
        t.subject_id,
        s.subject_name,
        t.teacher_id,
        u.full_name as teacher_name,
        t.academic_year,
        NULL as period_name,
        1 as is_teaching_period,
        t.is_active,
        t.created_at,
        t.updated_at
    FROM class_timetable t
    INNER JOIN classes c ON t.class_id = c.class_id
    LEFT JOIN subjects s ON t.subject_id = s.subject_id
    LEFT JOIN staff st ON t.teacher_id = st.staff_id
    LEFT JOIN users u ON st.user_id = u.user_id
    WHERE t.class_id = @class_id
    AND t.is_active = 1
    AND (@academic_year IS NULL OR t.academic_year = @academic_year)
    ORDER BY t.day_of_week, t.period_number;
END
GO

-- =============================================
-- Get Teacher Schedule
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetTeacherSchedule]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetTeacherSchedule;
GO

CREATE PROCEDURE sp_GetTeacherSchedule
    @teacher_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        t.timetable_id,
        t.class_id,
        c.class_name,
        t.day_of_week,
        t.period_number,
        t.start_time,
        t.end_time,
        t.subject_id,
        s.subject_name,
        t.is_active
    FROM class_timetable t
    INNER JOIN classes c ON t.class_id = c.class_id
    LEFT JOIN subjects s ON t.subject_id = s.subject_id
    WHERE t.teacher_id = @teacher_id
    AND t.is_active = 1
    ORDER BY t.day_of_week, t.period_number;
END
GO

-- =============================================
-- Get All Timetables (for admin view)
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetAllTimetables]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetAllTimetables;
GO

CREATE PROCEDURE sp_GetAllTimetables
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        t.timetable_id,
        t.class_id,
        c.class_name,
        t.day_of_week,
        t.period_number,
        t.start_time,
        t.end_time,
        t.subject_id,
        s.subject_name,
        t.teacher_id,
        u.full_name as teacher_name,
        t.is_active,
        t.created_at,
        t.updated_at
    FROM class_timetable t
    INNER JOIN classes c ON t.class_id = c.class_id
    LEFT JOIN subjects s ON t.subject_id = s.subject_id
    LEFT JOIN staff st ON t.teacher_id = st.staff_id
    LEFT JOIN users u ON st.user_id = u.user_id
    WHERE t.is_active = 1
    ORDER BY c.class_name, t.day_of_week, t.period_number;
END
GO

-- =============================================
-- Create/Update Timetable Entry
-- =============================================
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_UpsertTimetableEntry]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_UpsertTimetableEntry;
GO

CREATE PROCEDURE sp_UpsertTimetableEntry
    @timetable_id INT = NULL,
    @class_id INT,
    @day_of_week INT,
    @period_number INT,
    @start_time TIME,
    @end_time TIME,
    @subject_id INT,
    @teacher_id INT
AS
BEGIN
    SET NOCOUNT ON;
    
    IF @timetable_id IS NULL OR @timetable_id = 0
    BEGIN
        -- Insert new timetable entry
        INSERT INTO class_timetable (
            class_id, day_of_week, period_number, start_time, end_time,
            subject_id, teacher_id, academic_year, is_active, created_at, updated_at
        )
        VALUES (
            @class_id, @day_of_week, @period_number, @start_time, @end_time,
            @subject_id, @teacher_id, 2025, 1, GETDATE(), GETDATE()
        );
        
        SELECT SCOPE_IDENTITY() as timetable_id, 'Timetable entry created successfully' as message;
    END
    ELSE
    BEGIN
        -- Update existing timetable entry
        UPDATE class_timetable
        SET
            class_id = @class_id,
            day_of_week = @day_of_week,
            period_number = @period_number,
            start_time = @start_time,
            end_time = @end_time,
            subject_id = @subject_id,
            teacher_id = @teacher_id,
            updated_at = GETDATE()
        WHERE timetable_id = @timetable_id;
        
        SELECT @timetable_id as timetable_id, 'Timetable entry updated successfully' as message;
    END
END
GO

PRINT 'Timetable stored procedures created successfully!';
GO
