"""
Notification Service - SMS and Email
"""
from config import get_settings
from database import execute_non_query
import logging
from typing import Optional
from datetime import datetime

logger = logging.getLogger(__name__)
settings = get_settings()


class NotificationService:
    """Service for sending SMS and Email notifications"""
    
    @staticmethod
    def send_sms(phone_number: str, message: str) -> bool:
        """
        Send SMS using Twilio
        
        Args:
            phone_number: Recipient phone number
            message: SMS message content
        
        Returns:
            True if sent successfully, False otherwise
        """
        try:
            from twilio.rest import Client
            
            # Initialize Twilio client
            client = Client(settings.TWILIO_ACCOUNT_SID, settings.TWILIO_AUTH_TOKEN)
            
            # Send SMS
            message_obj = client.messages.create(
                body=message,
                from_=settings.TWILIO_PHONE_NUMBER,
                to=phone_number
            )
            
            # Log notification
            NotificationService._log_notification(
                notification_type='SMS',
                recipient=phone_number,
                message=message,
                status='Sent',
                external_id=message_obj.sid
            )
            
            logger.info(f"SMS sent to {phone_number}: {message_obj.sid}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending SMS to {phone_number}: {e}")
            
            # Log failed notification
            NotificationService._log_notification(
                notification_type='SMS',
                recipient=phone_number,
                message=message,
                status='Failed',
                error_message=str(e)
            )
            
            return False
    
    @staticmethod
    async def send_email(to_email: str, subject: str, body: str, html: bool = False) -> bool:
        """
        Send email using SMTP
        
        Args:
            to_email: Recipient email address
            subject: Email subject
            body: Email body content
            html: Whether body is HTML
        
        Returns:
            True if sent successfully, False otherwise
        """
        try:
            import aiosmtplib
            from email.mime.text import MIMEText
            from email.mime.multipart import MIMEMultipart
            
            # Create message
            message = MIMEMultipart('alternative')
            message['From'] = settings.SMTP_FROM_EMAIL
            message['To'] = to_email
            message['Subject'] = subject
            
            # Add body
            mime_type = 'html' if html else 'plain'
            message.attach(MIMEText(body, mime_type))
            
            # Send email
            await aiosmtplib.send(
                message,
                hostname=settings.SMTP_HOST,
                port=settings.SMTP_PORT,
                username=settings.SMTP_USERNAME,
                password=settings.SMTP_PASSWORD,
                start_tls=True
            )
            
            # Log notification
            NotificationService._log_notification(
                notification_type='Email',
                recipient=to_email,
                message=f"{subject}: {body[:100]}...",
                status='Sent'
            )
            
            logger.info(f"Email sent to {to_email}")
            return True
            
        except Exception as e:
            logger.error(f"Error sending email to {to_email}: {e}")
            
            # Log failed notification
            NotificationService._log_notification(
                notification_type='Email',
                recipient=to_email,
                message=f"{subject}: {body[:100]}...",
                status='Failed',
                error_message=str(e)
            )
            
            return False
    
    @staticmethod
    def send_attendance_alert(student_id: int, status: str, date: str) -> bool:
        """
        Send attendance alert to parent
        
        Args:
            student_id: Student ID
            status: Attendance status (Absent, Late)
            date: Attendance date
        
        Returns:
            True if sent successfully
        """
        try:
            # Get student and parent info
            from database import execute_query
            
            query = """
                SELECT s.full_name, s.full_name_urdu, s.parent_phone, s.parent_email
                FROM students s
                WHERE s.student_id = ?
            """
            
            result = execute_query(query, (student_id,))
            
            if not result:
                logger.warning(f"Student {student_id} not found")
                return False
            
            student = result[0]
            
            # Prepare message
            if status == 'Absent':
                message_en = f"Alert: Your child {student['full_name']} was absent on {date}."
                message_ur = f"انتباہ: آپ کا بچہ {student['full_name_urdu']} {date} کو غیر حاضر تھا۔"
            elif status == 'Late':
                message_en = f"Alert: Your child {student['full_name']} was late on {date}."
                message_ur = f"انتباہ: آپ کا بچہ {student['full_name_urdu']} {date} کو دیر سے آیا۔"
            else:
                return False
            
            message = f"{message_en}\n{message_ur}"
            
            # Send SMS if phone available
            sms_sent = False
            if student['parent_phone']:
                sms_sent = NotificationService.send_sms(student['parent_phone'], message)
            
            return sms_sent
            
        except Exception as e:
            logger.error(f"Error sending attendance alert: {e}")
            return False
    
    @staticmethod
    def send_fee_reminder(student_id: int, arrears: float, due_date: str) -> bool:
        """
        Send fee payment reminder to parent
        
        Args:
            student_id: Student ID
            arrears: Outstanding arrears amount
            due_date: Payment due date
        
        Returns:
            True if sent successfully
        """
        try:
            # Get student and parent info
            from database import execute_query
            
            query = """
                SELECT s.full_name, s.full_name_urdu, s.parent_phone, s.parent_email
                FROM students s
                WHERE s.student_id = ?
            """
            
            result = execute_query(query, (student_id,))
            
            if not result:
                logger.warning(f"Student {student_id} not found")
                return False
            
            student = result[0]
            
            # Prepare message
            message_en = f"Fee Reminder: Outstanding fee for {student['full_name']} is Rs. {arrears:.2f}. Due date: {due_date}"
            message_ur = f"فیس یاد دہانی: {student['full_name_urdu']} کی بقایا فیس روپے {arrears:.2f} ہے۔ آخری تاریخ: {due_date}"
            
            message = f"{message_en}\n{message_ur}"
            
            # Send SMS if phone available
            sms_sent = False
            if student['parent_phone']:
                sms_sent = NotificationService.send_sms(student['parent_phone'], message)
            
            return sms_sent
            
        except Exception as e:
            logger.error(f"Error sending fee reminder: {e}")
            return False
    
    @staticmethod
    def send_exam_result_notification(student_id: int, exam_id: int, percentage: float, grade: str) -> bool:
        """
        Send exam result notification to parent
        
        Args:
            student_id: Student ID
            exam_id: Exam ID
            percentage: Percentage obtained
            grade: Grade obtained
        
        Returns:
            True if sent successfully
        """
        try:
            # Get student and exam info
            from database import execute_query
            
            query = """
                SELECT s.full_name, s.full_name_urdu, s.parent_phone, s.parent_email,
                       e.exam_name, e.exam_name_urdu
                FROM students s
                CROSS JOIN exams e
                WHERE s.student_id = ? AND e.exam_id = ?
            """
            
            result = execute_query(query, (student_id, exam_id))
            
            if not result:
                logger.warning(f"Student {student_id} or Exam {exam_id} not found")
                return False
            
            data = result[0]
            
            # Prepare message
            message_en = f"Result: {data['full_name']} scored {percentage:.1f}% (Grade {grade}) in {data['exam_name']}"
            message_ur = f"نتیجہ: {data['full_name_urdu']} نے {data['exam_name_urdu']} میں {percentage:.1f}% (گریڈ {grade}) حاصل کیا"
            
            message = f"{message_en}\n{message_ur}"
            
            # Send SMS if phone available
            sms_sent = False
            if data['parent_phone']:
                sms_sent = NotificationService.send_sms(data['parent_phone'], message)
            
            return sms_sent
            
        except Exception as e:
            logger.error(f"Error sending exam result notification: {e}")
            return False
    
    @staticmethod
    def _log_notification(
        notification_type: str,
        recipient: str,
        message: str,
        status: str,
        external_id: Optional[str] = None,
        error_message: Optional[str] = None
    ):
        """
        Log notification to database
        
        Args:
            notification_type: Type (SMS, Email)
            recipient: Recipient contact
            message: Message content
            status: Status (Sent, Failed)
            external_id: External service ID
            error_message: Error message if failed
        """
        try:
            query = """
                INSERT INTO notifications (
                    notification_type, recipient, message, status,
                    external_id, error_message, sent_at
                )
                VALUES (?, ?, ?, ?, ?, ?, GETDATE())
            """
            
            execute_non_query(
                query,
                (notification_type, recipient, message, status, external_id, error_message)
            )
            
        except Exception as e:
            logger.error(f"Error logging notification: {e}")

