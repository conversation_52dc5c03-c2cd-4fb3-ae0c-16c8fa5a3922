"""
Timetable Configuration Routes
Handles school configuration and period templates
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from models import (SchoolConfigResponse, SchoolConfigCreate, 
                    PeriodTemplateResponse, PeriodTemplateCreate)
from auth import get_current_user, require_admin
from services.timetable_service import TimetableService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.get("/config", response_model=List[SchoolConfigResponse])
async def get_school_config(
    current_user: dict = Depends(get_current_user)
):
    """Get all school configuration settings"""
    try:
        result = TimetableService.get_school_config()
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching school config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching school configuration"
        )


@router.get("/config/{config_key}", response_model=SchoolConfigResponse)
async def get_school_config_by_key(
    config_key: str,
    current_user: dict = Depends(get_current_user)
):
    """Get specific school configuration by key"""
    try:
        result = TimetableService.get_school_config_by_key(config_key)
        
        if result:
            return result
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Configuration key '{config_key}' not found"
            )
    except Exception as e:
        logger.error(f"Error fetching config key {config_key}: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching configuration"
        )


@router.post("/config", status_code=status.HTTP_201_CREATED)
async def create_or_update_config(
    config: SchoolConfigCreate,
    current_user: dict = Depends(require_admin)
):
    """Create or update school configuration (Admin only)"""
    try:
        result = TimetableService.create_or_update_config(config, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return {"message": result.get('Message', 'Configuration updated successfully')}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to update configuration')
            )
    except Exception as e:
        logger.error(f"Error updating config: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during configuration update"
        )


@router.get("/periods/templates", response_model=List[PeriodTemplateResponse])
async def get_period_templates(
    academic_year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """Get period templates with optional academic year filter"""
    try:
        result = TimetableService.get_period_templates(academic_year)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching period templates: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching period templates"
        )


@router.post("/periods/templates", status_code=status.HTTP_201_CREATED)
async def create_period_template(
    period: PeriodTemplateCreate,
    current_user: dict = Depends(require_admin)
):
    """Create a new period template (Admin only)"""
    try:
        result = TimetableService.create_period_template(period, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return {
                "message": result.get('Message', 'Period template created successfully'),
                "template_id": result.get('TemplateId')
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to create period template')
            )
    except Exception as e:
        logger.error(f"Error creating period template: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during period template creation"
        )
