// Exam Page Functions Module
(function(window){
    const SMS = window.SMS || (window.SMS = {});

    // =============================================
    // EXAM PAGE LOADING
    // =============================================

    SMS.loadExamsPage = async function() {
        const contentArea = document.getElementById('contentArea');
        contentArea.innerHTML = `
            <div class="d-flex justify-content-between align-items-center mb-4">
                <h2><i class="fas fa-file-alt"></i> Exam Management</h2>
                <div>
                    <button class="btn btn-success me-2" onclick="showEnterMarksInterface()">
                        <i class="fas fa-pen"></i> Enter Marks
                    </button>
                    <button class="btn btn-primary" onclick="showAddExamModal()">
                        <i class="fas fa-plus"></i> Create Exam
                    </button>
                </div>
            </div>

            <div class="card">
                <div class="card-body">
                    <div class="row mb-3">
                        <div class="col-md-4">
                            <label>Academic Year</label>
                            <input type="number" class="form-control" id="examYearFilter" value="${new Date().getFullYear()}" min="2020" max="2030">
                        </div>
                        <div class="col-md-4">
                            <label>Exam Type</label>
                            <select class="form-control" id="examTypeFilter">
                                <option value="">All Types</option>
                                <option value="Monthly">Monthly</option>
                                <option value="Mid-Term">Mid-Term</option>
                                <option value="Final">Final</option>
                                <option value="Quiz">Quiz</option>
                            </select>
                        </div>
                        <div class="col-md-4">
                            <label>&nbsp;</label>
                            <button class="btn btn-primary w-100" onclick="loadExams()">
                                <i class="fas fa-search"></i> Search Exams
                            </button>
                        </div>
                    </div>
                    <div id="examsTableContainer">
                        <p class="text-center text-muted">Click "Search Exams" to view exams</p>
                    </div>
                </div>
            </div>
        `;
    };

    async function loadExams() {
        const year = document.getElementById('examYearFilter').value;
        const type = document.getElementById('examTypeFilter').value;
        
        try {
            const params = new URLSearchParams();
            if (year) params.append('academic_year', year);
            if (type) params.append('exam_type', type);
            
            const response = await fetch(SMS.apiUrl(`/exams/?${params.toString()}`), {
                headers: {
                    'Authorization': `Bearer ${SMS.authToken}`
                }
            });
            
            if (response.ok) {
                const exams = await response.json();
                displayExamsTable(exams);
            } else {
                SMS.showError('Failed to load exams');
            }
        } catch (error) {
            console.error('Error loading exams:', error);
            SMS.showError('Error loading exams');
        }
    }

    function displayExamsTable(exams) {
        const container = document.getElementById('examsTableContainer');
        
        if (!exams || exams.length === 0) {
            container.innerHTML = '<p class="text-center text-muted">No exams found</p>';
            return;
        }
        
        const tableHtml = `
            <div class="table-responsive">
                <table class="table table-striped">
                    <thead>
                        <tr>
                            <th>Exam Name</th>
                            <th>Type</th>
                            <th>Academic Year</th>
                            <th>Start Date</th>
                            <th>End Date</th>
                            <th>Status</th>
                            <th>Actions</th>
                        </tr>
                    </thead>
                    <tbody>
                        ${exams.map(exam => `
                            <tr>
                                <td><strong>${exam.exam_name}</strong></td>
                                <td><span class="badge bg-info">${exam.exam_type}</span></td>
                                <td>${exam.academic_year}</td>
                                <td>${new Date(exam.start_date).toLocaleDateString()}</td>
                                <td>${new Date(exam.end_date).toLocaleDateString()}</td>
                                <td>
                                    <span class="badge ${exam.is_active ? 'bg-success' : 'bg-secondary'}">
                                        ${exam.is_active ? 'Active' : 'Inactive'}
                                    </span>
                                </td>
                                <td>
                                    <button class="btn btn-sm btn-primary me-1" onclick="editExam(${exam.exam_id})" title="Edit">
                                        <i class="fas fa-edit"></i>
                                    </button>
                                    <button class="btn btn-sm btn-success me-1" onclick="showMarksEntryModal(${exam.exam_id}, '${exam.exam_name}')" title="Enter Marks">
                                        <i class="fas fa-pen"></i>
                                    </button>
                                    <button class="btn btn-sm btn-info" onclick="viewExamResults(${exam.exam_id}, '${exam.exam_name}')" title="View Results">
                                        <i class="fas fa-chart-bar"></i>
                                    </button>
                                </td>
                            </tr>
                        `).join('')}
                    </tbody>
                </table>
            </div>
        `;
        
        container.innerHTML = tableHtml;
    }

    // Export functions to global scope for backward compatibility
    window.loadExams = loadExams;
    window.displayExamsTable = displayExamsTable;

})(window);
