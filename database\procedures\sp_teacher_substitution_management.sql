-- Teacher Substitution Management Stored Procedures
USE SchoolManagementDB;
GO

-- =============================================
-- Get Teacher Substitutions
-- =============================================
IF OBJECT_ID('sp_Teacher_GetSubstitutions', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetSubstitutions;
GO
CREATE PROCEDURE sp_Teacher_GetSubstitutions
    @teacher_id INT = NULL,
    @start_date DATE = NULL,
    @end_date DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT ps.substitution_id, ps.original_period_id, ps.substitute_teacher_id,
           ps.substitution_date, ps.reason, ps.created_at,
           ot.full_name AS original_teacher_name,
           st.full_name AS substitute_teacher_name,
           c.class_name, c.section, sub.subject_name,
           pt.start_time, pt.end_time, ct.day_of_week, ct.period_number
    FROM period_substitutions ps
    INNER JOIN class_timetables ct ON ps.original_period_id = ct.timetable_id
    INNER JOIN users ot ON ct.teacher_id = ot.user_id
    INNER JOIN users st ON ps.substitute_teacher_id = st.user_id
    INNER JOIN classes c ON ct.class_id = c.class_id
    INNER JOIN subjects sub ON ct.subject_id = sub.subject_id
    INNER JOIN period_templates pt ON ct.period_number = pt.period_number
    WHERE (@teacher_id IS NULL OR ct.teacher_id = @teacher_id OR ps.substitute_teacher_id = @teacher_id)
      AND (@start_date IS NULL OR ps.substitution_date >= @start_date)
      AND (@end_date IS NULL OR ps.substitution_date <= @end_date)
    ORDER BY ps.substitution_date DESC, ct.period_number;
END
GO

-- =============================================
-- Create Teacher Substitution
-- =============================================
IF OBJECT_ID('sp_Teacher_CreateSubstitution', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_CreateSubstitution;
GO
CREATE PROCEDURE sp_Teacher_CreateSubstitution
    @original_teacher_id INT,
    @substitute_teacher_id INT,
    @substitution_date DATE,
    @reason VARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    -- This is a simplified version - in practice, you'd need to specify which periods
    -- For now, we'll return a success message indicating the substitution framework is ready
    SELECT 'Success' AS Result, 'Substitution framework ready - use sp_Teacher_CreatePeriodSubstitution for specific periods' AS Message;
END
GO

-- =============================================
-- Get Available Substitute Teachers
-- =============================================
IF OBJECT_ID('sp_Teacher_GetAvailableSubstitutes', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetAvailableSubstitutes;
GO
CREATE PROCEDURE sp_Teacher_GetAvailableSubstitutes
    @day_of_week INT,
    @period_number INT,
    @academic_year INT,
    @exclude_teacher_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT DISTINCT u.user_id AS teacher_id, u.full_name AS teacher_name, s.designation
    FROM users u
    LEFT JOIN staff s ON u.user_id = s.user_id
    WHERE u.role_id IN (SELECT role_id FROM roles WHERE role_name LIKE '%Teacher%' OR role_name = 'Staff')
      AND u.is_active = 1
      AND (@exclude_teacher_id IS NULL OR u.user_id != @exclude_teacher_id)
      AND u.user_id NOT IN (
          SELECT ct.teacher_id
          FROM class_timetables ct
          INNER JOIN classes c ON ct.class_id = c.class_id
          WHERE ct.day_of_week = @day_of_week
            AND ct.period_number = @period_number
            AND c.academic_year = @academic_year
      )
    ORDER BY u.full_name;
END
GO

-- =============================================
-- Check Substitute Teacher Conflict
-- =============================================
IF OBJECT_ID('sp_Teacher_CheckSubstituteConflict', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_CheckSubstituteConflict;
GO
CREATE PROCEDURE sp_Teacher_CheckSubstituteConflict
    @substitute_teacher_id INT,
    @day_of_week INT,
    @period_number INT,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT COUNT(*) AS conflict_count
    FROM class_timetables ct
    INNER JOIN classes c ON ct.class_id = c.class_id
    WHERE ct.teacher_id = @substitute_teacher_id
      AND ct.day_of_week = @day_of_week
      AND ct.period_number = @period_number
      AND c.academic_year = @academic_year;
END
GO

-- =============================================
-- Create Period Substitution
-- =============================================
IF OBJECT_ID('sp_Teacher_CreatePeriodSubstitution', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_CreatePeriodSubstitution;
GO
CREATE PROCEDURE sp_Teacher_CreatePeriodSubstitution
    @original_period_id INT,
    @substitute_teacher_id INT,
    @substitution_date DATE,
    @reason VARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    INSERT INTO period_substitutions (original_period_id, substitute_teacher_id, substitution_date, reason, created_at)
    VALUES (@original_period_id, @substitute_teacher_id, @substitution_date, @reason, GETDATE());
    
    SELECT 'Success' AS Result, 'Period substitution created successfully' AS Message, SCOPE_IDENTITY() AS SubstitutionId;
END
GO

-- =============================================
-- Get Period Substitutions
-- =============================================
IF OBJECT_ID('sp_Teacher_GetPeriodSubstitutions', 'P') IS NOT NULL DROP PROCEDURE sp_Teacher_GetPeriodSubstitutions;
GO
CREATE PROCEDURE sp_Teacher_GetPeriodSubstitutions
    @substitution_date DATE = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT ps.substitution_id, ps.original_period_id, ps.substitute_teacher_id,
           ps.substitution_date, ps.reason, ps.created_at,
           ot.full_name AS original_teacher_name,
           st.full_name AS substitute_teacher_name,
           c.class_name, c.section, sub.subject_name,
           pt.start_time, pt.end_time, ct.day_of_week, ct.period_number
    FROM period_substitutions ps
    INNER JOIN class_timetables ct ON ps.original_period_id = ct.timetable_id
    INNER JOIN users ot ON ct.teacher_id = ot.user_id
    INNER JOIN users st ON ps.substitute_teacher_id = st.user_id
    INNER JOIN classes c ON ct.class_id = c.class_id
    INNER JOIN subjects sub ON ct.subject_id = sub.subject_id
    INNER JOIN period_templates pt ON ct.period_number = pt.period_number
    WHERE (@substitution_date IS NULL OR ps.substitution_date = @substitution_date)
    ORDER BY ps.substitution_date DESC, ct.period_number;
END
GO
