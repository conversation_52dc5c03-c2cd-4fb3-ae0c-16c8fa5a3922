-- Additional Exam procedures to replace inline SQL
USE SchoolManagementDB;
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_CreateExam]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_CreateExam;
GO
CREATE PROCEDURE sp_CreateExam
    @exam_name NVARCHAR(255),
    @exam_name_urdu NVARCHAR(255),
    @exam_type NVARCHAR(50),
    @term_type NVARCHAR(50),
    @academic_year INT,
    @start_date DATE,
    @end_date DATE,
    @total_marks INT,
    @passing_marks INT,
    @weightage DECIMAL(5,2),
    @is_active BIT
AS
BEGIN
    SET NOCOUNT ON;
    INSERT INTO exams (exam_name, exam_name_urdu, exam_type, term_type, academic_year, start_date, end_date, total_marks, passing_marks, weightage, is_active, created_at, updated_at)
    VALUES (@exam_name, @exam_name_urdu, @exam_type, @term_type, @academic_year, @start_date, @end_date, @total_marks, @passing_marks, @weightage, @is_active, GETDATE(), GETDATE());
    SELECT SCOPE_IDENTITY() AS exam_id;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_UpdateExam]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_UpdateExam;
GO
CREATE PROCEDURE sp_UpdateExam
    @exam_id INT,
    @exam_name NVARCHAR(255),
    @exam_name_urdu NVARCHAR(255),
    @exam_type NVARCHAR(50),
    @term_type NVARCHAR(50),
    @academic_year INT,
    @start_date DATE,
    @end_date DATE,
    @total_marks INT,
    @passing_marks INT,
    @weightage DECIMAL(5,2),
    @is_active BIT
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE exams
    SET exam_name = @exam_name,
        exam_name_urdu = @exam_name_urdu,
        exam_type = @exam_type,
        term_type = @term_type,
        academic_year = @academic_year,
        start_date = @start_date,
        end_date = @end_date,
        total_marks = @total_marks,
        passing_marks = @passing_marks,
        weightage = @weightage,
        is_active = @is_active,
        updated_at = GETDATE()
    WHERE exam_id = @exam_id;
    SELECT 1 AS success;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetStudentResults]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetStudentResults;
GO
CREATE PROCEDURE sp_GetStudentResults
    @student_id INT,
    @exam_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    IF @exam_id IS NOT NULL
    BEGIN
        SELECT er.*, e.exam_name, e.exam_type,
               s.full_name AS student_name, s.admission_number,
               sub.subject_name, sub.subject_name_urdu,
               CAST((er.obtained_marks / NULLIF(er.total_marks,0) * 100) AS DECIMAL(5,2)) AS percentage
        FROM exam_results er
        INNER JOIN exams e ON er.exam_id = e.exam_id
        INNER JOIN students s ON er.student_id = s.student_id
        INNER JOIN subjects sub ON er.subject_id = sub.subject_id
        WHERE er.student_id = @student_id AND er.exam_id = @exam_id
        ORDER BY sub.subject_name;
    END
    ELSE
    BEGIN
        SELECT er.*, e.exam_name, e.exam_type, e.academic_year,
               s.full_name AS student_name, s.admission_number,
               sub.subject_name, sub.subject_name_urdu,
               CAST((er.obtained_marks / NULLIF(er.total_marks,0) * 100) AS DECIMAL(5,2)) AS percentage
        FROM exam_results er
        INNER JOIN exams e ON er.exam_id = e.exam_id
        INNER JOIN students s ON er.student_id = s.student_id
        INNER JOIN subjects sub ON er.subject_id = sub.subject_id
        WHERE er.student_id = @student_id
        ORDER BY e.academic_year DESC, e.exam_name, sub.subject_name;
    END
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetExamResults]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetExamResults;
GO
CREATE PROCEDURE sp_GetExamResults
    @exam_id INT,
    @class_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    IF @class_id IS NOT NULL
    BEGIN
        SELECT er.*, s.full_name AS student_name, s.admission_number,
               c.class_name, c.section,
               sub.subject_name, sub.subject_name_urdu,
               CAST((er.obtained_marks / NULLIF(er.total_marks,0) * 100) AS DECIMAL(5,2)) AS percentage
        FROM exam_results er
        INNER JOIN students s ON er.student_id = s.student_id
        INNER JOIN classes c ON s.class_id = c.class_id
        INNER JOIN subjects sub ON er.subject_id = sub.subject_id
        WHERE er.exam_id = @exam_id AND c.class_id = @class_id
        ORDER BY c.class_name, c.section, s.full_name, sub.subject_name;
    END
    ELSE
    BEGIN
        SELECT er.*, s.full_name AS student_name, s.admission_number,
               c.class_name, c.section,
               sub.subject_name, sub.subject_name_urdu,
               CAST((er.obtained_marks / NULLIF(er.total_marks,0) * 100) AS DECIMAL(5,2)) AS percentage
        FROM exam_results er
        INNER JOIN students s ON er.student_id = s.student_id
        INNER JOIN classes c ON s.class_id = c.class_id
        INNER JOIN subjects sub ON er.subject_id = sub.subject_id
        WHERE er.exam_id = @exam_id
        ORDER BY c.class_name, c.section, s.full_name, sub.subject_name;
    END
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetExams]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetExams;
GO
CREATE PROCEDURE sp_GetExams
    @academic_year INT = NULL,
    @exam_type NVARCHAR(50) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SELECT * FROM exams WHERE (@academic_year IS NULL OR academic_year = @academic_year) AND (@exam_type IS NULL OR exam_type = @exam_type) AND is_active = 1 ORDER BY academic_year DESC, start_date DESC;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetTopPerformers]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetTopPerformers;
GO
CREATE PROCEDURE sp_GetTopPerformers
    @exam_id INT,
    @class_id INT = NULL,
    @limit INT = 10
AS
BEGIN
    SET NOCOUNT ON;
    IF @class_id IS NOT NULL
    BEGIN
        SELECT TOP (@limit)
            s.student_id, s.admission_number, s.full_name,
            c.class_name, c.section,
            SUM(er.obtained_marks) AS total_obtained,
            SUM(er.total_marks) AS total_marks,
            CAST(SUM(er.obtained_marks) / NULLIF(SUM(er.total_marks),0) * 100 AS DECIMAL(5,2)) AS percentage
        FROM exam_results er
        INNER JOIN students s ON er.student_id = s.student_id
        INNER JOIN classes c ON s.class_id = c.class_id
        WHERE er.exam_id = @exam_id AND c.class_id = @class_id
        GROUP BY s.student_id, s.admission_number, s.full_name, c.class_name, c.section
        ORDER BY percentage DESC;
    END
    ELSE
    BEGIN
        SELECT TOP (@limit)
            s.student_id, s.admission_number, s.full_name,
            c.class_name, c.section,
            SUM(er.obtained_marks) AS total_obtained,
            SUM(er.total_marks) AS total_marks,
            CAST(SUM(er.obtained_marks) / NULLIF(SUM(er.total_marks),0) * 100 AS DECIMAL(5,2)) AS percentage
        FROM exam_results er
        INNER JOIN students s ON er.student_id = s.student_id
        INNER JOIN classes c ON s.class_id = c.class_id
        WHERE er.exam_id = @exam_id
        GROUP BY s.student_id, s.admission_number, s.full_name, c.class_name, c.section
        ORDER BY percentage DESC;
    END
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetClassStudentsForMarksEntry]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetClassStudentsForMarksEntry;
GO
CREATE PROCEDURE sp_GetClassStudentsForMarksEntry
    @exam_id INT,
    @class_id INT,
    @subject_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        s.student_id,
        s.admission_number,
        s.full_name,
        er.theory_marks,
        er.practical_marks,
        er.oral_marks,
        er.assignment_marks,
        er.obtained_marks,
        er.total_marks,
        er.is_draft,
        CASE WHEN er.result_id IS NOT NULL THEN 1 ELSE 0 END AS has_marks
    FROM students s
    LEFT JOIN exam_results er ON s.student_id = er.student_id AND er.exam_id = @exam_id AND er.subject_id = @subject_id
    WHERE s.class_id = @class_id AND s.is_active = 1
    ORDER BY s.admission_number;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetSubjectPerformanceSummary]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetSubjectPerformanceSummary;
GO
CREATE PROCEDURE sp_GetSubjectPerformanceSummary
    @exam_id INT,
    @class_id INT,
    @subject_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        COUNT(DISTINCT er.student_id) AS total_students,
        AVG(CASE WHEN er.total_marks=0 THEN 0 ELSE (er.obtained_marks / NULLIF(er.total_marks,0) * 100) END) AS average_percentage,
        MAX(er.obtained_marks) AS highest_marks,
        MIN(er.obtained_marks) AS lowest_marks,
        SUM(CASE WHEN (er.obtained_marks / NULLIF(er.total_marks,0) * 100) >= 90 THEN 1 ELSE 0 END) AS a_plus_count,
        SUM(CASE WHEN (er.obtained_marks / NULLIF(er.total_marks,0) * 100) >= 80 AND (er.obtained_marks / NULLIF(er.total_marks,0) * 100) < 90 THEN 1 ELSE 0 END) AS a_count,
        SUM(CASE WHEN (er.obtained_marks / NULLIF(er.total_marks,0) * 100) >= 70 AND (er.obtained_marks / NULLIF(er.total_marks,0) * 100) < 80 THEN 1 ELSE 0 END) AS b_count,
        SUM(CASE WHEN (er.obtained_marks / NULLIF(er.total_marks,0) * 100) >= 60 AND (er.obtained_marks / NULLIF(er.total_marks,0) * 100) < 70 THEN 1 ELSE 0 END) AS c_count,
        SUM(CASE WHEN (er.obtained_marks / NULLIF(er.total_marks,0) * 100) < 60 THEN 1 ELSE 0 END) AS below_c_count,
        SUM(CASE WHEN (er.obtained_marks / NULLIF(er.total_marks,0) * 100) >= 40 THEN 1 ELSE 0 END) AS passed_students,
        SUM(CASE WHEN (er.obtained_marks / NULLIF(er.total_marks,0) * 100) < 40 THEN 1 ELSE 0 END) AS failed_students
    FROM exam_results er
    INNER JOIN students s ON er.student_id = s.student_id
    WHERE er.exam_id = @exam_id AND s.class_id = @class_id AND er.subject_id = @subject_id;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetExamSubjects]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetExamSubjects;
GO
CREATE PROCEDURE sp_GetExamSubjects
    @exam_id INT,
    @class_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        es.exam_subject_id,
        es.exam_id,
        es.subject_id,
        s.subject_name,
        s.subject_name_urdu,
        es.class_id,
        c.class_name,
        c.section,
        es.max_marks,
        es.passing_marks,
        es.theory_max,
        es.practical_max,
        es.oral_max,
        es.assignment_max
    FROM exam_subjects es
    INNER JOIN subjects s ON es.subject_id = s.subject_id
    LEFT JOIN classes c ON es.class_id = c.class_id
    WHERE es.exam_id = @exam_id AND es.is_active = 1
      AND (@class_id IS NULL OR es.class_id = @class_id OR es.class_id IS NULL)
    ORDER BY s.subject_name;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetStudentsForMarksEntry]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetStudentsForMarksEntry;
GO
CREATE PROCEDURE sp_GetStudentsForMarksEntry
    @exam_id INT,
    @class_id INT,
    @subject_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        s.student_id,
        s.full_name,
        s.admission_number,
        er.result_id,
        er.obtained_marks,
        er.total_marks,
        er.theory_marks,
        er.practical_marks,
        er.oral_marks,
        er.assignment_marks,
        er.grade,
        er.remarks
    FROM students s
    LEFT JOIN exam_results er ON s.student_id = er.student_id AND er.exam_id = @exam_id AND er.subject_id = @subject_id
    WHERE s.class_id = @class_id AND s.is_active = 1
    ORDER BY s.full_name;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetReportCard]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetReportCard;
GO
CREATE PROCEDURE sp_GetReportCard
    @student_id INT,
    @exam_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT s.*, c.class_name, c.section, e.exam_name, e.exam_type
    FROM students s
    INNER JOIN classes c ON s.class_id = c.class_id
    CROSS JOIN exams e
    WHERE s.student_id = @student_id AND e.exam_id = @exam_id;
END
GO
IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetClassPerformance]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetClassPerformance;
GO
CREATE PROCEDURE sp_GetClassPerformance
    @exam_id INT,
    @class_id INT
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        COUNT(DISTINCT er.student_id) AS total_students,
        AVG(CASE WHEN er.total_marks=0 THEN 0 ELSE (er.obtained_marks / NULLIF(er.total_marks,0) * 100) END) AS average_percentage,
        MAX(CASE WHEN er.total_marks=0 THEN 0 ELSE (er.obtained_marks / NULLIF(er.total_marks,0) * 100) END) AS highest_percentage,
        MIN(CASE WHEN er.total_marks=0 THEN 0 ELSE (er.obtained_marks / NULLIF(er.total_marks,0) * 100) END) AS lowest_percentage,
        SUM(CASE WHEN (er.obtained_marks / NULLIF(er.total_marks,0) * 100) >= 40 THEN 1 ELSE 0 END) AS passed_students,
        SUM(CASE WHEN (er.obtained_marks / NULLIF(er.total_marks,0) * 100) < 40 THEN 1 ELSE 0 END) AS failed_students
    FROM exam_results er
    INNER JOIN students s ON er.student_id = s.student_id
    WHERE er.exam_id = @exam_id AND s.class_id = @class_id;
END
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_GetExamStudentSummaries]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_GetExamStudentSummaries;
GO
CREATE PROCEDURE sp_GetExamStudentSummaries
    @exam_id INT,
    @class_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    SELECT
        ses.summary_id,
        ses.exam_id,
        ses.student_id,
        s.full_name AS student_name,
        s.admission_number,
        ses.class_id,
        c.class_name,
        c.section,
        ses.total_obtained_marks,
        ses.total_max_marks,
        ses.percentage,
        ses.grade,
        ses.class_rank,
        ses.section_rank,
        ses.overall_rank,
        ses.result_status,
        ses.remarks,
        ses.is_published
    FROM student_exam_summary ses
    INNER JOIN students s ON ses.student_id = s.student_id
    INNER JOIN classes c ON ses.class_id = c.class_id
    WHERE ses.exam_id = @exam_id
      AND (@class_id IS NULL OR ses.class_id = @class_id)
    ORDER BY ses.percentage DESC, s.full_name;
END
GO

PRINT 'Exam procedures created';
GO

IF EXISTS (SELECT * FROM sys.objects WHERE object_id = OBJECT_ID(N'[dbo].[sp_SetExamResultDraft]') AND type in (N'P', N'PC'))
    DROP PROCEDURE sp_SetExamResultDraft;
GO
CREATE PROCEDURE sp_SetExamResultDraft
    @exam_id INT,
    @student_id INT,
    @subject_id INT,
    @is_draft BIT
AS
BEGIN
    SET NOCOUNT ON;
    UPDATE exam_results
    SET is_draft = @is_draft,
        updated_at = GETDATE()
    WHERE exam_id = @exam_id AND student_id = @student_id AND subject_id = @subject_id;
    SELECT 1 AS success;
END
GO
