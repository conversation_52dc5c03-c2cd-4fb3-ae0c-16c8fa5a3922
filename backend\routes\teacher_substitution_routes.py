"""
Teacher Substitution Routes
Handles substitute teacher management
"""
from fastapi import APIRouter, Depends, HTTPException, status
from typing import List, Optional
from datetime import date
from models import (PeriodSubstitutionCreate, PeriodSubstitutionResponse)
from auth import get_current_user, require_admin
from services.teacher_service import TeacherService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/substitutions", status_code=status.HTTP_201_CREATED)
async def create_substitution(
    substitution: PeriodSubstitutionCreate,
    current_user: dict = Depends(require_admin)
):
    """
    Create a period substitution (Admin only)
    
    Args:
        substitution: Substitution data
        current_user: Current authenticated user
    
    Returns:
        Success response with substitution ID
    """
    try:
        result = TeacherService.create_substitution(substitution, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return {
                "message": result.get('Message', 'Substitution created successfully'),
                "substitution_id": result.get('SubstitutionId'),
                "substitution_details": result.get('SubstitutionDetails', {})
            }
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to create substitution')
            )
    except Exception as e:
        logger.error(f"Error creating substitution: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during substitution creation"
        )


@router.get("/substitutions", response_model=List[PeriodSubstitutionResponse])
async def get_substitutions(
    substitution_date: Optional[date] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Get period substitutions with optional date filtering
    
    Args:
        substitution_date: Optional date filter
        current_user: Current authenticated user
    
    Returns:
        List of period substitutions
    """
    try:
        result = TeacherService.get_substitutions(substitution_date)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching substitutions: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching substitutions"
        )
