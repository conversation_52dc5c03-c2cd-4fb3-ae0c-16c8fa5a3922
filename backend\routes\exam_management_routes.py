"""
Exam Management Routes
Handles CRUD operations for exams
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import Optional
from models import ExamCreate, SuccessResponse
from auth import require_admin, get_current_user
from services.exam_service import ExamService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/", response_model=SuccessResponse)
async def create_exam(
    exam: ExamCreate,
    current_user: dict = Depends(require_admin)
):
    """
    Create a new exam (Admin only)
    
    Args:
        exam: Exam data
        current_user: Current authenticated user
    
    Returns:
        Success response with exam ID
    """
    try:
        result = ExamService.create_exam(exam, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return SuccessResponse(
                message=result.get('Message', 'Exam created successfully'),
                data={'exam_id': result.get('ExamId')}
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to create exam')
            )
    except Exception as e:
        logger.error(f"Error creating exam: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during exam creation"
        )


@router.get("/", response_model=list)
async def get_exams(
    academic_year: Optional[int] = Query(None),
    exam_type: Optional[str] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get exams with optional filtering
    
    Args:
        academic_year: Optional academic year filter
        exam_type: Optional exam type filter
        current_user: Current authenticated user
    
    Returns:
        List of exams
    """
    try:
        result = ExamService.get_exams(academic_year, exam_type)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching exams: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching exams"
        )


@router.put("/{exam_id}", response_model=SuccessResponse)
async def update_exam(
    exam_id: int,
    exam: ExamCreate,
    current_user: dict = Depends(require_admin)
):
    """
    Update an existing exam (Admin only)
    
    Args:
        exam_id: Exam ID to update
        exam: Updated exam data
        current_user: Current authenticated user
    
    Returns:
        Success response
    """
    try:
        result = ExamService.update_exam(exam_id, exam, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return SuccessResponse(
                message=result.get('Message', 'Exam updated successfully')
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to update exam')
            )
    except Exception as e:
        logger.error(f"Error updating exam: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during exam update"
        )
