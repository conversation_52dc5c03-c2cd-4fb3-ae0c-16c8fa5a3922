-- =============================================
-- School Management System - Stored Procedures
-- =============================================

USE SchoolManagementDB;
GO

-- =============================================
-- SP: Mark Attendance
-- =============================================
CREATE OR ALTER PROCEDURE sp_MarkAttendance
    @student_id INT,
    @class_id INT,
    @attendance_date DATE,
    @status NVARCHAR(10),
    @marked_by INT,
    @remarks NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Check if attendance already exists
        IF EXISTS (SELECT 1 FROM attendance WHERE student_id = @student_id AND attendance_date = @attendance_date)
        BEGIN
            -- Update existing attendance
            UPDATE attendance
            SET status = @status,
                class_id = @class_id,
                marked_by = @marked_by,
                remarks = @remarks,
                updated_at = GETDATE()
            WHERE student_id = @student_id AND attendance_date = @attendance_date;
        END
        ELSE
        BEGIN
            -- Insert new attendance
            INSERT INTO attendance (student_id, class_id, attendance_date, status, marked_by, remarks)
            VALUES (@student_id, @class_id, @attendance_date, @status, @marked_by, @remarks);
        END
        
        -- Log the action
        INSERT INTO audit_logs (user_id, action, table_name, record_id)
        VALUES (@marked_by, 'MARK_ATTENDANCE', 'attendance', @student_id);
        
        COMMIT TRANSACTION;
        SELECT 'Success' AS Result, 'Attendance marked successfully' AS Message;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        SELECT 'Error' AS Result, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- =============================================
-- SP: Get Daily Attendance Summary
-- =============================================
CREATE OR ALTER PROCEDURE sp_GetDailyAttendanceSummary
    @attendance_date DATE,
    @class_id INT = NULL
AS
BEGIN
    SET NOCOUNT ON;
    
    SELECT 
        c.class_name,
        c.section,
        COUNT(DISTINCT s.student_id) AS total_students,
        SUM(CASE WHEN a.status = 'Present' THEN 1 ELSE 0 END) AS present_count,
        SUM(CASE WHEN a.status = 'Absent' THEN 1 ELSE 0 END) AS absent_count,
        SUM(CASE WHEN a.status = 'Leave' THEN 1 ELSE 0 END) AS leave_count,
        SUM(CASE WHEN a.status = 'Late' THEN 1 ELSE 0 END) AS late_count,
        CAST(SUM(CASE WHEN a.status = 'Present' THEN 1 ELSE 0 END) * 100.0 / 
             NULLIF(COUNT(DISTINCT s.student_id), 0) AS DECIMAL(5,2)) AS attendance_percentage
    FROM classes c
    LEFT JOIN students s ON c.class_id = s.class_id AND s.is_active = 1
    LEFT JOIN attendance a ON s.student_id = a.student_id AND a.attendance_date = @attendance_date
    WHERE c.is_active = 1
        AND (@class_id IS NULL OR c.class_id = @class_id)
    GROUP BY c.class_id, c.class_name, c.section
    ORDER BY c.class_name, c.section;
END
GO

-- =============================================
-- SP: Record Exam Marks
-- =============================================
CREATE OR ALTER PROCEDURE sp_RecordExamMarks
    @exam_id INT,
    @student_id INT,
    @subject_id INT,
    @obtained_marks DECIMAL(5,2),
    @total_marks DECIMAL(5,2),
    @entered_by INT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Calculate grade
        DECLARE @percentage DECIMAL(5,2) = (@obtained_marks / @total_marks) * 100;
        DECLARE @grade NVARCHAR(5);
        
        IF @percentage >= 90 SET @grade = 'A+';
        ELSE IF @percentage >= 80 SET @grade = 'A';
        ELSE IF @percentage >= 70 SET @grade = 'B';
        ELSE IF @percentage >= 60 SET @grade = 'C';
        ELSE IF @percentage >= 50 SET @grade = 'D';
        ELSE IF @percentage >= 40 SET @grade = 'E';
        ELSE SET @grade = 'F';
        
        -- Check if result already exists
        IF EXISTS (SELECT 1 FROM exam_results WHERE exam_id = @exam_id AND student_id = @student_id AND subject_id = @subject_id)
        BEGIN
            -- Update existing result
            UPDATE exam_results
            SET obtained_marks = @obtained_marks,
                total_marks = @total_marks,
                grade = @grade,
                entered_by = @entered_by,
                updated_at = GETDATE()
            WHERE exam_id = @exam_id AND student_id = @student_id AND subject_id = @subject_id;
        END
        ELSE
        BEGIN
            -- Insert new result
            INSERT INTO exam_results (exam_id, student_id, subject_id, obtained_marks, total_marks, grade, entered_by)
            VALUES (@exam_id, @student_id, @subject_id, @obtained_marks, @total_marks, @grade, @entered_by);
        END
        
        -- Log the action
        INSERT INTO audit_logs (user_id, action, table_name, record_id)
        VALUES (@entered_by, 'RECORD_EXAM_MARKS', 'exam_results', @student_id);
        
        COMMIT TRANSACTION;
        SELECT 'Success' AS Result, 'Exam marks recorded successfully' AS Message, @grade AS Grade;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        SELECT 'Error' AS Result, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- =============================================
-- SP: Promote Students
-- =============================================
CREATE OR ALTER PROCEDURE sp_PromoteStudents
    @current_class_id INT,
    @next_class_id INT,
    @academic_year INT,
    @promoted_by INT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        DECLARE @promoted_count INT = 0;
        
        -- Get students who passed (average >= 40%)
        UPDATE s
        SET s.class_id = @next_class_id,
            s.status = 'Promoted',
            s.updated_at = GETDATE()
        FROM students s
        WHERE s.class_id = @current_class_id
            AND s.is_active = 1
            AND s.student_id IN (
                SELECT er.student_id
                FROM exam_results er
                INNER JOIN exams e ON er.exam_id = e.exam_id
                WHERE e.academic_year = @academic_year
                    AND e.exam_type = 'Final Term'
                GROUP BY er.student_id
                HAVING AVG((er.obtained_marks / er.total_marks) * 100) >= 40
            );
        
        SET @promoted_count = @@ROWCOUNT;
        
        -- Log the action
        INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values)
        VALUES (@promoted_by, 'PROMOTE_STUDENTS', 'students', @current_class_id, 
                'Promoted ' + CAST(@promoted_count AS NVARCHAR) + ' students');
        
        COMMIT TRANSACTION;
        SELECT 'Success' AS Result, 
               'Students promoted successfully' AS Message,
               @promoted_count AS PromotedCount;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        SELECT 'Error' AS Result, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- =============================================
-- SP: Fee Collection
-- =============================================
CREATE OR ALTER PROCEDURE sp_FeeCollection
    @student_id INT,
    @fee_account_id INT,
    @fee_type NVARCHAR(50),
    @fee_month INT = NULL,
    @amount DECIMAL(18,2),
    @fine_amount DECIMAL(18,2) = 0,
    @payment_method NVARCHAR(50),
    @collected_by INT,
    @remarks NVARCHAR(500) = NULL
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        DECLARE @total_amount DECIMAL(18,2) = @amount + @fine_amount;
        DECLARE @receipt_number NVARCHAR(50);
        
        -- Generate receipt number
        SET @receipt_number = 'RCP' + FORMAT(GETDATE(), 'yyyyMMdd') + 
                              RIGHT('00000' + CAST(@student_id AS NVARCHAR), 5) + 
                              RIGHT('000' + CAST(DATEPART(MILLISECOND, GETDATE()) AS NVARCHAR), 3);
        
        -- Insert transaction
        INSERT INTO fee_transactions (fee_account_id, student_id, transaction_date, fee_month, 
                                      fee_type, amount, fine_amount, total_amount, 
                                      payment_method, receipt_number, collected_by, remarks)
        VALUES (@fee_account_id, @student_id, CAST(GETDATE() AS DATE), @fee_month,
                @fee_type, @amount, @fine_amount, @total_amount,
                @payment_method, @receipt_number, @collected_by, @remarks);
        
        -- Update fee account
        UPDATE fee_accounts
        SET total_paid = total_paid + @total_amount,
            balance = total_fee + total_arrears + total_fines - (total_paid + @total_amount),
            last_payment_date = CAST(GETDATE() AS DATE),
            updated_at = GETDATE()
        WHERE fee_account_id = @fee_account_id;
        
        -- Log the action
        INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values)
        VALUES (@collected_by, 'FEE_COLLECTION', 'fee_transactions', @student_id, @receipt_number);
        
        COMMIT TRANSACTION;
        SELECT 'Success' AS Result, 
               'Fee collected successfully' AS Message,
               @receipt_number AS ReceiptNumber,
               @total_amount AS TotalAmount;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        SELECT 'Error' AS Result, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- =============================================
-- SP: Calculate Fee Arrears
-- =============================================
CREATE OR ALTER PROCEDURE sp_FeeArrearsCalculation
    @academic_year INT,
    @current_month INT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Update arrears for all active students
        UPDATE fa
        SET fa.total_arrears = (
                SELECT ISNULL(SUM(fs.amount), 0)
                FROM fee_structures fs
                WHERE fs.class_id = s.class_id
                    AND fs.academic_year = @academic_year
                    AND fs.fee_type = 'Monthly Tuition'
            ) * @current_month - fa.total_paid,
            fa.updated_at = GETDATE()
        FROM fee_accounts fa
        INNER JOIN students s ON fa.student_id = s.student_id
        WHERE fa.academic_year = @academic_year
            AND s.is_active = 1;
        
        COMMIT TRANSACTION;
        SELECT 'Success' AS Result, 'Arrears calculated successfully' AS Message;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        SELECT 'Error' AS Result, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

-- =============================================
-- SP: Calculate Fee Fines
-- =============================================
CREATE OR ALTER PROCEDURE sp_FeeFineLogic
    @student_id INT,
    @fee_month INT,
    @academic_year INT
AS
BEGIN
    SET NOCOUNT ON;
    
    DECLARE @due_date DATE;
    DECLARE @current_date DATE = CAST(GETDATE() AS DATE);
    DECLARE @days_late INT;
    DECLARE @fine_per_day DECIMAL(18,2);
    DECLARE @total_fine DECIMAL(18,2) = 0;
    
    -- Get fee structure
    SELECT @fine_per_day = fs.fine_per_day,
           @due_date = DATEFROMPARTS(@academic_year, @fee_month, fs.due_day)
    FROM fee_structures fs
    INNER JOIN students s ON fs.class_id = s.class_id
    WHERE s.student_id = @student_id
        AND fs.fee_type = 'Monthly Tuition'
        AND fs.academic_year = @academic_year;
    
    -- Calculate fine if payment is late
    IF @current_date > @due_date
    BEGIN
        SET @days_late = DATEDIFF(DAY, @due_date, @current_date);
        SET @total_fine = @days_late * @fine_per_day;
    END
    
    SELECT @total_fine AS CalculatedFine, @days_late AS DaysLate;
END
GO

-- =============================================
-- SP: Archive Old Sessions
-- =============================================
CREATE OR ALTER PROCEDURE sp_ArchiveOldSessions
    @academic_year INT,
    @archived_by INT
AS
BEGIN
    SET NOCOUNT ON;
    BEGIN TRY
        BEGIN TRANSACTION;
        
        -- Archive students
        INSERT INTO archives (archive_type, academic_year, data, archived_by)
        SELECT 'Student', @academic_year, 
               (SELECT * FROM students s
                INNER JOIN classes c ON s.class_id = c.class_id
                WHERE c.academic_year = @academic_year
                FOR JSON PATH),
               @archived_by;
        
        -- Archive attendance
        INSERT INTO archives (archive_type, academic_year, data, archived_by)
        SELECT 'Attendance', @academic_year,
               (SELECT * FROM attendance a
                INNER JOIN students s ON a.student_id = s.student_id
                INNER JOIN classes c ON s.class_id = c.class_id
                WHERE c.academic_year = @academic_year
                FOR JSON PATH),
               @archived_by;
        
        -- Archive exam results
        INSERT INTO archives (archive_type, academic_year, data, archived_by)
        SELECT 'Exam', @academic_year,
               (SELECT * FROM exam_results er
                INNER JOIN exams e ON er.exam_id = e.exam_id
                WHERE e.academic_year = @academic_year
                FOR JSON PATH),
               @archived_by;
        
        -- Archive fee transactions
        INSERT INTO archives (archive_type, academic_year, data, archived_by)
        SELECT 'Fee', @academic_year,
               (SELECT * FROM fee_transactions ft
                INNER JOIN fee_accounts fa ON ft.fee_account_id = fa.fee_account_id
                WHERE fa.academic_year = @academic_year
                FOR JSON PATH),
               @archived_by;
        
        -- Log the action
        INSERT INTO audit_logs (user_id, action, table_name, record_id, new_values)
        VALUES (@archived_by, 'ARCHIVE_SESSION', 'archives', @academic_year, 
                'Archived data for year ' + CAST(@academic_year AS NVARCHAR));
        
        COMMIT TRANSACTION;
        SELECT 'Success' AS Result, 'Session archived successfully' AS Message;
    END TRY
    BEGIN CATCH
        IF @@TRANCOUNT > 0
            ROLLBACK TRANSACTION;
        
        SELECT 'Error' AS Result, ERROR_MESSAGE() AS Message;
    END CATCH
END
GO

PRINT 'All stored procedures created successfully';
GO

