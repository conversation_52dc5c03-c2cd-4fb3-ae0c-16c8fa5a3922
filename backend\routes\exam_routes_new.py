"""
Exam Routes - Main Router
Combines all exam-related route modules
"""
from fastapi import APIRouter
from .exam_management_routes import router as management_router
from .exam_marks_routes import router as marks_router

# Create main router
router = APIRouter()

# Include all sub-routers
router.include_router(management_router, tags=["Exam Management"])
router.include_router(marks_router, tags=["Exam Marks"])

# TODO: Add remaining routes from original exam_routes.py:
# - Exam Reports & Analytics routes (report cards, performance analysis)
# - Exam Results retrieval routes
# - Exam Subjects management routes
# - Student promotion routes
# - Top performers routes
#
# These will be added in separate route files to keep modules focused and manageable.
