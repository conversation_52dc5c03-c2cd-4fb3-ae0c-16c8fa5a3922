"""
Teacher Attendance Routes
Handles teacher attendance marking and tracking
"""
from fastapi import APIRouter, Depends, HTTPException, status, Query
from typing import List, Optional
from datetime import date
from models import (TeacherAttendanceCreate, TeacherAttendanceResponse, 
                    AbsentTeacherWithPeriodsResponse)
from auth import get_current_user, require_admin
from services.teacher_service import TeacherService
import logging

logger = logging.getLogger(__name__)
router = APIRouter()


@router.post("/attendance/mark", status_code=status.HTTP_201_CREATED)
async def mark_teacher_attendance(
    attendance: TeacherAttendanceCreate,
    current_user: dict = Depends(require_admin)
):
    """
    Mark teacher attendance (Admin only)
    
    Args:
        attendance: Teacher attendance data
        current_user: Current authenticated user
    
    Returns:
        Success response
    """
    try:
        result = TeacherService.mark_teacher_attendance(attendance, current_user['user_id'])
        
        if result and result.get('Result') == 'Success':
            return {"message": result.get('Message', 'Teacher attendance marked successfully')}
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=result.get('Message', 'Failed to mark teacher attendance')
            )
    except Exception as e:
        logger.error(f"Error marking teacher attendance: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error during teacher attendance marking"
        )


@router.get("/attendance", response_model=List[TeacherAttendanceResponse])
async def get_teacher_attendance(
    attendance_date: Optional[date] = None,
    teacher_id: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Get teacher attendance records with optional filtering
    
    Args:
        attendance_date: Optional date filter
        teacher_id: Optional teacher ID filter
        current_user: Current authenticated user
    
    Returns:
        List of teacher attendance records
    """
    try:
        result = TeacherService.get_teacher_attendance(attendance_date, teacher_id)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching teacher attendance: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching teacher attendance"
        )


@router.get("/attendance/absent-with-periods", response_model=List[AbsentTeacherWithPeriodsResponse])
async def get_absent_teachers_with_periods(
    attendance_date: date,
    current_user: dict = Depends(get_current_user)
):
    """
    Get absent teachers along with their scheduled periods for a specific date
    
    Args:
        attendance_date: Date to check for absent teachers
        current_user: Current authenticated user
    
    Returns:
        List of absent teachers with their scheduled periods
    """
    try:
        result = TeacherService.get_absent_teachers_with_periods(attendance_date)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching absent teachers with periods: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching absent teachers"
        )


@router.get("/attendance/summary")
async def get_teacher_attendance_summary(
    teacher_id: Optional[int] = None,
    month: Optional[int] = None,
    year: Optional[int] = None,
    current_user: dict = Depends(get_current_user)
):
    """
    Get teacher attendance summary with optional filtering
    
    Args:
        teacher_id: Optional teacher ID filter
        month: Optional month filter (1-12)
        year: Optional year filter
        current_user: Current authenticated user
    
    Returns:
        Teacher attendance summary data
    """
    try:
        result = TeacherService.get_teacher_attendance_summary(teacher_id, month, year)
        return result if result else {}
    except Exception as e:
        logger.error(f"Error fetching teacher attendance summary: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching attendance summary"
        )


@router.get("/attendance/date-range")
async def get_teacher_attendance_date_range(
    start_date: date,
    end_date: date,
    teacher_id: Optional[int] = Query(None),
    current_user: dict = Depends(get_current_user)
):
    """
    Get teacher attendance for a date range
    
    Args:
        start_date: Start date for the range
        end_date: End date for the range
        teacher_id: Optional teacher ID filter
        current_user: Current authenticated user
    
    Returns:
        Teacher attendance data for the specified date range
    """
    try:
        result = TeacherService.get_teacher_attendance_date_range(start_date, end_date, teacher_id)
        return result if result else []
    except Exception as e:
        logger.error(f"Error fetching teacher attendance date range: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal server error while fetching attendance date range"
        )
