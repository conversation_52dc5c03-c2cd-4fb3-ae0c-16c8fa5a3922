/**
 * Module <PERSON><PERSON> - Dynamically loads all required JS modules
 */

(function(){
    const scripts = [
        '/static/js/core.module.js',
        '/static/js/api.js',
        '/static/js/auth.js',
        '/static/js/router.js',
        '/static/js/dashboard.js',
        '/static/js/users.js',
        '/static/js/classes.js',
        '/static/js/students.module.js',
        '/static/js/attendance.module.js',
        '/static/js/fees.module.js',
        '/static/js/exams.js',
        '/static/js/reports.js',
        '/static/js/subjects.js',
        '/static/js/staff.js',
        '/static/js/teacher_pages.js',
        '/static/js/timetable.js',
        '/static/js/timetable_grid.js',
        '/static/js/holidays.js',
        '/static/js/settings.js',
        '/static/js/student-modals.js',
        '/static/js/attendance-functions.js',
        '/static/js/attendance-functions-2.js',
        '/static/js/attendance-functions-3.js',
        '/static/js/attendance-functions-4.js',
        '/static/js/fee-functions.js',
        '/static/js/report-functions.js',
        '/static/js/subject-functions.js',
        '/static/js/staff-functions.js',
        '/static/js/holiday-functions.js',
        '/static/js/teacher-functions.js',
        '/static/js/monthly-overview.js',
        '/static/js/sms-namespace.js'
    ];

    function loadNext(i){
        if (i >= scripts.length) {
            console.log('[MODULE-LOADER] All modules loaded successfully');
            return;
        }
        const s = document.createElement('script');
        s.src = scripts[i];
        s.defer = true;
        s.onload = () => {
            console.log('[MODULE-LOADER] Loaded:', scripts[i]);
            loadNext(i+1);
        };
        s.onerror = () => { 
            console.error('[MODULE-LOADER] Failed to load:', scripts[i]); 
            loadNext(i+1); 
        };
        document.head.appendChild(s);
    }

    console.log('[MODULE-LOADER] Starting to load modules...');
    loadNext(0);
})();
